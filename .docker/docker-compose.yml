version: '3.8'

# Settings and configurations that are common for all containers
x-minio-common: &minio-common
  image: quay.io/minio/minio:RELEASE.2023-11-01T01-57-10Z
  command: server --console-address ":9001" http://minio1/data{1...2}
  expose:
    - "9000"
    - "9001"
  ports:
    - "9000:9000"
    - "9001:9001"
  environment:
    MINIO_ROOT_USER: minioadmin
    MINIO_ROOT_PASSWORD: minioadmin
  healthcheck:
    test: [ "CMD", "curl", "-f", "http://localhost:9000/minio/health/live" ]
    interval: 30s
    timeout: 20s
    retries: 3

networks:
  escooter:
services:
  timescaledb:
    image: timescale/timescaledb:latest-pg14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5433:5432"
    restart: on-failure
    volumes:
      - ./PostgresLocalVol:/home/<USER>
      - ./PostgresInit/init.sql:/docker-entrypoint-initdb.d/init.sql
      - timescale-data:/home/<USER>/pgdata/data
    networks:
      escooter:

  minio1:
    <<: *minio-common
    hostname: minio1
    volumes:
      - data1-1:/data1
      - data1-2:/data2

volumes:
  timescale-data:
  data1-1:
  data1-2: