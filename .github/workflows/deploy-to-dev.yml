name: Deploy Build To DEV

on:
  repository_dispatch:
    types: [ deploy-to-dev ]
  workflow_dispatch:  

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      BRANCH_NAME: ${{ github.event.client_payload.branch || github.ref_name }}

    steps:
      - name: Checkout
        uses: actions/checkout@v1
        with:
          ref: ${{ github.event.client_payload.branch }}

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: 17

      - name: Build and deploy jar
        env:
          FLINK_ENDPOINT: ${{ secrets.DEV_FLINK_ENDPOINT }}
          FLINK_PASSWORD: ${{ secrets.DEV_FLINK_PASSWORD }}
          FLINK_USERNAME: ${{ secrets.DEV_FLINK_USERNAME }}
        run: |
          ./gradlew clean && ./gradlew shadowJar  
          FILENAME=$(find build/libs -name "data-cleaning*.jar")
          curl -X POST -H "Expect:" -F "jarfile=@$FILENAME" $FLINK_ENDPOINT -u "$FLINK_USERNAME:$FLINK_PASSWORD"
