## Following are the table that need to be created for storing the actual and transformed data.

create table throttle_percentage (imei BIGINT, ts BIGINT, ingestion_time BIGINT, correlation_id VARCHAR, window_start
BIGINT, window_end BIGINT, throttle_percentage_actual INT, throttle_percentage_transformed FLOAT);

create table motor_speed (imei BIGINT, ts BIGINT, ingestion_time BIGINT, correlation_id VARCHAR, window_start
BIGINT, window_end BIGINT, motor_speed_actual INT, motor_speed_transformed FLOAT);

create table dc_current (imei BIGINT, ts BIGINT, ingestion_time BIGINT, correlation_id VARCHAR, window_start
BIGINT, window_end BIGINT, dc_current_actual FLOAT, dc_current_transformed FLOAT);

create table location_speed (imei BIGINT, ts BIGINT, ingestion_time BIGINT, correlation_id VARCHAR, window_start
BIGINT, window_end BIGINT, location_actual FLOAT, location_transformed FLOAT);

create table location_speed (imei BIGINT, ts BIGINT, ingestion_time BIGINT, correlation_id VARCHAR, window_start
BIGINT, window_end BIGINT, location_actual FLOAT, location_transformed FLOAT);

create table io_voltage_input (imei BIGINT, ts BIGINT, ingestion_time BIGINT, correlation_id VARCHAR, window_start
BIGINT, window_end BIGINT, voltage_input_actual FLOAT, voltage_input_transformed FLOAT);

create table io_temperature (imei BIGINT, ts BIGINT, ingestion_time BIGINT, correlation_id VARCHAR, window_start
BIGINT, window_end BIGINT, temperature_corrected_actual FLOAT, temperature_corrected_transformed FLOAT);

## Catalog Creation.

create table catalog_test (imei, BIGINT, correlation_id VARCHAR);

## Writing cleaned and uncleaned data to timescale db:

The following tables have to be created for data to be written to timescale db.

- To create the `analog_input_initial` table execute:

```text
create table analog_input_initial (imei BIGINT, ts BIGINT, correlation_id VARCHAR, temperature INT, voltage_input INT);
```

- To create the `analog_input_final` table execute:

```text
create table analog_input_final (imei BIGINT, ts BIGINT, correlation_id VARCHAR, temperature INT, voltage_input INT);
```

## To run this job via flink cli in a local cluster:

This will run the job in detached mode.

```bash
./bin/flink run -d -m localhost:8081 ./data-cleaning/build/libs/data-cleaning-0.0.1-SNAPSHOT-all.jar
```

## To run this job via flink cli in a remote cluster:

```bash
./bin/flink run -d -m cluster_ip:port ./data-cleaning/build/libs/data-cleaning-0.0.1-SNAPSHOT-all.jar
```

## To list all the job:

```bash
./bin/flink list 
```

## To cancel a job:

```bash
./bin/flink cancel <job_id>
```