plugins {
    id 'java'
    id 'application'
    id 'org.springframework.boot' version '2.7.0'
    id 'io.spring.dependency-management' version '1.1.0'
    id 'com.github.johnrengelman.shadow' version '7.1.2'
}

ext {
    javaVersion = '11'
    flinkVersion = '1.17.1'
    scalaBinaryVersion = '_2.12'
    slf4jVersion = '1.7.36'
    log4jVersion = '2.17.1'
    rocksDbStateBackendVersion = '1.17.1'
}

def getAppVersion = { ->
    def stdout = new ByteArrayOutputStream()
    exec {
        commandLine 'git', 'rev-parse', '--short', 'HEAD'
        standardOutput = stdout
    }
    def commitId = stdout.toString().replace("\n", "").replace("\r", "").trim()
    stdout = new ByteArrayOutputStream()
    exec {
        commandLine 'git', 'tag', '--points-at', commitId
        standardOutput = stdout
    }
    def tagName = stdout.toString().replace("\n", "").replace("\r", "").trim()
    def versionName = 'git-' + commitId
    if (tagName != null && "" != tagName) {
        versionName = tagName
    }
    return versionName
}

group = 'com.nichesolv.nds.datacleaning'
version = getAppVersion()
sourceCompatibility = "11"
targetCompatibility = "11"

mainClassName = 'com.nichesolv.nds.datacleaning.DataCleaningApplication'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }

    flinkShadowJar // dependencies which go into the shadowJar

    // always exclude these (also from transitive dependencies) since they are provided by Flink
    flinkShadowJar.exclude group: 'org.apache.flink', module: 'force-shading'
    flinkShadowJar.exclude group: 'com.google.code.findbugs', module: 'jsr305'
    flinkShadowJar.exclude group: 'org.slf4j'
    flinkShadowJar.exclude group: 'org.apache.logging.log4j'

    configureEach {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
        exclude group: 'ch.qos.logback', module: 'logback-classic'
        exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
    }
}

repositories {
    mavenCentral()
    maven {
        url "https://repository.apache.org/content/repositories/snapshots"
        mavenContent {
            snapshotsOnly()
        }
    }
}

dependencies {
    // https://mvnrepository.com/artifact/com.google.guava/guava
    implementation 'com.google.guava:guava:32.1.3-jre'


    // https://mvnrepository.com/artifact/org.apache.flink/flink-connector-jdbc
    implementation group: 'org.apache.flink', name: 'flink-connector-jdbc', version: '3.1.0-1.17'

    // https://mvnrepository.com/artifact/org.postgresql/postgresql
    implementation group: 'org.postgresql', name: 'postgresql', version: '42.6.0'

    implementation 'org.springframework.boot:spring-boot-starter'

    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'

    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.2'

    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.15.2'

    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2'

    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    // testImplementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    // Use JUnit Jupiter for testing.
    testImplementation 'org.junit.jupiter:junit-jupiter:5.9.1'

    compileOnly 'org.projectlombok:lombok:1.18.28'
    annotationProcessor 'org.projectlombok:lombok:1.18.28'

    testCompileOnly 'org.projectlombok:lombok:1.18.28'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.28'

    // --------------------------------------------------------------
    // Compile-time dependencies that should NOT be part of the
    // shadow jar and are provided in the lib folder of Flink
    // --------------------------------------------------------------
    implementation "org.apache.flink:flink-streaming-java:${flinkVersion}"

    implementation "org.apache.flink:flink-clients:${flinkVersion}"

    // https://mvnrepository.com/artifact/org.apache.flink/flink-runtime-web
    implementation group: 'org.apache.flink', name: 'flink-runtime-web', version: '1.17.1'

    // --------------------------------------------------------------
    // Dependencies that should be part of the shadow jar, e.g.
    // connectors. These must be in the flinkShadowJar configuration!
    // --------------------------------------------------------------
    runtimeOnly "org.apache.logging.log4j:log4j-api:${log4jVersion}"
    runtimeOnly "org.apache.logging.log4j:log4j-core:${log4jVersion}"

    // https://mvnrepository.com/artifact/org.apache.flink/flink-connector-rabbitmq
    implementation 'org.apache.flink:flink-connector-rabbitmq:3.0.1-1.17'

    // https://mvnrepository.com/artifact/org.apache.flink/flink-statebackend-rocksdb
    compileOnly "org.apache.flink:flink-statebackend-rocksdb:${rocksDbStateBackendVersion}"

    // https://mvnrepository.com/artifact/org.apache.flink/flink-table-api-java-bridge
    compileOnly 'org.apache.flink:flink-table-api-java-bridge:1.17.1'

    implementation 'com.google.protobuf:protobuf-java:3.24.0'

    // https://mvnrepository.com/artifact/org.apache.flink/flink-csv
    implementation group: 'org.apache.flink', name: 'flink-csv', version: '1.17.1'

    implementation(platform("io.opentelemetry:opentelemetry-bom:1.31.0"))

    implementation("io.opentelemetry:opentelemetry-api")

    testImplementation "org.apache.flink:flink-test-utils:1.18-SNAPSHOT"

    testImplementation("org.assertj:assertj-core:3.24.2")

    implementation 'org.springframework.boot:spring-boot-starter'


    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'


    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.2'


    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.15.2'


    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2'


    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
}

tasks.named('test') {
    useJUnitPlatform()
}

// make compileOnly dependencies available for tests:
sourceSets {
    main.compileClasspath += configurations.flinkShadowJar
    main.runtimeClasspath += configurations.flinkShadowJar
    test.compileClasspath += configurations.flinkShadowJar
    test.runtimeClasspath += configurations.flinkShadowJar
    javadoc.classpath += configurations.flinkShadowJar
}

run.classpath = sourceSets.main.runtimeClasspath

jar {
    manifest {
        attributes 'Built-By': System.getProperty('user.name'),
                'Build-Jdk': System.getProperty('java.version'),
                "Main-Class": "com.nichesolv.nds.datacleaning.DataCleaningApplication"
    }
}


import com.github.jengelman.gradle.plugins.shadow.transformers.PropertiesFileTransformer

shadowJar {
    mergeServiceFiles()
    append 'META-INF/spring.handlers'
    append 'META-INF/spring.schemas'
    append 'META-INF/spring.tooling'
    transform(PropertiesFileTransformer) {
        paths = ['META-INF/spring.factories']
        mergeStrategy = "append"
    }
    configurations = [project.configurations.flinkShadowJar, project.configurations.productionRuntimeClasspath]
}

application {
    applicationDefaultJvmArgs = ["--add-opens", "java.base/java.lang=ALL-UNNAMED", "--add-opens", "java.base/java.lang.invoke=ALL-UNNAMED", "--add-opens", "java.base/java.util=ALL-UNNAMED", "--add-opens", "java.base/java.lang.reflect=ALL-UNNAMED", "--add-opens", "java.base/java.nio=ALL-UNNAMED"]
    mainClassName = 'com.nichesolv.nds.datacleaning.DataCleaningApplication'
}


// To implement a custom task, extend the DefaultTask.
abstract class GreetingTask extends DefaultTask {
    // Let's add a property to the task, so we can customize it.
    @Input
    abstract Property<String> getGreeting()

    GreetingTask() {
        greeting.convention("hello from GreetingTask")
    }

    // Add some behaviour. To do so, add a method and mark it with TaskAction annotation.
    @TaskAction
    def greet() {
        println greeting.get()
    }
}

// Create a task using the task type.
tasks.register("hello", GreetingTask)

// Customize the greeting
tasks.register('greeting', GreetingTask) {
    greeting = 'greeting from GreetingTask'
}