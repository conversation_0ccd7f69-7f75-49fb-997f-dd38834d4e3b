package com.nichesolv.nds.datacleaning;

import com.nichesolv.nds.datacleaning.function.PrimaryDataStreamHandler;
import com.nichesolv.nds.datacleaning.function.serde.mc.MotorControllerDataDeserializer;
import com.nichesolv.nds.datacleaning.function.serde.mc.MotorControllerMergedDeserializer;
import com.nichesolv.nds.datacleaning.function.serde.mc.MotorControllerStatusDeserializer;
import com.nichesolv.nds.datacleaning.function.serde.telemetry.*;
import com.nichesolv.nds.datacleaning.function.source.AbstractSource;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BatteryStatus;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.BatteryCellTemperature;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltage;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerData;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerMerged;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerStatus;
import com.nichesolv.nds.model.core.ajjas.event.location.Location;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.domain.Telemetry;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.apache.flink.streaming.connectors.rabbitmq.RMQSource;
import org.apache.flink.streaming.connectors.rabbitmq.common.RMQConnectionConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class DataCleaningApplication implements CommandLineRunner {

  private static final Logger LOG = LoggerFactory.getLogger(DataCleaningApplication.class);

  @Value("${spring.profiles.active}")
  private String profile;

  @Value("${appConfig.streaming.parallelism}")
  private Integer parallelism;

  @Value("${appConfig.streaming.checkpointing.interval}")
  private Integer checkpointingInterval;

  @Value("${appConfig.streaming.jobName}")
  private String jobName;

  @Value("${appConfig.streaming.checkpointDir}")
  private String checkpointDir;

  @Value("${appConfig.deployWithZipArchiveEnabled}")
  private Boolean isDeployWithZipArchiveEnabled;

  @Value("${appConfig.version}")
  private String version;

  @Value("${appConfig.autoWatermarkInterval}")
  private Integer autoWatermarkInterval;

  @Value("${appConfig.enableRestPersistence}")
  private Boolean enableRestPersistence;

  @Value("${appConfig.enableDbPersistence}")
  private Boolean enableDbPersistence;

  @Autowired private RMQConnectionConfig rmqConnectionConfig;

  @Autowired
  @Qualifier("batteryStatusSource")
  private AbstractSource<BatteryStatus> batterySource;

  @Autowired
  @Qualifier("batteryTemperatureSource")
  private AbstractSource<BatteryCellTemperature> batteryTemperatureSource;

  @Autowired
  @Qualifier("batteryVoltageSource")
  private AbstractSource<BatteryCellVoltage> batteryVoltageSource;

  @Autowired
  @Qualifier("batteryMetadataSource")
  private AbstractSource<com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata>
      batteryMetadataSource;

  @Autowired private BeanFactory beanFactory;

  public static void main(String[] args) {
    SpringApplication.run(DataCleaningApplication.class, args);
  }

  @Override
  public void run(String... args) throws Exception {

    // Accelerometer source.
    final RichSourceFunction<Tuple3<Timestamp, Metadata, Telemetry>> telemetrySource =
        new RMQSource<>(
            rmqConnectionConfig, "event.parsed.telemetry", false, new TelemetryDeserializer());

    final RichSourceFunction<Tuple3<Timestamp, Metadata, Telemetry>> imuSource =
        new RMQSource<>(rmqConnectionConfig, "event.parsed.imu", false, new ImuDeserializer());

    // Location source.
    final RichSourceFunction<Tuple3<Timestamp, Metadata, Location>> locationSource =
        new RMQSource<>(
            rmqConnectionConfig, "event.parsed.location", false, new LocationProtocDeserializer());

    // Motor Data
    final RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerData>>
        motorControllerDataSource =
            new RMQSource<>(
                rmqConnectionConfig,
                "event.parsed.mcu.data",
                false,
                new MotorControllerDataDeserializer());

    // Motor Data
    final RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerStatus>>
        motorControllerStatusSource =
            new RMQSource<>(
                rmqConnectionConfig,
                "event.parsed.mcu.status",
                false,
                new MotorControllerStatusDeserializer());

    final RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerMerged>>
        motorControllerMergedSource =
            new RMQSource<>(
                rmqConnectionConfig,
                "event.parsed.mcu",
                false,
                new MotorControllerMergedDeserializer());

    // BMS Status
    final RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryStatus>> bmsStatusSource =
        this.batterySource.getSource();

    // BMS Temperature
    final RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryCellTemperature>>
        bmsTemperatureSource = this.batteryTemperatureSource.getSource();

    // BMS Metadata
    final RichSourceFunction<
            Tuple3<
                Timestamp,
                Metadata,
                com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata>>
        bmsMetadataSource = this.batteryMetadataSource.getSource();

    // BMS Voltage
    final RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryCellVoltage>> bmsVoltageSource =
        this.batteryVoltageSource.getSource();

    this.execute(
        telemetrySource,
        locationSource,
        motorControllerDataSource,
        motorControllerStatusSource,
        bmsStatusSource,
        bmsTemperatureSource,
        bmsMetadataSource,
        bmsVoltageSource,
        imuSource,
        motorControllerMergedSource);
  }

  private void execute(
      RichSourceFunction<Tuple3<Timestamp, Metadata, Telemetry>> telemetrySource,
      RichSourceFunction<Tuple3<Timestamp, Metadata, Location>> locationSource,
      RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerData>>
          motorControllerDataSource,
      RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerStatus>>
          motorControllerStatusSource,
      RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryStatus>> bmsStatusSource,
      RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryCellTemperature>> bmsTemperatureSource,
      RichSourceFunction<
              Tuple3<
                  Timestamp,
                  Metadata,
                  com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata>>
          bmsMetadataSource,
      RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryCellVoltage>> bmsVoltageSource,
      RichSourceFunction<Tuple3<Timestamp, Metadata, Telemetry>> imuSource,
      RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerMerged>> mcuMergedSource)
      throws Exception {

    if (version == null || version.isBlank() || version.isEmpty()) {
      throw new RuntimeException("Version cannot be empty.");
    }

    if (this.enableRestPersistence && this.enableDbPersistence) {
      throw new RuntimeException("Cannot enable both rest and db persistence.");
    }

    // We are not supporting loading venv from python archive.
    if (isDeployWithZipArchiveEnabled) {
      throw new UnsupportedOperationException(
          "Loading from python archive is not yet supported. "
              + "Disable `deployWithZipArchiveEnabled` and try again.");
    }

    try (final StreamExecutionEnvironment env =
        StreamExecutionEnvironment.getExecutionEnvironment(new Configuration())) {

      if (LOG.isInfoEnabled()) LOG.info("Profile: " + profile);

      // Register the application configuration globally.
      Map<String, String> globalApplicationMap = new HashMap<>();
      globalApplicationMap.put("profile", profile);
      globalApplicationMap.put("version", version);
      ParameterTool parameterTool = ParameterTool.fromMap(globalApplicationMap);
      env.getConfig().setGlobalJobParameters(parameterTool);

      // enable checkpointing. This is ensures redundancy.
      // We enable checkpointing regardless of the profile.
      env.enableCheckpointing(checkpointingInterval);
      // Support for unaligned checkpoints.
      env.getCheckpointConfig().enableUnalignedCheckpoints();
      // If the checkpoint does not complete in 30 seconds, it's considered unaligned.
      env.getCheckpointConfig().setAlignedCheckpointTimeout(Duration.ofSeconds(30));

      if (!"dev1".equals(profile) && !"preprod".equals(profile) && !"prod".equals(profile)) {
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        // checkpoint storage. This has to be done otherwise flink will use
        // JobManagerCheckpointStorage which is very limited in the amount of storage that it
        // provides.
        // If not set properly this can cause jobs with large state to fail.
        env.getCheckpointConfig().setCheckpointStorage(checkpointDir);
        // Other checkpoint specific configurations.
        if (LOG.isInfoEnabled()) LOG.info("Actual checkpoint interval: {}", checkpointingInterval);
        env.getCheckpointConfig().setCheckpointInterval(checkpointingInterval);
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(10);
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(5000);
      }

      if (LOG.isInfoEnabled())
        LOG.info("Configured checkpoint interval: {}", env.getCheckpointInterval());

      if (LOG.isInfoEnabled()) LOG.info("Job parallelism: {}", parallelism);
      // set parallelism.
      env.setParallelism(parallelism);

      // Auto watermark generator.
      // Generates a watermark every 1 second. This will prevent events from getting stuck in
      // windows.
      if (LOG.isInfoEnabled()) LOG.info("Auto watermark interval: {}", autoWatermarkInterval);

      env.getConfig().setAutoWatermarkInterval(autoWatermarkInterval);

      PrimaryDataStreamHandler primaryDataStreamHandler =
          beanFactory.getBean(
              PrimaryDataStreamHandler.class,
              telemetrySource,
              locationSource,
              motorControllerDataSource,
              motorControllerStatusSource,
              bmsStatusSource,
              bmsTemperatureSource,
              bmsMetadataSource,
              bmsVoltageSource,
              imuSource,
              mcuMergedSource);
      primaryDataStreamHandler.process(env);
      env.execute(jobName);
    }
  }
}
