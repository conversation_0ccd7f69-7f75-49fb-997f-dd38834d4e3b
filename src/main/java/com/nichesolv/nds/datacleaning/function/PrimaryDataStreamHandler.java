package com.nichesolv.nds.datacleaning.function;

import static org.apache.flink.table.api.Expressions.*;

import com.nichesolv.nds.datacleaning.function.component.CatalogRegistrationHandler;
import com.nichesolv.nds.datacleaning.function.component.LookupCacheRegistrationHandler;
import com.nichesolv.nds.datacleaning.function.component.ThirdPartyUdfLoader;
import com.nichesolv.nds.datacleaning.function.component.timeattribute.SimpleWatermarkStrategy;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.datacleaning.function.transformer.accelerometer.AccelerometerRawTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.accelerometer.AccelerometerTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.bms.*;
import com.nichesolv.nds.datacleaning.function.transformer.gravitationalVector.GravitationalVectorAndLeanAngleTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.gravitationalVector.GravitationalVectorRawTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.gyroscope.GyroscopeTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.io.AnalogInputTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.io.DigitalInputTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.io.DigitalOutputTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.location.LocationDataTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.motorcontroller.MotorControllerMergedTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.motorcontroller.data.MotorControllerDataTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.motorcontroller.status.MotorControllerStatusTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.motorcontroller.status.MotorFaultsTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.telemetry.ImuRawTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.telemetry.ImuTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.telemetry.TelemetryRawTransformer;
import com.nichesolv.nds.datacleaning.function.transformer.telemetry.TelemetryTransformer;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BatteryStatus;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.BatteryCellTemperature;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltage;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerData;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerMerged;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerStatus;
import com.nichesolv.nds.model.core.ajjas.event.location.Location;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.domain.Telemetry;
import java.io.Serializable;
import java.time.Duration;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableConfig;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/** This is the primary MotorControllerData stream handler class. */
@Component
@Scope("prototype")
public class PrimaryDataStreamHandler implements Serializable {

  private static final Logger LOG = LoggerFactory.getLogger(PrimaryDataStreamHandler.class);

  private static final long serialVersionUID = -251544285415174360L;

  @Value("${appConfig.source.rmq.accelerometer.uid:AccelerometerSource}")
  private String accelerometerUid;

  @Value("${appConfig.source.rmq.accelerometer.parallelism:1}")
  private Integer accelerometerParallelism;

  @Value("${appConfig.source.rmq.io.uid:IoSource}")
  private String ioUid;

  @Value("${appConfig.source.rmq.io.parallelism:1}")
  private Integer ioParallelism;

  @Value("${appConfig.source.rmq.location.uid:LocationSource}")
  private String locationUid;

  @Value("${appConfig.source.rmq.location.parallelism:1}")
  private Integer locationParallelism;

  @Value("${appConfig.source.rmq.motorController.uid:MotorController}")
  private String motorControllerUid;

  @Value("${appConfig.source.rmq.motorController.parallelism:1}")
  private Integer motorControllerParallelism;

  @Value("${appConfig.persistence.catalog.enable}")
  private Boolean enableCatalog;

  @Value("${appConfig.debug}")
  private Boolean debug;

  // ------------------------------TRANSFORMERS--------------------------------------

  @Autowired private TelemetryRawTransformer telemetryRawTransformer;

  @Autowired private TelemetryTransformer telemetryTransformer;

  @Autowired private AnalogInputTransformer analogInputTransformer;

  @Autowired private LocationDataTransformer locationDataTransformer;

  @Autowired private DigitalInputTransformer digitalInputTransformer;

  @Autowired private DigitalOutputTransformer digitalOutputTransformer;

  @Autowired private AccelerometerTransformer accelerometerTransformer;

  @Autowired private AccelerometerRawTransformer accelerometerRawTransformer;

  @Autowired private BatteryStatusTransformer batteryStatusTransformer;

  @Autowired private MotorControllerDataTransformer motorControllerDataTransformer;

  @Autowired private MotorControllerStatusTransformer motorControllerStatusTransformer;

  @Autowired private MotorFaultsTransformer motorFaultsTransformer;

  @Autowired private BatteryCellTemperatureTransformer batteryCellTemperatureTransformer;

  @Autowired private BatteryAlarmsAndProtectionsTransformer batteryAlarmsAndProtectionsTransformer;

  @Autowired private BatteryCellVoltageTransformer batteryCellVoltageTransformer;

  @Autowired private GyroscopeTransformer gyroscopeTransformer;

  @Autowired private GravitationalVectorRawTransformer gravitationalVectorRawTransformer;

  @Autowired
  private GravitationalVectorAndLeanAngleTransformer gravitationalVectorAndLeanAngleTransformer;

  // --------------------------------------------------------------------

  @Autowired private ThirdPartyUdfLoader thirdPartyUdfLoader;

  @Autowired private CatalogRegistrationHandler catalogRegistrationHandler;

  @Autowired private LookupCacheRegistrationHandler lookupCacheRegistrationHandler;

  @Autowired private ImuRawTransformer imuRawTransformer;

  @Autowired private ImuTransformer imuTransformer;

  // --------------- NEW ------------------------

  @Autowired
  @Qualifier("telemetryWriterV2")
  private AbstractWriter<Row> telemetryWriter;

  @Autowired
  @Qualifier("motorControllerStatusWriter")
  private AbstractWriter<Row> motorControllerStatusWriter;

  @Autowired
  @Qualifier("motorControllerDataWriter")
  private AbstractWriter<Row> motorControllerDataWriter;

  @Autowired
  @Qualifier("alarmsAndProtectionsWriter")
  private AbstractWriter<Row> alarmsAndProtectionsWriter;

  @Autowired
  @Qualifier("motorFaultsWriter")
  private AbstractWriter<Row> motorFaultsWriter;

  @Autowired
  @Qualifier("locationWriterV2")
  private AbstractWriter<Row> locationWriter;

  @Autowired
  @Qualifier("accelerometerWriterV2")
  private AbstractWriter<Row> accelerometerWriter;

  @Autowired
  @Qualifier("batteryStatusWriter")
  private AbstractWriter<Row> batteryStatusWriter;

  @Autowired
  @Qualifier("batteryTemperatureWriter")
  private AbstractWriter<Row> batteryTemperatureWriter;

  @Autowired
  @Qualifier("cellVoltagesWriter")
  private AbstractWriter<Row> batteryCellVoltagesWriter;

  @Autowired
  @Qualifier("gyroscopeWriterV2")
  private AbstractWriter<Row> gyroscopeWriter;

  @Autowired
  @Qualifier("gravitationalVectorWriterV2")
  private AbstractWriter<Row> gravitationalVectorWriter;

  @Autowired
  @Qualifier("analogInputWriterV2")
  private AbstractWriter<Row> analogInputWriter;

  @Autowired
  @Qualifier("digitalInputWriterV2")
  private AbstractWriter<Row> digitalInputWriter;

  @Autowired
  @Qualifier("digitalOutputWriterV2")
  private AbstractWriter<Row> digitalOutputWriter;

  @Autowired
  @Qualifier("imuWriterV2")
  private AbstractWriter<Row> imuWriter;

  //  @Autowired
  //  @Qualifier("batteryStatusTempMaxAndMinWriter")
  //  private AbstractWriter<Row> batteryStatusTempMaxAndMinWriter;

  @Autowired private RedisConnectionManager redisConnectionManager;

  // -------------------------------------------------

  private final RichSourceFunction<Tuple3<Timestamp, Metadata, Telemetry>> telemetry;

  private final RichSourceFunction<Tuple3<Timestamp, Metadata, Location>> location;

  private final RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerData>>
      motorControllerData;

  private final RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerStatus>>
      motorControllerStatus;

  private final RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryStatus>> bmsStatus;

  private final RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryCellTemperature>>
      bmsTemperature;

  private final RichSourceFunction<
          Tuple3<
              Timestamp,
              Metadata,
              com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata>>
      bmsMetadata;

  private final RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryCellVoltage>> bmsVoltage;

  private final RichSourceFunction<Tuple3<Timestamp, Metadata, Telemetry>> imuSource;
  private final RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerMerged>>
      mcuMergedSource;

  @Autowired private MotorControllerMergedTransformer motorControllerMergedTransformer;

  @Autowired
  @Qualifier("motorControllerMergedWriter")
  private AbstractWriter<Row> motorControllerMergedWriter;

  public PrimaryDataStreamHandler(
      RichSourceFunction<Tuple3<Timestamp, Metadata, Telemetry>> telemetry,
      RichSourceFunction<Tuple3<Timestamp, Metadata, Location>> location,
      RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerData>> motorControllerData,
      RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerStatus>> motorControllerStatus,
      RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryStatus>> bmsStatus,
      RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryCellTemperature>> bmsTemperature,
      RichSourceFunction<
              Tuple3<
                  Timestamp,
                  Metadata,
                  com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata>>
          bmsMetadata,
      RichSourceFunction<Tuple3<Timestamp, Metadata, BatteryCellVoltage>> bmsVoltage,
      RichSourceFunction<Tuple3<Timestamp, Metadata, Telemetry>> imuSource,
      RichSourceFunction<Tuple3<Timestamp, Metadata, MotorControllerMerged>> mcuMergedSource) {
    this.telemetry = telemetry;
    this.location = location;
    this.motorControllerData = motorControllerData;
    this.motorControllerStatus = motorControllerStatus;
    this.bmsStatus = bmsStatus;
    this.bmsTemperature = bmsTemperature;
    this.bmsMetadata = bmsMetadata;
    this.bmsVoltage = bmsVoltage;
    this.imuSource = imuSource;
    this.mcuMergedSource = mcuMergedSource;
  }

  public void process(StreamExecutionEnvironment env) {

    // Telemetry Stream
    DataStream<Tuple3<Timestamp, Metadata, Telemetry>> telemetryStream =
        env.addSource(this.telemetry)
            .setParallelism(1)
            .name("telemetry_source")
            .uid("telemetry_source")
            .assignTimestampsAndWatermarks(new SimpleWatermarkStrategy<>());

    DataStream<Tuple3<Timestamp, Metadata, Location>> locationStream =
        env.addSource(this.location)
            .setParallelism(1)
            .name("location_source")
            .uid("location_source")
            .assignTimestampsAndWatermarks(new SimpleWatermarkStrategy<>());

    DataStream<Tuple3<Timestamp, Metadata, MotorControllerData>> motorControllerDataStream =
        env.addSource(this.motorControllerData)
            .setParallelism(1)
            .name("motor_controller_data_source")
            .uid("motor_controller_data_source")
            .assignTimestampsAndWatermarks(new SimpleWatermarkStrategy<>());

    DataStream<Tuple3<Timestamp, Metadata, MotorControllerStatus>> motorControllerStatusStream =
        env.addSource(this.motorControllerStatus)
            .setParallelism(1)
            .name("motor_controller_status_source")
            .uid("motor_controller_status_source")
            .assignTimestampsAndWatermarks(new SimpleWatermarkStrategy<>());

    // Battery status stream.
    DataStream<Tuple3<Timestamp, Metadata, BatteryStatus>> bmsStatusStream =
        env.addSource(this.bmsStatus)
            .setParallelism(1)
            .name("bms_status_source")
            .uid("bms_status_source")
            .assignTimestampsAndWatermarks(
                new SimpleWatermarkStrategy<Tuple3<Timestamp, Metadata, BatteryStatus>>()
                    .withIdleness(Duration.ofSeconds(7)));

    // Battery cell temperature stream.
    DataStream<Tuple3<Timestamp, Metadata, BatteryCellTemperature>> bmsTemperatureStream =
        env.addSource(this.bmsTemperature)
            .setParallelism(1)
            .name("bms_temperature_source")
            .uid("bms_temperature_source")
            .assignTimestampsAndWatermarks(new SimpleWatermarkStrategy<>());

    // Battery metadata stream.
    DataStream<
            Tuple3<
                Timestamp,
                Metadata,
                com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata>>
        bmsMetadataStream =
            env.addSource(this.bmsMetadata)
                .setParallelism(1)
                .name("bms_metadata_source")
                .uid("bms_metadata_source")
                .assignTimestampsAndWatermarks(new SimpleWatermarkStrategy<>());

    // Battery voltage stream.
    DataStream<Tuple3<Timestamp, Metadata, BatteryCellVoltage>> bmsVoltageStream =
        env.addSource(this.bmsVoltage)
            .setParallelism(1)
            .name("bms_voltage_source")
            .uid("bms_voltage_source")
            .assignTimestampsAndWatermarks(
                new SimpleWatermarkStrategy<Tuple3<Timestamp, Metadata, BatteryCellVoltage>>()
                    .withIdleness(Duration.ofSeconds(7)));

    DataStream<Tuple3<Timestamp, Metadata, Telemetry>> imuStream =
        env.addSource(this.imuSource)
            .setParallelism(1)
            .name("imu_source")
            .uid("imu_source")
            .assignTimestampsAndWatermarks(new SimpleWatermarkStrategy<>());

    DataStream<Tuple3<Timestamp, Metadata, MotorControllerMerged>> motorControllerMergedStream =
        env.addSource(this.mcuMergedSource)
            .setParallelism(1)
            .name("motor_controller_merged_source")
            .uid("motor_controller_merged_source")
            .assignTimestampsAndWatermarks(new SimpleWatermarkStrategy<>());

    // We are just printing all the streams, this might help in comparing the initial raw values and
    // final transformed values.
    telemetryStream.print("telemetryStreamInitial:>> ").setParallelism(1);
    motorControllerDataStream.print("MotorControllerStreamInitial:>> ").setParallelism(1);
    motorControllerStatusStream.print("MotorControllerStreamInitial:>> ").setParallelism(1);
    bmsStatusStream.print("BatteryStatusInitial:>> ").setParallelism(1);
    bmsTemperatureStream.print("BatteryTemperatureInitial:>> ").setParallelism(1);
    bmsMetadataStream.print("BatteryMetadataInitial:>> ").setParallelism(1);
    bmsVoltageStream.print("BatteryVoltageInitial:>> ").setParallelism(1);
    motorControllerMergedStream.print("MotorControllerMergedStreamInitial:>> ").setParallelism(1);

    // Stream table environment.
    if (LOG.isInfoEnabled()) LOG.info("Creating stream table env");
    StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);
    // Setting the idle watermark timeout.
    TableConfig tableConfig = tableEnv.getConfig();
    final String sourceIdleTimeout = "13 s";
    tableConfig.set("table.exec.source.idle-timeout", sourceIdleTimeout);
    // Enable thread mode. See here for more information:
    // https://flink.apache.org/2022/05/06/exploring-the-thread-mode-in-pyflink/
    // This might offer a performance boot in some scenarios, but since we are using vectorized udf
    // it might not take effect.
    tableEnv.getConfig().set("python.execution-mode", "thread");

    // ====================
    // Python udf loaders.
    // ====================
    if (LOG.isInfoEnabled()) LOG.info("Loading udfs.");
    thirdPartyUdfLoader.load(tableEnv);

    if (enableCatalog) {
      // Register catalogs
      catalogRegistrationHandler.registerCatalog(tableEnv);
    }

    // Lookup cache registration.
    lookupCacheRegistrationHandler.register(tableEnv);

    // Telemetry raw
    Table telemetryRawTable = telemetryRawTransformer.transform(env, tableEnv, telemetryStream);
    telemetryWriter.save(tableEnv.toChangelogStream(telemetryRawTable), false, false, true);

    // Telemetry
    Table telemetryTable = telemetryTransformer.transform(env, tableEnv, telemetryStream);
    telemetryWriter.save(tableEnv.toChangelogStream(telemetryTable), false, false, false);

    // imu raw
    Table imuRawTable = imuRawTransformer.transform(env, tableEnv, telemetryStream);
    imuWriter.save(tableEnv.toChangelogStream(imuRawTable), false, false, true);

    // imu
    Table imuTable = imuTransformer.transform(env, tableEnv, telemetryStream);
    imuWriter.save(tableEnv.toChangelogStream(imuTable), false, false, false);

    // Motor controller data
    Table motorControllerDataTable =
        motorControllerDataTransformer.transform(env, tableEnv, motorControllerDataStream);

    // Select required columns
    motorControllerDataTable =
        motorControllerDataTable.select(
            $("observed_timestamp").as("timestamp"),
            $("ingestion_timestamp").as("ingestion_timestamp"),
            $("correlation_id").as("correlation_id"),
            $("imei").as("imei"),
            $("vehicle_id").as("vehicle_id"),
            $("mfr_org_id").as("mfr_org_id"), // long
            $("owner_org_id").as("owner_org_id"), // long
            $("dc_voltage").as("dc_voltage"),
            $("motor_speed_corrected").as("motor_speed"),
            $("dc_current_corrected").as("dc_current"),
            $("motor_temperature").as("motor_temperature"),
            $("mcs_temperature").as("mcs_temperature"));

    // Motor controller data table print schema
    motorControllerDataTable.printSchema();

    DataStream<Row> mcd = tableEnv.toChangelogStream(motorControllerDataTable);
    // print
    mcd.print("MotorControllerData::>> ").setParallelism(1);

    // Write mcd to db
    this.motorControllerDataWriter.save(mcd, false, false, false);

    // Motor controller status
    Table motorControllerStatusTable =
        motorControllerStatusTransformer.transform(env, tableEnv, motorControllerStatusStream);

    // Select required columns
    motorControllerStatusTable =
        motorControllerStatusTable.select(
            $("observed_timestamp").as("timestamp"),
            $("ingestion_timestamp").as("ingestion_timestamp"),
            $("correlation_id").as("correlation_id"),
            $("imei").as("imei"),
            $("vehicle_id").as("vehicle_id"),
            $("mfr_org_id").as("mfr_org_id"), // long
            $("owner_org_id").as("owner_org_id"), // long
            $("drive_selection").as("drive_selection"),
            $("regeneration").as("regeneration"),
            $("ready_sign").as("ready_sign"),
            $("p_light").as("p_light"),
            $("reverse").as("reverse"),
            $("cruise").as("cruise"),
            $("vehicle_brake").as("vehicle_brake"),
            $("side_stand").as("side_stand"),
            $("throttle_percentage_corrected").as("throttle_percentage"));

    // motor controller status data.
    motorControllerStatusTable.printSchema();

    DataStream<Row> mcs = tableEnv.toChangelogStream(motorControllerStatusTable);
    // print
    mcs.print("MotorControllerStatus::>> ").setParallelism(1);

    // Write motor controller status to db.
    this.motorControllerStatusWriter.save(mcs, false, false, false);

    // Location stream
    Table locationTable = locationDataTransformer.transform(env, tableEnv, locationStream);

    locationTable =
        locationTable.select(
            $("observed_timestamp").as("timestamp"),
            $("ingestion_timestamp").as("ingestion_timestamp"),
            $("correlation_id").as("correlation_id"),
            $("imei").as("imei"),
            $("vehicle_id").as("vehicle_id"),
            $("mfr_org_id").as("mfr_org_id"), // long
            $("owner_org_id").as("owner_org_id"), // long
            $("latitude").as("latitude"),
            $("longitude").as("longitude"),
            $("altitude").as("altitude"),
            $("speed").as("speed"),
            $("bearing").as("bearing"),
            $("pdop").as("pdop"),
            $("hdop").as("hdop"),
            $("vdop").as("vdop"),
            $("view_sats").as("view_sats"),
            $("track_sats").as("track_sats"),
            $("gps_distance").as("gps_distance"));

    locationTable.printSchema();

    DataStream<Row> loc = tableEnv.toChangelogStream(locationTable);
    // print
    loc.print("Location::>> ").setParallelism(1);

    // write location to db
    this.locationWriter.save(loc, false, false, false);

    // ----------------------------------------------------------------------
    // --------------------------- BMS --------------------------------------
    Table batteryStatusTable = batteryStatusTransformer.transform(env, tableEnv, bmsStatusStream);
    // Let's remap the fields.
    batteryStatusTable =
        batteryStatusTable.select(
            $("observed_timestamp").as("timestamp"),
            $("ingestion_timestamp").as("ingestion_timestamp"),
            $("correlation_id").as("correlation_id"),
            $("imei").as("imei"),
            $("vehicle_id").as("vehicle_id"),
            $("mfr_org_id").as("mfr_org_id"), // long
            $("owner_org_id").as("owner_org_id"), // long
            $("battery_volt").as("battery_volt"),
            $("cell_volt_max").as("cell_volt_max"),
            $("cell_volt_min").as("cell_volt_min"),
            $("chg_cycle_count").as("chg_cycle_count"),
            $("current").as("current"),
            $("dsg_cycle_count").as("dsg_cycle_count"),
            $("soc").as("soc"),
            $("soh").as("soh"),
            $("temperature_max").as("temperature_max"),
            $("temperature_min").as("temperature_min"),
            $("remaining_capacity").as("remaining_capacity"),
            $("mosfet_temperature").as("mosfet_temperature"),
            $("discharge").as("discharge"));

    DataStream<Row> batteryStatus = tableEnv.toChangelogStream(batteryStatusTable);
    // print
    batteryStatus.print("BatteryStatus::>> ").setParallelism(1);

    // print schema
    batteryStatusTable.printSchema();

    // Write battery status to the db
    this.batteryStatusWriter.save(batteryStatus, false, false, false);

    // Battery Cell Temperature.
    Table batteryCellTemperatureTable =
        batteryCellTemperatureTransformer.transform(env, tableEnv, bmsTemperatureStream);

    // Remap the fields.
    batteryCellTemperatureTable =
        batteryCellTemperatureTable.select(
            $("observed_timestamp").as("timestamp"),
            $("ingestion_timestamp").as("ingestion_timestamp"),
            $("correlation_id").as("correlation_id"),
            $("imei").as("imei"),
            $("vehicle_id").as("vehicle_id"),
            $("mfr_org_id").as("mfr_org_id"), // long
            $("owner_org_id").as("owner_org_id"), // long
            $("stack_id").as("stack_id"),
            $("temperature").as("temperature"));

    // print schema
    batteryCellTemperatureTable.printSchema();

    // Convert to datastream of rows.
    DataStream<Row> batterCellTemperature = tableEnv.toChangelogStream(batteryCellTemperatureTable);
    // print
    batterCellTemperature.print("BatterCellTemperature::>> ").setParallelism(1);

    // Battery Cell Temperature Writer
    this.batteryTemperatureWriter.save(batterCellTemperature, false, false, false);

    //    // Battery status - temperatureMax and temperatureMin
    //    this.batteryStatusTempMaxAndMinWriter.save(batterCellTemperature);

    Table bmsAlarmsAndProtectionsTable =
        batteryAlarmsAndProtectionsTransformer.transform(env, tableEnv, bmsStatusStream);

    bmsAlarmsAndProtectionsTable =
        bmsAlarmsAndProtectionsTable.select(
            $("event_time").as("event_time"),
            $("observed_timestamp").as("timestamp"),
            $("ingestion_timestamp").as("ingestion_timestamp"),
            $("correlation_id").as("correlation_id"),
            $("imei").as("imei"),
            $("vehicle_id").as("vehicle_id"),
            $("mfr_org_id").as("mfr_org_id"), // long
            $("owner_org_id").as("owner_org_id"), // long
            $("alarm").as("alarm"),
            $("protection").as("protection"),
            $("alarm_name").as("alarm_name"),
            $("protection_name").as("protection_name"),
            $("alarm_id").as("alarm_id"),
            $("part_type").as("part_type"));

    // print schema
    bmsAlarmsAndProtectionsTable.printSchema();

    DataStream<Row> bmsAlarmsAndProtections =
        tableEnv.toChangelogStream(bmsAlarmsAndProtectionsTable);
    // print
    bmsAlarmsAndProtections.print("BatteryAlarmsAndProtections::>> ").setParallelism(1);

    // Write cell temperatures to db.
    this.alarmsAndProtectionsWriter.save(bmsAlarmsAndProtections, false, false, false);

    // ----------------------------------------------------------------------
    // --------------------------- Motor Faults------------------------------
    Table motorFaultsTable =
        motorFaultsTransformer.transform(env, tableEnv, motorControllerStatusStream);

    motorFaultsTable =
        motorFaultsTable.select(
            $("event_time").as("event_time"),
            $("observed_timestamp").as("timestamp"),
            $("ingestion_timestamp").as("ingestion_timestamp"),
            $("correlation_id").as("correlation_id"),
            $("imei").as("imei"),
            $("vehicle_id").as("vehicle_id"),
            $("mfr_org_id").as("mfr_org_id"), // long
            $("owner_org_id").as("owner_org_id"), // long
            $("alarm").as("alarm"),
            $("protection").as("protection"),
            $("alarm_name").as("alarm_name"),
            $("protection_name").as("protection_name"),
            $("alarm_id").as("alarm_id"),
            $("part_type").as("part_type"));

    // print schema
    motorFaultsTable.printSchema();

    DataStream<Row> motorFaults = tableEnv.toChangelogStream(motorFaultsTable);
    // print
    motorFaults.print("MotorFaults::>> ").setParallelism(1);

    // Write motor faults to db.
    this.motorFaultsWriter.save(motorFaults, false, false, false);

    // Batter Cell Voltages...
    // Let's fetch the table
    Table batteryCellVoltagesTable =
        batteryCellVoltageTransformer.transform(env, tableEnv, bmsStatusStream, bmsVoltageStream);

    // Attach aliases.
    batteryCellVoltagesTable =
        batteryCellVoltagesTable.select(
            $("event_time").as("event_time"),
            $("observed_timestamp").as("timestamp"),
            $("ingestion_timestamp").as("ingestion_timestamp"),
            $("correlation_id").as("correlation_id"),
            $("imei").as("imei"),
            $("vehicle_id").as("vehicle_id"),
            $("mfr_org_id").as("mfr_org_id"), // long
            $("owner_org_id").as("owner_org_id"), // long
            $("cell_id").as("cell_id"),
            $("volts").as("volts"),
            $("balancing_status").as("balancing_status"));

    // print schema
    batteryCellVoltagesTable.printSchema();

    DataStream<Row> batteryCellVoltages = tableEnv.toChangelogStream(batteryCellVoltagesTable);
    // print
    batteryCellVoltages.print("BatteryCellVoltages::>> ").setParallelism(1);
    // Write cell voltages to db.
    this.batteryCellVoltagesWriter.save(batteryCellVoltages, false, false, false);

    // ----------------------------------------------------------------------
    // --------------------------- Motor Controller Merged Data------------------------------
    Table motorMergedTable =
        motorControllerMergedTransformer.transform(env, tableEnv, motorControllerMergedStream);

    motorMergedTable =
        motorMergedTable.select(
            $("event_time").as("event_time"),
            $("observed_timestamp").as("timestamp"),
            $("ingestion_timestamp").as("ingestion_timestamp"),
            $("correlation_id").as("correlation_id"),
            $("imei").as("imei"),
            $("vehicle_id").as("vehicle_id"),
            $("mfr_org_id").as("mfr_org_id"), // long
            $("owner_org_id").as("owner_org_id"), // long
            $("dc_voltage").as("dc_voltage"),
            $("motor_speed_corrected").as("motor_speed"),
            $("dc_current_corrected").as("dc_current"),
            $("motor_temperature").as("motor_temperature"),
            $("mcs_temperature").as("mcs_temperature"),
            $("drive_selection").as("drive_selection"),
            $("regeneration").as("regeneration"),
            $("ready_sign").as("ready_sign"),
            $("p_light").as("p_light"),
            $("reverse").as("reverse"),
            $("cruise").as("cruise"),
            $("vehicle_brake").as("vehicle_brake"),
            $("side_stand").as("side_stand"),
            $("throttle_percentage_corrected").as("throttle_percentage"),
            $("motor_id").as("motor_id"),
            $("di_motion").as("di_motion"),
            $("di_ignition").as("di_ignition"),
            $("motor_speed_kmph").as("motor_speed_kmph"),
            $("motor_distance").as("motor_distance"));

    // print schema
    motorMergedTable.printSchema();

    DataStream<Row> motorMerged = tableEnv.toChangelogStream(motorMergedTable);
    // print
    motorMerged.print("MotorMerged::>> ").setParallelism(1);

    // Write motor faults to db.
    this.motorControllerMergedWriter.save(motorMerged, false, false, false);
  }
}
