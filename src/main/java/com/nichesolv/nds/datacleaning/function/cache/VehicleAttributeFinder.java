package com.nichesolv.nds.datacleaning.function.cache;

import com.fasterxml.jackson.databind.JsonNode;

import java.util.HashMap;
import java.util.Map;

public class VehicleAttributeFinder {
    private final Map<String, Map<String, String>> attributeCache = new HashMap<>();

    public VehicleAttributeFinder() {

    }

    public VehicleAttributeFinder(JsonNode vehicleData) {
        buildPartModelAttributeCache(vehicleData);
    }

    private void buildPartModelAttributeCache(JsonNode vehicleData) {
        JsonNode partModels = vehicleData.path("partModels");

        for (JsonNode partModel : partModels) {
            String partType = partModel.path("partType").asText();

            attributeCache.putIfAbsent(partType, new HashMap<>());

            JsonNode attributes = partModel.path("partModelAttributes");
            for (JsonNode attribute : attributes) {
                String attributeName = attribute.path("name").asText();
                String attributeValue = attribute.path("value").asText();
                attributeCache.get(partType).put(attributeName, attributeValue);
            }
        }
    }

    public void buildVehicleAttributeCache(JsonNode vehicleData) {
        String partType = "VEHICLE";
        attributeCache.putIfAbsent(partType, new HashMap<>());
        JsonNode attributes = vehicleData.path("partModelAttributes");
        for (JsonNode attribute : attributes) {
            String attributeName = attribute.path("name").asText();
            String attributeValue = attribute.path("value").asText();
            attributeCache.get(partType).put(attributeName, attributeValue);
        }
    }

    public Map<String, String> getSpecificAttributesByPartType(String partType, String... attributeNames) {
        Map<String, String> attributes = attributeCache.getOrDefault(partType, new HashMap<>());
        Map<String, String> result = new HashMap<>();
        for (String attributeName : attributeNames) {
            if (attributes.containsKey(attributeName)) {
                result.put(attributeName, attributes.get(attributeName));
            }
        }
        return result;
    }
}
