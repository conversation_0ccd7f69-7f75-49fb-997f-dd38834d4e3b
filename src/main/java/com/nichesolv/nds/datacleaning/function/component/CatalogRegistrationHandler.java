package com.nichesolv.nds.datacleaning.function.component;

import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/** Class that handles catalog registration. */
@Component
public class CatalogRegistrationHandler {

  private static final Logger LOG = LoggerFactory.getLogger(CatalogRegistrationHandler.class);

  @Value("${appConfig.persistence.catalog.name}")
  private String catalogName;

  @Value("${appConfig.persistence.catalog.defaultDatabase}")
  private String defaultDatabase;

  @Value("${appConfig.persistence.catalog.username}")
  private String username;

  @Value("${appConfig.persistence.catalog.password}")
  private String password;

  @Value("${appConfig.persistence.catalog.baseUrl}")
  private String baseUrl;

  public void registerCatalog(StreamTableEnvironment tableEnv) {
    if (LOG.isInfoEnabled()) LOG.info("Registering catalog");
    String createCatalogStatement =
        "CREATE CATALOG "
            + catalogName
            + " WITH ("
            + "'type'='jdbc',"
            + "'default-database'='"
            + defaultDatabase
            + "',"
            + "'username'='"
            + username
            + "',"
            + "'password'='"
            + password
            + "',"
            + "'base-url'='"
            + baseUrl
            + "'"
            + ")";

    if (LOG.isInfoEnabled()) LOG.info("CreateCatalogStatement: {}", createCatalogStatement);

    // Register a catalog
    tableEnv.executeSql(createCatalogStatement);
  }
}
