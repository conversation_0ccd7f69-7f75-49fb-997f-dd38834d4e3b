package com.nichesolv.nds.datacleaning.function.component;

import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class LookupCacheRegistrationHandler {

  @Value("${appConfig.persistence.jdbcConnectionUrl}")
  private String connectionString;

  @Value("${appConfig.persistence.jdbcUsername}")
  private String username;

  @Value("${appConfig.persistence.jdbcPassword}")
  private String password;

  @Value("${appConfig.lookup.cache}")
  private String strategy;

  @Value("${appConfig.lookup.partial-cache.max-rows}")
  private Integer maxRows;

  @Value("${appConfig.lookup.partial-cache.expire-after-write}")
  private String expireAfterWrite;

  @Value("${appConfig.lookup.partial-cache.expire-after-access}")
  private String expireAfterAccess;

  @Value("${appConfig.lookup.partial-cache.cache-missing-key}")
  private Boolean cacheMissingKeys;

  @Value("${appConfig.lookup.max-retries}")
  private Integer maxRetries;

  /**
   *
   *
   * <pre>                                                            Table "public.vehicle"
   *           Column          |            Type             | Collation | Nullable | Default | Storage  | Compression | Stats target | Description
   * --------------------------+-----------------------------+-----------+----------+---------+----------+-------------+--------------+-------------
   *  id                       | bigint                      |           | not null |         | plain    |             |              |
   *  birth_time               | timestamp(6) with time zone |           |          |         | plain    |             |              |
   *  created_on               | timestamp(6) with time zone |           |          |         | plain    |             |              |
   *  imei                     | character varying(255)      |           | not null |         | extended |             |              |
   *  mfr_date                 | date                        |           |          |         | plain    |             |              |
   *  modified_on              | timestamp(6) with time zone |           |          |         | plain    |             |              |
   *  net_weight               | real                        |           |          |         | plain    |             |              |
   *  operation_status         | character varying(255)      |           |          |         | extended |             |              |
   *  color_id                 | bigint                      |           |          |         | plain    |             |              |
   *  dealership_id            | bigint                      |           |          |         | plain    |             |              |
   *  manufacturer_id          | bigint                      |           |          |         | plain    |             |              |
   *  owner_id                 | bigint                      |           |          |         | plain    |             |              |
   *  vehicle_model_id         | bigint                      |           |          |         | plain    |             |              |
   *  odometer_last_updated_at | timestamp(6) with time zone |           |          |         | plain    |             |              |
   *  total_distance_traveled  | real                        |           |          |         | plain    |             |              |
   * Indexes:
   *     "vehicle_pkey" PRIMARY KEY, btree (id)
   *     "uk_1hpb53ftilstbb8tpc9f1gyjn" UNIQUE CONSTRAINT, btree (imei)
   *     "vehicle_manufacturer_idx" btree (manufacturer_id)
   *     "vehicle_model_idx" btree (vehicle_model_id)
   * Check constraints:
   *     "vehicle_operation_status_check" CHECK (operation_status::text = ANY (ARRAY['ACTIVE'::character varying::text, 'INACTIVE'::character varying::text, 'UNDER_MAINTENANCE'::character varying::text, 'OUT_OF_SERVICE'::character varying::text, 'SOLD'::character varying::text, 'DECOMMISSIONED'::character varying::text, 'ON_HIRE'::character varying::text, 'OFF_HIRE'::character varying::text]))
   * Foreign-key constraints:
   *     "fk14agfhjn30uqs65a88e5iq44p" FOREIGN KEY (dealership_id) REFERENCES organisations(id)
   *     "fk7f07wmi12tx1kqvegt9yj1hth" FOREIGN KEY (color_id) REFERENCES vehicle_color(id)
   *     "fke8vgmx4h8eedo1u3c0s32w0mc" FOREIGN KEY (owner_id) REFERENCES organisations(id)
   *     "fkjtchj1qk6y3jdm3ycsbaoci6q" FOREIGN KEY (vehicle_model_id) REFERENCES vehicle_model(id)
   *     "fkqk4b94gsb8i109oae2vou64bg" FOREIGN KEY (manufacturer_id) REFERENCES organisations(id)
   * Referenced by:
   *     TABLE "telemetry" CONSTRAINT "fk2ohe13nas41u2d7m71chtxi8k" FOREIGN KEY (vehicle_id) REFERENCES vehicle(id)
   *     TABLE "battery_stack" CONSTRAINT "fk35x8p3b8vfyf4ghp8ijkmpm84" FOREIGN KEY (vehicle_id) REFERENCES vehicle(id)
   *     TABLE "vehicle_test_ride_form" CONSTRAINT "fk6up710apn7nupip3aagihvdxi" FOREIGN KEY (vehicle_id) REFERENCES vehicle(id)
   *     TABLE "vehicle_parts" CONSTRAINT "fk7i7hq1qif7nx8sptet05e7raa" FOREIGN KEY (vehicle_id) REFERENCES vehicle(id)
   *     TABLE "user_profile" CONSTRAINT "fk8dumvb8t46bq2drkdqbs3pidq" FOREIGN KEY (connected_vehicle_id) REFERENCES vehicle(id)
   *     TABLE "vehicle_test" CONSTRAINT "fk979jml0unof8y96i4o9o1cr8b" FOREIGN KEY (vehicle_id) REFERENCES vehicle(id)
   *     TABLE "fleet_vehicles" CONSTRAINT "fk_fleveh_on_vehicle" FOREIGN KEY (vehicle_id) REFERENCES vehicle(id)
   *     TABLE "vehicle_fleet" CONSTRAINT "fk_fleveh_on_vehicle" FOREIGN KEY (vehicles_id) REFERENCES vehicle(id)
   *     TABLE "vehicle_registration_details" CONSTRAINT "fka6vlycy2fdbcdjm8a37b708gm" FOREIGN KEY (vehicle_id) REFERENCES vehicle(id)
   *     TABLE "battery_cell" CONSTRAINT "fkb47tg9rb81od1w6iofjkoso93" FOREIGN KEY (vehicle_id) REFERENCES vehicle(id)
   *     TABLE "user_vehicles" CONSTRAINT "fkllte6ttvtr515ij8g2m7y1qtw" FOREIGN KEY (vehicle_id) REFERENCES vehicle(id)
   *     TABLE "trip" CONSTRAINT "fkrji8htecrp06ao6s7nfubswnr" FOREIGN KEY (vehicle_id) REFERENCES vehicle(id)
   * </pre>
   */
  private void registerVehicleTable(StreamTableEnvironment tableEnv) {
    // Here we are using lookup joins to fetch data from timescale db, we need to enrich the data
    // stream before writing to the telemetry tables.
    // 1. We need to create a table that will read from the underlying table.
    // todo: maybe consider using asyncio with caching or something?
    // todo: Are lookup joins a good idea here?

    // This registers the Vehicle table in flink catalog, the data is fetched from the postgresql
    // table of the same name,
    final String tableName = "vehicle";
    final String connector = "jdbc";
    String createVehicleTable =
        "CREATE TABLE vehicle (\n"
            + "   id BIGINT,\n"
            + "   imei STRING,\n"
            + "   color_id BIGINT,\n"
            + "   dealership_id BIGINT,\n"
            + "   mfr_org_id BIGINT,\n"
            + "   owner_org_id BIGINT,\n"
            + "   vehicle_model_id BIGINT,\n"
            + "   operation_status STRING,\n"
            + "   PRIMARY KEY (id) NOT ENFORCED\n"
            + " ) WITH (\n"
            + "   'connector' = '"
            + connector
            + "',\n"
            + "   'url' = '"
            + this.connectionString
            + "',\n"
            + "   'username' = '"
            + this.username
            + "',\n"
            + "   'password' = '"
            + this.password
            + "',\n"
            + "   'table-name' = '"
            + tableName
            + "',\n"
            + "    'lookup.cache'='"
            + strategy
            + "',\n"
            + "   'lookup.partial-cache.max-rows'='"
            + maxRows
            + "',\n"
            + "   'lookup.partial-cache.expire-after-write'='"
            + expireAfterWrite
            + "',\n"
            + "   'lookup.partial-cache.expire-after-access'='"
            + expireAfterAccess
            + "',\n"
            + "   'lookup.partial-cache.cache-missing-key'='"
            + cacheMissingKeys
            + "',\n"
            + "   'lookup.max-retries'='"
            + maxRetries
            + "'\n"
            + " );";
    // Let's register this table in flink catalog so that we can query it later.
    tableEnv.executeSql(createVehicleTable);
  }

  private void registerAlarmsAndProtectionsTable(StreamTableEnvironment tableEnv) {
    // Here we are using lookup joins to fetch data from timescale db, we need to enrich the data
    // stream before writing to the telemetry tables.
    // 1. We need to create a table that will read from the underlying table.
    // todo: maybe consider using asyncio with caching or something?
    // todo: Are lookup joins a good idea here?

    final String tableName = "alarm_type";
    final String connector = "jdbc";
    String createAlarmTypeTable =
        "CREATE TABLE alarm_type (\n"
            + "   id BIGINT,\n"
            + "   name STRING,\n"
            + "   organisation_id BIGINT,\n"
            + "   PRIMARY KEY (id) NOT ENFORCED\n"
            + " ) WITH (\n"
            + "   'connector' = '"
            + connector
            + "',\n"
            + "   'url' = '"
            + this.connectionString
            + "',\n"
            + "   'username' = '"
            + this.username
            + "',\n"
            + "   'password' = '"
            + this.password
            + "',\n"
            + "   'table-name' = '"
            + tableName
            + "',\n"
            + "    'lookup.cache'='"
            + strategy
            + "',\n"
            + "   'lookup.partial-cache.max-rows'='"
            + maxRows
            + "',\n"
            + "   'lookup.partial-cache.expire-after-write'='"
            + expireAfterWrite
            + "',\n"
            + "   'lookup.partial-cache.expire-after-access'='"
            + expireAfterAccess
            + "',\n"
            + "   'lookup.partial-cache.cache-missing-key'='"
            + cacheMissingKeys
            + "',\n"
            + "   'lookup.max-retries'='"
            + maxRetries
            + "'\n"
            + " );";
    // Let's register this table in flink catalog so that we can query it later.
    tableEnv.executeSql(createAlarmTypeTable);
  }

  public void register(StreamTableEnvironment tableEnv) {
    // Register vehicle lookup table.
    this.registerVehicleTable(tableEnv);
    // Register alarm_type lookup table.
    this.registerAlarmsAndProtectionsTable(tableEnv);
  }
}
