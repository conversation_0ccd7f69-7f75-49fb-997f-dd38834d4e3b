package com.nichesolv.nds.datacleaning.function.component;

import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/** Loads all the udfs. */
@Component
public class ThirdPartyUdfLoader {

  private static final Logger LOG = LoggerFactory.getLogger(ThirdPartyUdfLoader.class);

  // Configurations for fetching udfs and archives from s3 bucket.
  @Value("${appConfig.python.files}")
  private String pythonFiles;

  @Value("${appConfig.python.archive}")
  private String pythonArchive;

  @Value("${appConfig.python.executable}")
  private String pythonExecutable;

  @Value("${appConfig.python.client.executable}")
  private String pythonClientExecutable;

  // To deploy the python functions as archive.
  @Value("${appConfig.deployWithZipArchiveEnabled}")
  private Boolean isDeployWithZipArchiveEnabled;

  @Value("${appConfig.udf.dataCleaning.python.v1.basePath}")
  private String basePath;

  @Value("${appConfig.udf.dataCleaning.python.v1.entryPoint}")
  private String entryPoint;

  @Value("${appConfig.udf.dataCleaning.python.v1.pythonExecutablePath}")
  private String pythonExecutablePath;

  public void load(StreamTableEnvironment tableEnv) {
    final String requirements = basePath + "/requirements.txt";

    final String udf = basePath + "/v1";
    final String executable = pythonExecutablePath;

    if (LOG.isInfoEnabled())
      LOG.info(
          "UDF base path: {}, python executable path: {}, entryPoint: {}",
          basePath,
          pythonExecutablePath,
          entryPoint);

    if (LOG.isInfoEnabled())
      LOG.info("isDeployWithZipArchiveEnabled: {}", isDeployWithZipArchiveEnabled);

    // These are the setting that work with python zip archive.
    if (isDeployWithZipArchiveEnabled) {
      if (LOG.isInfoEnabled()) LOG.info("Fetching python archive from: {}", pythonArchive);
      tableEnv.getConfig().getConfiguration().setString("python.archive", pythonArchive);
      if (LOG.isInfoEnabled()) LOG.info("Setting python executable: {}", pythonExecutable);
      tableEnv.getConfig().getConfiguration().setString("python.executable", pythonExecutable);
      if (LOG.isInfoEnabled())
        LOG.info("Setting python client executable: {}", pythonClientExecutable);
      tableEnv
          .getConfig()
          .getConfiguration()
          .setString("python.client.executable", pythonClientExecutable);
    } else {
      if (LOG.isInfoEnabled()) LOG.info("Fetching requirements from: {}", requirements);
      tableEnv.getConfig().getConfiguration().setString("python.requirements", requirements);
      if (LOG.isInfoEnabled()) LOG.info("Setting python executable from: {}", executable);
      tableEnv.getConfig().getConfiguration().setString("python.client.executable", executable);
      tableEnv.getConfig().getConfiguration().setString("python.executable", executable);
    }

    if (LOG.isInfoEnabled())
      LOG.info("Fetching python files from: {}", isDeployWithZipArchiveEnabled ? pythonFiles : udf);
    tableEnv
        .getConfig()
        .getConfiguration()
        .setString("python.files", isDeployWithZipArchiveEnabled ? pythonFiles : udf);

    // General user defined function.
    tableEnv.executeSql(
        "create temporary system function impute_udf as 'udf.impute_udf' language python");
  }
}
