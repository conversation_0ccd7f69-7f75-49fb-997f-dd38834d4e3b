package com.nichesolv.nds.datacleaning.function.component.timeattribute;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.time.Duration;
import lombok.Getter;
import lombok.Setter;
import org.apache.flink.api.common.eventtime.Watermark;
import org.apache.flink.api.common.eventtime.WatermarkGenerator;
import org.apache.flink.api.common.eventtime.WatermarkOutput;
import org.apache.flink.api.java.tuple.Tuple3;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This is a per event watermark generator, handles isolated event as well.
 *
 * @param <T>
 */
@Getter
@Setter
public class PerEventWatermarkGenerator<T extends Tuple3<Timestamp, Metadata, ?>>
    implements WatermarkGenerator<T> {

  private static final Logger LOG = LoggerFactory.getLogger(PerEventWatermarkGenerator.class);

  private long lastEventWatermark;

  private long lastMappedSystemTimeWatermark;

  private String name;

  private final long outOfOrderliness;

  public PerEventWatermarkGenerator(String name) {
    this(name, Long.MIN_VALUE, System.currentTimeMillis(), Duration.ofSeconds(10));
  }

  public PerEventWatermarkGenerator(String name, Duration outOfOrderliness) {
    this(name, Long.MIN_VALUE, System.currentTimeMillis(), outOfOrderliness);
  }

  public PerEventWatermarkGenerator(
      String name,
      Long lastEventWatermark,
      Long lastMappedSystemTimeWatermark,
      Duration outOfOrderliness) {
    this.name = name;
    this.outOfOrderliness = outOfOrderliness.toMillis();
    this.lastEventWatermark = lastEventWatermark + this.outOfOrderliness + 1;
    this.lastMappedSystemTimeWatermark = lastMappedSystemTimeWatermark;
  }

  @Override
  public void onEvent(T event, long eventTimestamp, WatermarkOutput output) {
    this.lastEventWatermark = Math.max(eventTimestamp, this.lastEventWatermark);
    this.lastMappedSystemTimeWatermark = System.currentTimeMillis();
  }

  @Override
  public void onPeriodicEmit(WatermarkOutput output) {
    final long delta = 11000L;
    final long millisecondsElapsedSinceLastWatermark = 7000L;
    long currentSystemTimeInMilliseconds = System.currentTimeMillis();
    // If an event was observed and then the stream becomes idle for more than
    // millisecondsElapsedSinceLastWatermark, then we emit a watermark.
    if (((currentSystemTimeInMilliseconds - this.lastMappedSystemTimeWatermark)
        >= millisecondsElapsedSinceLastWatermark)) {
      output.emitWatermark(new Watermark(this.lastEventWatermark + delta));
    } else {
      // Otherwise just emit the last watermark.
      // Takes care of the outorderness.
      output.emitWatermark(new Watermark(this.lastEventWatermark));
    }
  }
}
