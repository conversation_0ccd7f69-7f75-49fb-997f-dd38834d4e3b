package com.nichesolv.nds.datacleaning.function.component.timeattribute;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import org.apache.flink.api.common.eventtime.Watermark;
import org.apache.flink.api.common.eventtime.WatermarkOutput;
import org.apache.flink.api.java.tuple.Tuple3;

public class PerEventWatermarkGeneratorWithBoundedDelay<T extends Tuple3<Timestamp, Metadata, ?>>
    extends PerEventWatermarkGenerator<T> {

  private long lastEventWatermark;

  private long lastMappedSystemTimeWatermark;

  private long nextDelayedEmission;

  private final long millisecondsElapsedSinceLastWatermark;

  private final long delta;

  private static final long defaultDelta = 30000L;

  public PerEventWatermarkGeneratorWithBoundedDelay() {
    this("default", defaultDelta);
  }

  public PerEventWatermarkGeneratorWithBoundedDelay(String name) {
    this(name, defaultDelta);
  }

  public PerEventWatermarkGeneratorWithBoundedDelay(long delta) {
    this("default", delta);
  }

  public PerEventWatermarkGeneratorWithBoundedDelay(String name, long delta) {
    super(name);
    this.delta = delta;
    this.lastEventWatermark = Long.MIN_VALUE;
    this.lastMappedSystemTimeWatermark = System.currentTimeMillis();
    this.nextDelayedEmission = Long.MIN_VALUE;
    this.millisecondsElapsedSinceLastWatermark = 180000L;
  }

  @Override
  public void onEvent(T event, long eventTimestamp, WatermarkOutput output) {
    this.lastEventWatermark = Math.min(eventTimestamp, System.currentTimeMillis());
    this.lastMappedSystemTimeWatermark = System.currentTimeMillis();
  }

  @Override
  public void onPeriodicEmit(WatermarkOutput output) {
    long currentSystemTimeInMilliseconds = System.currentTimeMillis();
    // If an event was observed and then the stream becomes idle for more than
    // millisecondsElapsedSinceLastWatermark, then we emit a watermark.
    if (((currentSystemTimeInMilliseconds - this.lastMappedSystemTimeWatermark)
        >= this.millisecondsElapsedSinceLastWatermark)) {
      // we emit a watermark which is current time - 5 seconds.
      if (currentSystemTimeInMilliseconds > this.nextDelayedEmission) {
        output.emitWatermark(new Watermark(currentSystemTimeInMilliseconds));
        this.nextDelayedEmission = currentSystemTimeInMilliseconds + 30000L;
      }
    } else {
      // Otherwise just emit the last watermark.
      output.emitWatermark(new Watermark(this.lastEventWatermark));
    }
  }
}
