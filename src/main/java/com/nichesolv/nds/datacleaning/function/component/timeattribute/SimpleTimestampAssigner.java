package com.nichesolv.nds.datacleaning.function.component.timeattribute;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import org.apache.flink.api.common.eventtime.SerializableTimestampAssigner;
import org.apache.flink.api.java.tuple.Tuple3;

/**
 * Extracts time attribute from event.
 *
 * @param <T>
 */
public class SimpleTimestampAssigner<T extends Tuple3<Timestamp, Metadata, ?>>
    implements SerializableTimestampAssigner<T> {
  private static final long serialVersionUID = 3878335588893716230L;

  @Override
  public long extractTimestamp(T element, long recordTimestamp) {
    final long offset = 1000L;
    return Math.min(element.f0.getTimestamp() * offset, System.currentTimeMillis());
  }
}
