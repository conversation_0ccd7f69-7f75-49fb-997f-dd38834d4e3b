package com.nichesolv.nds.datacleaning.function.component.timeattribute;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import lombok.Getter;
import lombok.Setter;
import org.apache.flink.api.common.eventtime.*;
import org.apache.flink.api.java.tuple.Tuple3;

@Getter
@Setter
public class SimpleWatermarkStrategy<T extends Tuple3<Timestamp, Metadata, ?>>
    implements WatermarkStrategy<T> {

  private static final long serialVersionUID = -504881989573652670L;

  private String name;

  public SimpleWatermarkStrategy() {
    this.name = "default";
  }

  @Override
  public WatermarkGenerator<T> createWatermarkGenerator(
      WatermarkGeneratorSupplier.Context context) {
    return new PerEventWatermarkGenerator<>(this.name);
  }

  @Override
  public TimestampAssigner<T> createTimestampAssigner(TimestampAssignerSupplier.Context context) {
    return new SimpleTimestampAssigner<>();
  }
}
