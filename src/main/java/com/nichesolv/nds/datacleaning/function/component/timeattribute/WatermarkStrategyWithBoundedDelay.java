package com.nichesolv.nds.datacleaning.function.component.timeattribute;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import org.apache.flink.api.common.eventtime.WatermarkGenerator;
import org.apache.flink.api.common.eventtime.WatermarkGeneratorSupplier;
import org.apache.flink.api.java.tuple.Tuple3;

public class WatermarkStrategyWithBoundedDelay<T extends Tuple3<Timestamp, Metadata, ?>>
    extends SimpleWatermarkStrategy<T> {
  private static final long serialVersionUID = 650483501286924869L;

  @Override
  public WatermarkGenerator<T> createWatermarkGenerator(
      WatermarkGeneratorSupplier.Context context) {
    return new PerEventWatermarkGeneratorWithBoundedDelay<>(this.getName());
  }
}
