package com.nichesolv.nds.datacleaning.function.component.timeattribute;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.domain.Window;
import org.apache.flink.api.common.eventtime.SerializableTimestampAssigner;
import org.apache.flink.api.java.tuple.Tuple4;

public class WindowAwareTimestampAssigner<T extends Tuple4<Timestamp, Metadata, Window, ?>>
    implements SerializableTimestampAssigner<T> {

  private static final long serialVersionUID = -5737608117762172929L;

  @Override
  public long extractTimestamp(T element, long recordTimestamp) {
    final long offset = 1000L;
    return Math.min(element.f0.getTimestamp() * offset, System.currentTimeMillis());
  }
}
