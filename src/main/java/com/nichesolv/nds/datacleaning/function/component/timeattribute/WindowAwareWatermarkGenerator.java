package com.nichesolv.nds.datacleaning.function.component.timeattribute;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.domain.Window;
import org.apache.flink.api.common.eventtime.Watermark;
import org.apache.flink.api.common.eventtime.WatermarkGenerator;
import org.apache.flink.api.common.eventtime.WatermarkOutput;
import org.apache.flink.api.java.tuple.Tuple4;

public class WindowAwareWatermarkGenerator<T extends Tuple4<Timestamp, Metadata, Window, ?>>
    implements WatermarkGenerator<T> {

  @Override
  public void onEvent(T event, long eventTimestamp, WatermarkOutput output) {
    Long delta = 0L;
    output.emitWatermark(new Watermark(event.f2.getStart() + delta));
  }

  @Override
  public void onPeriodicEmit(WatermarkOutput output) {}
}
