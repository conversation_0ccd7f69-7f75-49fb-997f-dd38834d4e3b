package com.nichesolv.nds.datacleaning.function.component.timeattribute;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.domain.Window;
import org.apache.flink.api.common.eventtime.*;
import org.apache.flink.api.java.tuple.Tuple4;

public class WindowAwareWatermarkStrategy<T extends Tuple4<Timestamp, Metadata, Window, ?>>
    implements WatermarkStrategy<T> {

  private static final long serialVersionUID = -1528863685082992586L;

  @Override
  public WatermarkGenerator<T> createWatermarkGenerator(
      WatermarkGeneratorSupplier.Context context) {
    return new WindowAwareWatermarkGenerator<>();
  }

  @Override
  public TimestampAssigner<T> createTimestampAssigner(TimestampAssignerSupplier.Context context) {
    return new WindowAwareTimestampAssigner<>();
  }
}
