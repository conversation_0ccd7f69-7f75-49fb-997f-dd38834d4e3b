package com.nichesolv.nds.datacleaning.function.config;

import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;
import org.apache.flink.streaming.connectors.rabbitmq.common.RMQConnectionConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/** This is the main application config class. */
@Configuration
public class AppConfig {

  @Value("${appConfig.queueingEngineConfig.host}")
  private String host;

  @Value("${appConfig.queueingEngineConfig.port}")
  private Integer port;

  @Value("${appConfig.queueingEngineConfig.virtualHost}")
  private String virtualHost;

  @Value("${appConfig.queueingEngineConfig.username}")
  private String username;

  @Value("${appConfig.queueingEngineConfig.password}")
  private String password;

  @Value("${appConfig.persistence.jdbcConnectionUrl:*****************************************}")
  private String jdbcConnectionUrl;

  @Value("${appConfig.persistence.jdbcUsername:postgres}")
  private String jdbcUsername;

  @Value("${appConfig.persistence.jdbcPassword:password}")
  private String jdbcPassword;

  @Bean
  public RMQConnectionConfig rmqConnectionConfig() {
    return new RMQConnectionConfig.Builder()
        .setHost(host)
        .setPort(port)
        .setVirtualHost(virtualHost)
        .setUserName(username)
        .setPassword(password)
        .build();
  }

  @Bean
  public JdbcConnectionOptions jdbcConnectionConfig() {
    final String jdbcDriverName = "org.postgresql.Driver";
    return new JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
        .withUrl(jdbcConnectionUrl)
        .withDriverName(jdbcDriverName)
        .withUsername(jdbcUsername)
        .withPassword(jdbcPassword)
        .build();
  }

  @Bean
  public JdbcExecutionOptions jdbcExecutionOptions() {
    return JdbcExecutionOptions.builder()
        .withBatchSize(1000)
        .withBatchIntervalMs(200)
        .withMaxRetries(5)
        .build();
  }
}
