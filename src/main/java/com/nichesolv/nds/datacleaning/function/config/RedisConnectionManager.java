package com.nichesolv.nds.datacleaning.function.config;

import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.sync.RedisCommands;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.Duration;

@Component
public class RedisConnectionManager {

    private static RedisConnectionManager instance;

    private static RedisClient redisClient;
    private static StatefulRedisConnection<String, String> connection;
    private static RedisCommands<String, String> commands;


    @Value("${redis.host}")
    private String host;

    @Value("${redis.port}")
    private int port;

    @Value("${redis.username}")
    private String username;

    @Value("${redis.password}")
    private String password;


    private RedisConnectionManager() {
    }

    @PostConstruct
    private void init() {
        if (redisClient == null) {
            synchronized (RedisConnectionManager.class) {
                if (instance == null) {
                    instance = this;
                }
                if (redisClient == null) { // Double-checked locking for thread safety
                    RedisURI redisURI = RedisURI.builder()
                            .withHost(host)
                            .withPort(port)
                            .withPassword(password.isEmpty() ? null : password.toCharArray())
                            .withTimeout(Duration.ofSeconds(10))
                            .build();

                    redisClient = RedisClient.create(redisURI);
                    connection = redisClient.connect();
                    commands = connection.sync();
                }
            }
        }
    }

    public static RedisConnectionManager getInstance() {
        if (instance == null) {
            throw new IllegalStateException("RedisConnectionManager is not initialized. Use @Autowired instead.");
        }
        return instance;
    }

    public String getValue(String key) {
        return commands.get(key);
    }

    public void setValue(String key, String value) {
        commands.set(key, value);
    }

    @PreDestroy
    public void close() {
        if (connection != null) {
            connection.close();
        }
        if (redisClient != null) {
            redisClient.shutdown();
        }
    }
}