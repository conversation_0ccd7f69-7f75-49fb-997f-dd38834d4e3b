package com.nichesolv.nds.datacleaning.function.config;

import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.sync.RedisCommands;
import java.time.Duration;
import java.io.Serializable;

public class RedisConnectionManager2 implements Serializable {
    private static final long serialVersionUID = 1L;

    private transient RedisClient redisClient;
    private transient StatefulRedisConnection<String, String> connection;
    private transient RedisCommands<String, String> commands;

    private final String host;
    private final int port;
    private final String username;
    private final String password;

    public RedisConnectionManager2(String host, int port, String username, String password) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
    }

    public void init() {
        if (redisClient == null) {
            RedisURI redisURI = RedisURI.builder()
                    .withHost(host)
                    .withPort(port)
                    .withPassword(password.isEmpty() ? null : password.toCharArray())
                    .withTimeout(Duration.ofSeconds(10))
                    .build();

            redisClient = RedisClient.create(redisURI);
            connection = redisClient.connect();
            commands = connection.sync();
        }
    }

    public String getValue(String key) {
        return commands.get(key);
    }

    public void setValue(String key, String value) {
        commands.set(key, value);
    }

    public void close() {
        if (connection != null) {
            connection.close();
        }
        if (redisClient != null) {
            redisClient.shutdown();
        }
    }
}