package com.nichesolv.nds.datacleaning.function.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class RedisConnectionManagerFactory2 {

    @Value("${redis.host}")
    private String host;

    @Value("${redis.port}")
    private int port;

    @Value("${redis.username}")
    private String username;

    @Value("${redis.password}")
    private String password;

    public RedisConnectionManager2 createRedisConnectionManager() {
        return new RedisConnectionManager2(host, port, username, password);
    }
}