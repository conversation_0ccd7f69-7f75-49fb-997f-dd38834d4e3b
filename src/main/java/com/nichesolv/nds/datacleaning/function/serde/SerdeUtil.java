package com.nichesolv.nds.datacleaning.function.serde;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.proto.model.MetadataProto;
import com.nichesolv.nds.model.proto.model.TimestampProto;

/** A simple serialization, deserialization utils class. */
public class SerdeUtil {

  public static Timestamp buildTimestamp(TimestampProto.Timestamp protoc) {
    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getIngestionTimestamp());
    return timestamp;
  }

  public static Metadata buildMetadata(MetadataProto.Metadata protoc) {
    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMagic());
    metadata.setCorrelationId(protoc.getCorrelationId());
    metadata.setImei(protoc.getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getSqn()).shortValue());
    return metadata;
  }
}
