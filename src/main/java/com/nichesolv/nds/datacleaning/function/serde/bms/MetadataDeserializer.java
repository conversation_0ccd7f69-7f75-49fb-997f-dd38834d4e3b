package com.nichesolv.nds.datacleaning.function.serde.bms;

import com.nichesolv.nds.datacleaning.function.serde.SerdeUtil;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Chemistry;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.springframework.stereotype.Component;

@Component("batteryMetadataDeserializer")
public class MetadataDeserializer
    implements DeserializationSchema<
        Tuple3<
            Timestamp,
            Metadata,
            com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata>> {
  private static final long serialVersionUID = -4423254780982722961L;

  @Override
  public Tuple3<
          Timestamp, Metadata, com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata>
      deserialize(byte[] data) throws IOException {
    BatteryManagementSystemProto.BmsMetadata protoc =
        BatteryManagementSystemProto.BmsMetadata.parseFrom(data);

    com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.MetadataImpl.MetadataImplBuilder
        builder = com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.MetadataImpl.builder();

    if (protoc.hasBatterySerialNumber())
      builder.batterySerialNumber(protoc.getBatterySerialNumber());

    if (protoc.hasBmsSerialNumber()) builder.bmsSerialNumber(protoc.getBmsSerialNumber());

    if (protoc.hasProtocolVersion()) builder.protocolVersion(protoc.getProtocolVersion());

    if (protoc.hasRatedCapacity()) builder.ratedCapacity(protoc.getRatedCapacity());

    if (protoc.hasActiveCell()) builder.activeCell(protoc.getActiveCell());

    if (protoc.hasDod()) builder.dod(protoc.getDod());

    if (protoc.hasChemistry()) builder.chemistry(Chemistry.of(protoc.getChemistry().getNumber()));

    // Todo: missing bmsFirmwareVersion in proto

    // Timestamp.
    Timestamp timestamp = SerdeUtil.buildTimestamp(protoc.getTimestamp());

    // Metadata.
    Metadata metadata = SerdeUtil.buildMetadata(protoc.getMetadata());

    return new Tuple3<>(timestamp, metadata, builder.build());
  }

  @Override
  public boolean isEndOfStream(
      Tuple3<
              Timestamp,
              Metadata,
              com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata>
          nextElement) {
    return false;
  }

  @Override
  public TypeInformation<
          Tuple3<
              Timestamp,
              Metadata,
              com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata>>
      getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(
            com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata.class,
            new HashMap<>()));
  }
}
