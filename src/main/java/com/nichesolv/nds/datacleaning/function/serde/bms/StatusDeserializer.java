package com.nichesolv.nds.datacleaning.function.serde.bms;

import com.nichesolv.nds.datacleaning.function.serde.SerdeUtil;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.*;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto;
import com.nichesolv.nds.model.proto.model.ProtectionProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component("batteryStatusSerializer")
public class StatusDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, BatteryStatus>> {

  private static final Logger logger = LoggerFactory.getLogger(StatusDeserializer.class);

  private static final long serialVersionUID = -4423254780982722961L;

  @Override
  public Tuple3<Timestamp, Metadata, BatteryStatus> deserialize(byte[] data) throws IOException {
    BatteryManagementSystemProto.Status protoc =
        BatteryManagementSystemProto.Status.parseFrom(data);

    // Battery status builder.
    BatteryStatusImpl.BatteryStatusImplBuilder builder = BatteryStatusImpl.builder();

    // Protoc with optional fields provide hasField() method to check if the field has been set.
    if (protoc.hasChgCycleCount()) builder.chgCycleCount(protoc.getChgCycleCount());

    if (protoc.hasDsgCycleCount()) builder.dsgCycleCount(protoc.getDsgCycleCount());

    if (protoc.hasCellVoltMax()) builder.cellVoltMax(protoc.getCellVoltMax());

    if (protoc.hasCellVoltMin()) builder.cellVoltMin(protoc.getCellVoltMin());

    if (protoc.hasTemperatureMin()) builder.temperatureMin(protoc.getTemperatureMin());

    if (protoc.hasTemperatureMax()) builder.temperatureMax(protoc.getTemperatureMax());

    if (protoc.hasSoh()) builder.soh(protoc.getSoh());

    if (protoc.hasSoc()) builder.soc(protoc.getSoc());

    if (protoc.hasCurrent()) builder.current(protoc.getCurrent());

    if (protoc.hasBatteryVolt()) builder.batteryVolt(protoc.getBatteryVolt());

    if (protoc.hasRemainingCapacity()) builder.remainingCapacity(protoc.getRemainingCapacity());

    if (protoc.getAlarmsCount() > 0) {
      for (BatteryManagementSystemProto.Alarm alarm : protoc.getAlarmsList()) {
        builder.alarm(
            AlarmImpl.builder()
                .set(alarm.getIsSet())
                .alarmType(AlarmType.of(alarm.getAlarmType().getNumber()))
                .build());
      }
    }

    if (protoc.getProtectionsCount() > 0) {
      for (ProtectionProto.Protection protection : protoc.getProtectionsList()) {
        builder.protection(
            ProtectionImpl.builder()
                .protectionType(ProtectionType.of(protection.getNumber()))
                .set(true)
                .build());
      }
    }

    if (protoc.getBalancingStatusesCount() > 0) {
      for (BatteryManagementSystemProto.BalancingStatus status :
          protoc.getBalancingStatusesList()) {
        builder.balancingStatus(
            BalancingStatusImpl.builder()
                .status(status.getIsSet())
                .cellId(status.getCellId())
                .build());
      }
    }

    if (protoc.hasMosfetTemperature()) builder.mosfetTemperature(protoc.getMosfetTemperature());

    // Timestamp.
    Timestamp timestamp = SerdeUtil.buildTimestamp(protoc.getTimestamp());

    // Metadata.
    Metadata metadata = SerdeUtil.buildMetadata(protoc.getMetadata());

    Tuple3<Timestamp, Metadata, BatteryStatus> batteryStatus =
        new Tuple3<>(timestamp, metadata, builder.build());

    if (logger.isInfoEnabled()) logger.info("BatterStatusDeserialized: {}", batteryStatus);

    return batteryStatus;
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, BatteryStatus> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, BatteryStatus>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(BatteryStatus.class, new HashMap<>()));
  }
}
