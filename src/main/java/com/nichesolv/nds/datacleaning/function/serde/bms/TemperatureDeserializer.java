package com.nichesolv.nds.datacleaning.function.serde.bms;

import com.nichesolv.nds.datacleaning.function.serde.SerdeUtil;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.BatteryCellTemperature;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.BatteryCellTemperatureImpl;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.CellTemperatureImpl;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.springframework.stereotype.Component;

@Component("batteryTemperatureDeserializer")
public class TemperatureDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, BatteryCellTemperature>> {
  private static final long serialVersionUID = -4423254780982722961L;

  @Override
  public Tuple3<Timestamp, Metadata, BatteryCellTemperature> deserialize(byte[] data)
      throws IOException {
    BatteryManagementSystemProto.Temperature protoc =
        BatteryManagementSystemProto.Temperature.parseFrom(data);

    BatteryCellTemperatureImpl.BatteryCellTemperatureImplBuilder builder =
        BatteryCellTemperatureImpl.builder();

    if (protoc.getCellTemperaturesList().size() > 0) {
      for (BatteryManagementSystemProto.CellTemperature cellTemperature :
          protoc.getCellTemperaturesList()) {
        builder.cellTemperature(
            CellTemperatureImpl.builder()
                .temperature(cellTemperature.getTemperature())
                .id(cellTemperature.getCellId())
                .build());
      }
    }

    // Timestamp.
    Timestamp timestamp = SerdeUtil.buildTimestamp(protoc.getTimestamp());

    // Metadata.
    Metadata metadata = SerdeUtil.buildMetadata(protoc.getMetadata());

    return new Tuple3<>(timestamp, metadata, builder.build());
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, BatteryCellTemperature> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, BatteryCellTemperature>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(BatteryCellTemperature.class, new HashMap<>()));
  }
}
