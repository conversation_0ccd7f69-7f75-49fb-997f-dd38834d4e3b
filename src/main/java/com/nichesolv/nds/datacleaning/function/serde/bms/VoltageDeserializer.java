package com.nichesolv.nds.datacleaning.function.serde.bms;

import com.nichesolv.nds.datacleaning.function.serde.SerdeUtil;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltage;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltageImpl;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.CellVoltageImpl;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.springframework.stereotype.Component;

@Component("batteryVoltageDeserializer")
public class VoltageDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, BatteryCellVoltage>> {
  private static final long serialVersionUID = -4423254780982722961L;

  @Override
  public Tuple3<Timestamp, Metadata, BatteryCellVoltage> deserialize(byte[] data)
      throws IOException {
    BatteryManagementSystemProto.Voltage protoc =
        BatteryManagementSystemProto.Voltage.parseFrom(data);

    BatteryCellVoltageImpl.BatteryCellVoltageImplBuilder builder = BatteryCellVoltageImpl.builder();

    if (protoc.getCellVoltagesList().size() > 0) {
      for (BatteryManagementSystemProto.CellVoltage cellVoltage : protoc.getCellVoltagesList()) {

        builder.cellVoltage(
            CellVoltageImpl.builder()
                .volts(cellVoltage.getVolts())
                .id(cellVoltage.getCellId())
                .build());
      }
    }

    // Timestamp.
    Timestamp timestamp = SerdeUtil.buildTimestamp(protoc.getTimestamp());

    // Metadata.
    Metadata metadata = SerdeUtil.buildMetadata(protoc.getMetadata());

    return new Tuple3<>(timestamp, metadata, builder.build());
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, BatteryCellVoltage> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, BatteryCellVoltage>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(BatteryCellVoltage.class, new HashMap<>()));
  }
}
