package com.nichesolv.nds.datacleaning.function.serde.mc;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerData;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerDataImpl;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.proto.model.MotorControllerProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;

/** Class that handles deserializing motor controller data. */
public class MotorControllerDataDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, MotorControllerData>> {
  private static final long serialVersionUID = -8370129892742162555L;

  @Override
  public Tuple3<Timestamp, Metadata, MotorControllerData> deserialize(byte[] data)
      throws IOException {
    MotorControllerProto.MotorControllerData protoc =
        MotorControllerProto.MotorControllerData.parseFrom(data);
    MotorControllerData motorControllerData = new MotorControllerDataImpl();

    if (protoc.hasDcCurrent()) {
      motorControllerData.setDcCurrent(protoc.getDcCurrent());
    }

    if (protoc.hasDcVoltage()) {
      motorControllerData.setDcVoltage(protoc.getDcVoltage());
    }

    if (protoc.hasMcsTemperature()) {
      motorControllerData.setMcsTemperature(protoc.getMcsTemperature());
    }

    if (protoc.hasMotorTemperature()) {
      motorControllerData.setMotorTemperature(protoc.getMotorTemperature());
    }

    if (protoc.hasMotorSpeed()) {
      motorControllerData.setMotorSpeed(protoc.getMotorSpeed());
    }

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
//    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
//    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
//    metadata.setMagic(protoc.getMetadata().getMagic());
//    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
//    metadata.setImei(protoc.getMetadata().getImei());
//    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
//    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, motorControllerData);
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, MotorControllerData> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, MotorControllerData>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(MotorControllerData.class, new HashMap<>()));
  }
}
