package com.nichesolv.nds.datacleaning.function.serde.mc;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.*;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.proto.model.MotorControllerProto;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;

/** Class that handles deserializing motor controller merged. */
public class MotorControllerMergedDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, MotorControllerMerged>> {
  private static final long serialVersionUID = -8370129892742162555L;

  @Override
  public Tuple3<Timestamp, Metadata, MotorControllerMerged> deserialize(byte[] data)
      throws IOException {

    MotorControllerProto.MotorController protoc =
        MotorControllerProto.MotorController.parseFrom(data);

    MotorControllerMergedImpl.MotorControllerMergedImplBuilder motorControllerMergedBuilder =
        MotorControllerMergedImpl.builder();

    if (protoc.hasData()) {
      MotorControllerProto.MotorControllerData dataProtoc = protoc.getData();
      MotorControllerDataImpl.MotorControllerDataImplBuilder motorControllerDataBuilder =
          MotorControllerDataImpl.builder();

      if (dataProtoc.hasDcCurrent()) {
        motorControllerDataBuilder.dcCurrent(dataProtoc.getDcCurrent());
      }

      if (dataProtoc.hasDcVoltage()) {
        motorControllerDataBuilder.dcVoltage(dataProtoc.getDcVoltage());
      }

      if (dataProtoc.hasMcsTemperature()) {
        motorControllerDataBuilder.mcsTemperature(dataProtoc.getMcsTemperature());
      }

      if (dataProtoc.hasMotorTemperature()) {
        motorControllerDataBuilder.motorTemperature(dataProtoc.getMotorTemperature());
      }

      if (dataProtoc.hasMotorSpeed()) {
        motorControllerDataBuilder.motorSpeed(dataProtoc.getMotorSpeed());
      }
      motorControllerMergedBuilder.motorControllerData(motorControllerDataBuilder.build());
    }

    if (protoc.hasStatus()) {
      MotorControllerProto.MotorControllerStatus motorControllerStatusProtoc = protoc.getStatus();
      MotorControllerStatusImpl.MotorControllerStatusImplBuilder motorControllerStatusBuilder =
          MotorControllerStatusImpl.builder();

      Optional.of(motorControllerStatusProtoc.getThrottlePercentage())
          .ifPresent(motorControllerStatusBuilder::throttlePercentage);
      List<MotorControllerProto.MotorStatus> motorStatuses =
          motorControllerStatusProtoc.getMotorStatusList();
      List<MotorStatus> deserializedMotorStatus =
          motorStatuses.stream()
              .map(status -> MotorStatus.of(status.getNumber()))
              .collect(Collectors.toList());
      motorControllerStatusBuilder.motorStatuses(deserializedMotorStatus);
      // Status feedback.
      StatusFeedback.StatusFeedbackBuilder statusFeedbackBuilder = StatusFeedback.builder();
      Optional.of(motorControllerStatusProtoc.getCruise()).ifPresent(statusFeedbackBuilder::cruise);
      Optional.of(motorControllerStatusProtoc.getRegeneration())
          .ifPresent(statusFeedbackBuilder::regeneration);
      Optional.of(motorControllerStatusProtoc.getReadySign())
          .ifPresent(statusFeedbackBuilder::readySign);
      Optional.of(motorControllerStatusProtoc.getPLight()).ifPresent(statusFeedbackBuilder::pLight);
      Optional.of(motorControllerStatusProtoc.getReverse())
          .ifPresent(statusFeedbackBuilder::reverse);
      Optional.of(motorControllerStatusProtoc.getVehicleBrake())
          .ifPresent(statusFeedbackBuilder::vehicleBrake);
      Optional.of(motorControllerStatusProtoc.getSideStand())
          .ifPresent(statusFeedbackBuilder::sideStand);
      Optional.of(motorControllerStatusProtoc.getDriveSelection())
          .ifPresent(
              (driveSelection) ->
                  statusFeedbackBuilder.driveSelection(DriveSelection.of(driveSelection)));
      motorControllerStatusBuilder.statusFeedback(statusFeedbackBuilder.build());

      motorControllerMergedBuilder.motorControllerStatus(motorControllerStatusBuilder.build());
    }

    if (protoc.hasDiIgnition()) {
      motorControllerMergedBuilder.diIgnition(protoc.getDiIgnition());
    }
    if (protoc.hasDiMotion()) {
      motorControllerMergedBuilder.diMotion(protoc.getDiMotion());
    }

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMetadata().getMagic());
    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
    metadata.setImei(protoc.getMetadata().getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, motorControllerMergedBuilder.build());
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, MotorControllerMerged> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, MotorControllerMerged>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(MotorControllerMerged.class, new HashMap<>()));
  }
}
