package com.nichesolv.nds.datacleaning.function.serde.mc;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.*;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.proto.model.MotorControllerProto;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;

/** Motor controller status deserializer. */
public class MotorControllerStatusDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, MotorControllerStatus>> {
  private static final long serialVersionUID = -4423254780982722961L;

  @Override
  public Tuple3<Timestamp, Metadata, MotorControllerStatus> deserialize(byte[] data)
      throws IOException {
    MotorControllerProto.MotorControllerStatus protoc =
        MotorControllerProto.MotorControllerStatus.parseFrom(data);
    MotorControllerStatusImpl.MotorControllerStatusImplBuilder builder =
        MotorControllerStatusImpl.builder();

    Optional.of(protoc.getThrottlePercentage()).ifPresent(builder::throttlePercentage);
    List<MotorControllerProto.MotorStatus> motorStatuses = protoc.getMotorStatusList();
    List<MotorStatus> deserializedMotorStatus = motorStatuses.stream()
            .map(status -> MotorStatus.of(status.getNumber()))
            .collect(Collectors.toList());
    builder.motorStatuses(deserializedMotorStatus);
    // Status feedback.
    StatusFeedback.StatusFeedbackBuilder statusFeedbackBuilder = StatusFeedback.builder();
    Optional.of(protoc.getCruise()).ifPresent(statusFeedbackBuilder::cruise);
    Optional.of(protoc.getRegeneration()).ifPresent(statusFeedbackBuilder::regeneration);
    Optional.of(protoc.getReadySign()).ifPresent(statusFeedbackBuilder::readySign);
    Optional.of(protoc.getPLight()).ifPresent(statusFeedbackBuilder::pLight);
    Optional.of(protoc.getReverse()).ifPresent(statusFeedbackBuilder::reverse);
    Optional.of(protoc.getVehicleBrake()).ifPresent(statusFeedbackBuilder::vehicleBrake);
    Optional.of(protoc.getSideStand()).ifPresent(statusFeedbackBuilder::sideStand);
    Optional.of(protoc.getDriveSelection())
        .ifPresent(
            (driveSelection) ->
                statusFeedbackBuilder.driveSelection(DriveSelection.of(driveSelection)));
    builder.statusFeedback(statusFeedbackBuilder.build());

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
//    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
//    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
//    metadata.setMagic(protoc.getMetadata().getMagic());
//    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
//    metadata.setImei(protoc.getMetadata().getImei());
//    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
//    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, builder.build());
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, MotorControllerStatus> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, MotorControllerStatus>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(MotorControllerStatus.class, new HashMap<>()));
  }
}
