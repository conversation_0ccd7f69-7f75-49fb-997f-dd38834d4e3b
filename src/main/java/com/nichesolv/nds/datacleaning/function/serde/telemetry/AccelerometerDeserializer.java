package com.nichesolv.nds.datacleaning.function.serde.telemetry;

import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import com.nichesolv.nds.model.core.ajjas.event.accelerometer.AccelerometerImpl;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.proto.model.AccelerometerProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;

public class AccelerometerDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, Accelerometer>> {

  private static final long serialVersionUID = 6439891222349753168L;

  @Override
  public Tuple3<Timestamp, Metadata, Accelerometer> deserialize(byte[] data) throws IOException {
    AccelerometerProto.Accelerometer protoc = AccelerometerProto.Accelerometer.parseFrom(data);

    Accelerometer accelerometer = new AccelerometerImpl();

    if (protoc.hasX()) {
      accelerometer.setX(protoc.getX());
    }

    if (protoc.hasY()) {
      accelerometer.setY(protoc.getY());
    }

    if (protoc.hasZ()) {
      accelerometer.setZ(protoc.getZ());
    }

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMetadata().getMagic());
    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
    metadata.setImei(protoc.getMetadata().getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, accelerometer);
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, Accelerometer> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, Accelerometer>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(Accelerometer.class, new HashMap<>()));
  }
}
