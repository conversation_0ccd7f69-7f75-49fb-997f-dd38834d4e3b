package com.nichesolv.nds.datacleaning.function.serde.telemetry;

import com.nichesolv.nds.model.core.ajjas.event.io.*;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.proto.model.AnalogInputProto;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.metrics.Counter;
import org.springframework.stereotype.Component;

/** Deserializer for IO protoc. */
@Component("analogInputDeserializationSchema")
public class AnalogInputDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, List<AnalogInput>>> {

  private static final long serialVersionUID = -2900805694830286716L;

  private transient Counter counter;

  @Override
  public void open(InitializationContext context) throws Exception {
    this.counter = context.getMetricGroup().addGroup("source").counter("AnalogInput");
  }

  private List<AnalogInput> buildAnalogInputs(AnalogInputProto.AnalogInput analogInputProto) {
    // form analogInputs.
    List<AnalogInput> analogInputs = new ArrayList<>();
    // TEMP
    analogInputs.add(
        AnalogInputImpl.builder()
            .id(AnalogInputIdentifier.TEMP.getAnalogInputIdentifier())
            .value(analogInputProto.getTemp())
            .build());

    // VIN
    analogInputs.add(
        AnalogInputImpl.builder()
            .id(AnalogInputIdentifier.VIN.getAnalogInputIdentifier())
            .value(analogInputProto.getVin())
            .build());

    // VSYS
    analogInputs.add(
        AnalogInputImpl.builder()
            .id(AnalogInputIdentifier.VSYS.getAnalogInputIdentifier())
            .value(analogInputProto.getVsys())
            .build());

    // VUSR_1
    analogInputs.add(
        AnalogInputImpl.builder()
            .id(AnalogInputIdentifier.VUSR_1.getAnalogInputIdentifier())
            .value(analogInputProto.getVusr1())
            .build());

    // VUSR_2
    analogInputs.add(
        AnalogInputImpl.builder()
            .id(AnalogInputIdentifier.VUSR_2.getAnalogInputIdentifier())
            .value(analogInputProto.getVusr2())
            .build());

    // VBUCK
    analogInputs.add(
        AnalogInputImpl.builder()
            .id(AnalogInputIdentifier.VBUCK.getAnalogInputIdentifier())
            .value(analogInputProto.getVbuck())
            .build());

    // LEAN_ANG
    analogInputs.add(
        AnalogInputImpl.builder()
            .id(AnalogInputIdentifier.LEAN_ANG.getAnalogInputIdentifier())
            .value(analogInputProto.getLeanAngle())
            .build());

    return analogInputs;
  }

  @Override
  public Tuple3<Timestamp, Metadata, List<AnalogInput>> deserialize(byte[] data)
      throws IOException {
    AnalogInputProto.AnalogInput protoc = AnalogInputProto.AnalogInput.parseFrom(data);
    List<AnalogInput> analogInputs = buildAnalogInputs(protoc);

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMetadata().getMagic());
    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
    metadata.setImei(protoc.getMetadata().getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    // This counter will report the number of analog input records coming into the data cleaning
    // pipeline.
    this.counter.inc();

    return new Tuple3<>(timestamp, metadata, analogInputs);
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, List<AnalogInput>> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, List<AnalogInput>>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(IO.class, new HashMap<>()));
  }
}
