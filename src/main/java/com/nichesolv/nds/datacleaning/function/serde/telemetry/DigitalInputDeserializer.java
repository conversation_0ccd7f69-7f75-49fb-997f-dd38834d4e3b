package com.nichesolv.nds.datacleaning.function.serde.telemetry;

import com.nichesolv.nds.model.core.ajjas.event.io.DigitalInput;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalInputImpl;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.proto.model.DigitalInputProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;

/** To deserialize digital input data. */
public class DigitalInputDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, DigitalInput>> {

  private static final long serialVersionUID = 4892896745452882026L;

  @Override
  public Tuple3<Timestamp, Metadata, DigitalInput> deserialize(byte[] data) throws IOException {
    DigitalInputProto.DigitalInput protoc = DigitalInputProto.DigitalInput.parseFrom(data);
    DigitalInputImpl.DigitalInputImplBuilder builder = DigitalInputImpl.builder();

    // Deserialize IO.
    if (protoc.hasIgnition()) {
      builder.ignition(protoc.getIgnition());
    }

    if (protoc.hasMainPower()) {
      builder.mainPower(protoc.getMainPower());
    }

    if (protoc.hasMotion()) {
      builder.motion(protoc.getMotion());
    }

    if (protoc.hasUsr1()) {
      builder.usr1(protoc.getUsr1());
    }

    if (protoc.hasUsr2()) {
      builder.usr2(protoc.getUsr2());
    }

    if (protoc.hasTamper()) {
      builder.tamper(protoc.getTamper());
    }

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMetadata().getMagic());
    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
    metadata.setImei(protoc.getMetadata().getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, builder.build());
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, DigitalInput> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, DigitalInput>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(DigitalInput.class, new HashMap<>()));
  }
}
