package com.nichesolv.nds.datacleaning.function.serde.telemetry;

import com.nichesolv.nds.model.core.ajjas.event.io.DigitalOutput;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalOutputImpl;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.proto.model.DigitalOutputProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;

public class DigitalOutputDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, DigitalOutput>> {

  private static final long serialVersionUID = 2480720396318131454L;

  @Override
  public Tuple3<Timestamp, Metadata, DigitalOutput> deserialize(byte[] data) throws IOException {
    DigitalOutputProto.DigitalOutput protoc = DigitalOutputProto.DigitalOutput.parseFrom(data);

    DigitalOutput digitalOutput = new DigitalOutputImpl();

    if (protoc.hasUsr1()) {
      digitalOutput.setUsr1(protoc.getUsr1());
    }

    if (protoc.hasUsr2()) {
      digitalOutput.setUsr2(protoc.getUsr2());
    }

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMetadata().getMagic());
    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
    metadata.setImei(protoc.getMetadata().getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, digitalOutput);
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, DigitalOutput> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, DigitalOutput>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(DigitalOutput.class, new HashMap<>()));
  }
}
