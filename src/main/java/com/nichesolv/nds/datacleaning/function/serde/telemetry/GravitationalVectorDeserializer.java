package com.nichesolv.nds.datacleaning.function.serde.telemetry;

import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVector;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVectorImpl;
import com.nichesolv.nds.model.core.ajjas.event.gyroscope.Gyroscope;
import com.nichesolv.nds.model.core.ajjas.event.gyroscope.GyroscopeImpl;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.proto.model.GravitationalVectorProto;
import com.nichesolv.nds.model.proto.model.GyroscopeProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;

public class GravitationalVectorDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, GravitationalVector>> {

  private static final long serialVersionUID = -5417394889112120262L;

  @Override
  public Tuple3<Timestamp, Metadata, GravitationalVector> deserialize(byte[] data) throws IOException {
    GravitationalVectorProto.GravitationalVector protoc = GravitationalVectorProto.GravitationalVector.parseFrom(data);

    GravitationalVector gravitationalVector = new GravitationalVectorImpl();

    if (protoc.hasX()) {
      gravitationalVector.setX(protoc.getX());
    }

    if (protoc.hasY()) {
      gravitationalVector.setY(protoc.getY());
    }

    if (protoc.hasZ()) {
      gravitationalVector.setZ(protoc.getZ());
    }

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMetadata().getMagic());
    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
    metadata.setImei(protoc.getMetadata().getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, gravitationalVector);
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, GravitationalVector> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, GravitationalVector>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(Gyroscope.class, new HashMap<>()));
  }
}
