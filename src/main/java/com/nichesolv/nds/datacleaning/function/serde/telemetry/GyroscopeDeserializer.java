package com.nichesolv.nds.datacleaning.function.serde.telemetry;

import com.nichesolv.nds.model.core.ajjas.event.gyroscope.Gyroscope;
import com.nichesolv.nds.model.core.ajjas.event.gyroscope.GyroscopeImpl;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import java.io.IOException;
import java.util.HashMap;

import com.nichesolv.nds.model.proto.model.GyroscopeProto;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
public class GyroscopeDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, Gyroscope>> {

  private static final long serialVersionUID = 6439891222349753168L;

  @Override
  public Tuple3<Timestamp, Metadata, Gyroscope> deserialize(byte[] data) throws IOException {
    GyroscopeProto.Gyroscope protoc = GyroscopeProto.Gyroscope.parseFrom(data);

    Gyroscope gyroscope = new GyroscopeImpl();

    if (protoc.hasX()) {
      gyroscope.setX(protoc.getX());
    }

    if (protoc.hasY()) {
      gyroscope.setY(protoc.getY());
    }

    if (protoc.hasZ()) {
      gyroscope.setZ(protoc.getZ());
    }

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMetadata().getMagic());
    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
    metadata.setImei(protoc.getMetadata().getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, gyroscope);
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, Gyroscope> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, Gyroscope>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(Gyroscope.class, new HashMap<>()));
  }
}
