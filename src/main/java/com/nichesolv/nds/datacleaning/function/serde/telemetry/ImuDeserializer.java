package com.nichesolv.nds.datacleaning.function.serde.telemetry;

import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import com.nichesolv.nds.model.core.ajjas.event.accelerometer.AccelerometerImpl;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVector;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVectorImpl;
import com.nichesolv.nds.model.core.ajjas.event.gyroscope.Gyroscope;
import com.nichesolv.nds.model.core.ajjas.event.gyroscope.GyroscopeImpl;
import com.nichesolv.nds.model.core.ajjas.event.io.*;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.domain.Telemetry;
import com.nichesolv.nds.model.domain.TelemetryImpl;
import com.nichesolv.nds.model.proto.model.ImuProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;

public class ImuDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, Telemetry>> {

  private static final long serialVersionUID = -5503662402881397662L;

  @Override
  public Tuple3<Timestamp, Metadata, Telemetry> deserialize(byte[] data) throws IOException {
    ImuProto.Imu protoc = ImuProto.Imu.parseFrom(data);
    Telemetry telemetry = new TelemetryImpl();

    if(protoc.hasDigitalInput()){
      DigitalInput digitalInput = getDigitalInput(protoc);

      telemetry.setDigitalInput(digitalInput);
    }



    if(protoc.hasAccelerometer()){
      ImuProto.Imu.Accelerometer accelProto = protoc.getAccelerometer();
      Accelerometer accelerometer = new AccelerometerImpl();
      if (accelProto.hasX()) {
        accelerometer.setX(accelProto.getX());
      }

      if (accelProto.hasY()) {
        accelerometer.setY(accelProto.getY());
      }

      if (accelProto.hasZ()) {
        accelerometer.setZ(accelProto.getZ());
      }

      telemetry.setAccelerometer(accelerometer);
    }

    if(protoc.hasGyroscope()){
      ImuProto.Imu.Gyroscope gyroProto = protoc.getGyroscope();
      Gyroscope gyroscope = new GyroscopeImpl();
      if (gyroProto.hasX()) {
        gyroscope.setX(gyroProto.getX());
      }

      if (gyroProto.hasY()) {
        gyroscope.setY(gyroProto.getY());
      }

      if (gyroProto.hasZ()) {
        gyroscope.setZ(gyroProto.getZ());
      }

      telemetry.setGyroscope(gyroscope);
    }

    if(protoc.hasGravitationalVector()){
      ImuProto.Imu.GravitationalVector grvProto = protoc.getGravitationalVector();
      GravitationalVector gravitationalVector = new GravitationalVectorImpl();
      if (grvProto.hasX()) {
        gravitationalVector.setX(grvProto.getX());
      }

      if (grvProto.hasY()) {
        gravitationalVector.setY(grvProto.getY());
      }

      if (grvProto.hasZ()) {
        gravitationalVector.setZ(grvProto.getZ());
      }

      telemetry.setGravitationalVector(gravitationalVector);
    }

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMetadata().getMagic());
    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
    metadata.setImei(protoc.getMetadata().getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, telemetry);
  }

  private static DigitalInput getDigitalInput(ImuProto.Imu protoc) {
    ImuProto.Imu.DigitalInput digitalInputProto = protoc.getDigitalInput();
    DigitalInput digitalInput = new DigitalInputImpl();
    if (digitalInputProto.hasIgnition()) {
      digitalInput.setIgnition(digitalInputProto.getIgnition());
    }
    if (digitalInputProto.hasMotion()) {
      digitalInput.setMotion(digitalInputProto.getMotion());
    }
    return digitalInput;
  }


  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, Telemetry> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, Telemetry>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(Telemetry.class, new HashMap<>()));
  }
}
