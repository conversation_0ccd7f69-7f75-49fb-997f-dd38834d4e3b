package com.nichesolv.nds.datacleaning.function.serde.telemetry;

import com.nichesolv.nds.model.core.ajjas.event.location.Location;
import com.nichesolv.nds.model.core.ajjas.event.location.LocationImpl;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.typeinfo.TimestampTypeInfo;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.proto.model.LocationProto;
import java.io.IOException;
import java.util.HashMap;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;

/** Location proto deserializer. */
public class LocationProtocDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, Location>> {
  private static final long serialVersionUID = -8841073564065984008L;

  @Override
  public Tuple3<Timestamp, Metadata, Location> deserialize(byte[] data) throws IOException {
    LocationProto.Location protoc = LocationProto.Location.parseFrom(data);
    Location location =
        LocationImpl.builder()
            .latitude(protoc.getLatitude())
            .longitude(protoc.getLongitude())
            .altitude(protoc.getAltitude())
            .speed(protoc.getSpeed())
            .bearing(protoc.getBearing())
            .pdop(protoc.getPdop())
            .hdop(protoc.getHdop())
            .vdop(protoc.getVdop())
            .viewSats(protoc.getViewSats())
            .trackSats(protoc.getTrackSats())
            .build();

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMetadata().getMagic());
    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
    metadata.setImei(protoc.getMetadata().getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, location);
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, Location> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, Location>> getProducedType() {
    return Types.TUPLE(
        TimestampTypeInfo.getTypeInfo(),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(Location.class, new HashMap<>()));
  }
}
