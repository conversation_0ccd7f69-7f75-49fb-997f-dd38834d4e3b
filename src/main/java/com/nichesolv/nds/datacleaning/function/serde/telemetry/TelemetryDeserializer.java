package com.nichesolv.nds.datacleaning.function.serde.telemetry;

import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import com.nichesolv.nds.model.core.ajjas.event.accelerometer.AccelerometerImpl;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVector;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVectorImpl;
import com.nichesolv.nds.model.core.ajjas.event.gyroscope.Gyroscope;
import com.nichesolv.nds.model.core.ajjas.event.gyroscope.GyroscopeImpl;
import com.nichesolv.nds.model.core.ajjas.event.io.*;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.domain.Telemetry;
import com.nichesolv.nds.model.domain.TelemetryImpl;
import java.io.IOException;
import java.util.HashMap;

import com.nichesolv.nds.model.proto.model.TelemetryProto;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;

public class TelemetryDeserializer
    implements DeserializationSchema<Tuple3<Timestamp, Metadata, Telemetry>> {

  private static final long serialVersionUID = -5503662402881997662L;

  @Override
  public Tuple3<Timestamp, Metadata, Telemetry> deserialize(byte[] data) throws IOException {
    TelemetryProto.Telemetry protoc = TelemetryProto.Telemetry.parseFrom(data);
    Telemetry telemetry = new TelemetryImpl();
    if(protoc.hasAnalogInput()){
      AnalogInputParsed analogInput = getAnalogInputParsed(protoc);

      telemetry.setAnalogInput(analogInput);
    }

    if(protoc.hasDigitalInput()){
      DigitalInput digitalInput = getDigitalInput(protoc);

      telemetry.setDigitalInput(digitalInput);
    }

    if(protoc.hasDigitalOutput()){
      TelemetryProto.Telemetry.DigitalOutput digitalOutputProto = protoc.getDigitalOutput();
      DigitalOutput digitalOutput = new DigitalOutputImpl();
      if (digitalOutputProto.hasUsr1()) {
        digitalOutput.setUsr1(digitalOutputProto.getUsr1());
      }

      if (digitalOutputProto.hasUsr2()) {
        digitalOutput.setUsr2(digitalOutputProto.getUsr2());
      }

      telemetry.setDigitalOutput(digitalOutput);
    }

    if(protoc.hasAccelerometer()){
      TelemetryProto.Telemetry.Accelerometer accelProto = protoc.getAccelerometer();
      Accelerometer accelerometer = new AccelerometerImpl();
      if (accelProto.hasX()) {
        accelerometer.setX(accelProto.getX());
      }

      if (accelProto.hasY()) {
        accelerometer.setY(accelProto.getY());
      }

      if (accelProto.hasZ()) {
        accelerometer.setZ(accelProto.getZ());
      }

      telemetry.setAccelerometer(accelerometer);
    }

    if(protoc.hasGyroscope()){
      TelemetryProto.Telemetry.Gyroscope gyroProto = protoc.getGyroscope();
      Gyroscope gyroscope = new GyroscopeImpl();
      if (gyroProto.hasX()) {
        gyroscope.setX(gyroProto.getX());
      }

      if (gyroProto.hasY()) {
        gyroscope.setY(gyroProto.getY());
      }

      if (gyroProto.hasZ()) {
        gyroscope.setZ(gyroProto.getZ());
      }

      telemetry.setGyroscope(gyroscope);
    }

    if(protoc.hasGravitationalVector()){
      TelemetryProto.Telemetry.GravitationalVector grvProto = protoc.getGravitationalVector();
      GravitationalVector gravitationalVector = new GravitationalVectorImpl();
      if (grvProto.hasX()) {
        gravitationalVector.setX(grvProto.getX());
      }

      if (grvProto.hasY()) {
        gravitationalVector.setY(grvProto.getY());
      }

      if (grvProto.hasZ()) {
        gravitationalVector.setZ(grvProto.getZ());
      }

      telemetry.setGravitationalVector(gravitationalVector);
    }

    // Timestamp.
    Timestamp timestamp = new TimestampImpl();
    timestamp.setTimestamp(protoc.getTimestamp().getObservedTimestamp());
    timestamp.setIngestionTime(protoc.getTimestamp().getIngestionTimestamp());

    // Metadata.
    Metadata metadata = new MetadataImpl();
    metadata.setMagic(protoc.getMetadata().getMagic());
    metadata.setCorrelationId(protoc.getMetadata().getCorrelationId());
    metadata.setImei(protoc.getMetadata().getImei());
    metadata.setCrc16(Integer.valueOf(protoc.getMetadata().getCrc16()).shortValue());
    metadata.setSqn(Integer.valueOf(protoc.getMetadata().getSqn()).shortValue());

    return new Tuple3<>(timestamp, metadata, telemetry);
  }

  private static DigitalInput getDigitalInput(TelemetryProto.Telemetry protoc) {
    TelemetryProto.Telemetry.DigitalInput digitalInputProto = protoc.getDigitalInput();
    DigitalInput digitalInput = new DigitalInputImpl();
    if (digitalInputProto.hasIgnition()) {
      digitalInput.setIgnition(digitalInputProto.getIgnition());
    }

    if (digitalInputProto.hasMainPower()) {
      digitalInput.setMainPower(digitalInputProto.getMainPower());
    }

    if (digitalInputProto.hasMotion()) {
      digitalInput.setMotion(digitalInputProto.getMotion());
    }

    if (digitalInputProto.hasTamper()) {
      digitalInput.setTamper(digitalInputProto.getTamper());
    }

    if (digitalInputProto.hasUsr1()) {
      digitalInput.setUsr1(digitalInputProto.getUsr2());
    }

    if (digitalInputProto.hasUsr2()) {
      digitalInput.setUsr2(digitalInputProto.getUsr2());
    }
    return digitalInput;
  }

  private static AnalogInputParsed getAnalogInputParsed(TelemetryProto.Telemetry protoc) {
    TelemetryProto.Telemetry.AnalogInput analogInputProto = protoc.getAnalogInput();
    AnalogInputParsed analogInput = new AnalogInputParsedImpl();
    if (analogInputProto.hasTemp()) {
      analogInput.setTemp(analogInputProto.getTemp());
    }

    if (analogInputProto.hasVin()) {
      analogInput.setVin(analogInputProto.getVin());
    }

    if (analogInputProto.hasVsys()) {
      analogInput.setVsys(analogInputProto.getVsys());
    }

    if (analogInputProto.hasVbuck()) {
      analogInput.setVbuck(analogInputProto.getVbuck());
    }

    if (analogInputProto.hasVusr1()) {
      analogInput.setVusr1(analogInputProto.getVusr1());
    }

    if (analogInputProto.hasVusr2()) {
      analogInput.setVusr2(analogInputProto.getVusr2());
    }

    if (analogInputProto.hasLeanAngle()) {
      analogInput.setLeanAngle(analogInputProto.getLeanAngle());
    }
    return analogInput;
  }

  @Override
  public boolean isEndOfStream(Tuple3<Timestamp, Metadata, Telemetry> nextElement) {
    return false;
  }

  @Override
  public TypeInformation<Tuple3<Timestamp, Metadata, Telemetry>> getProducedType() {
    return Types.TUPLE(
        Types.POJO(Timestamp.class, new HashMap<>()),
        Types.POJO(Metadata.class, new HashMap<>()),
        Types.POJO(Telemetry.class, new HashMap<>()));
  }
}
