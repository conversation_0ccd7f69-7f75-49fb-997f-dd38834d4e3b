package com.nichesolv.nds.datacleaning.function.sink.csv;

import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInput;
import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInputIdentifier;
import com.nichesolv.nds.model.core.ajjas.event.io.IO;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.util.List;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.springframework.stereotype.Component;

@Component
public class AnalogInputCsvWriter {

  public void write(
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, IO>> dataStream,
      String path,
      String outputTableName,
      String inputTableName) {

    // Create table
    String createTable =
        "CREATE TABLE IF NOT EXISTS "
            + outputTableName
            + " (`timestamp` INT, "
            + "correlation_id STRING, "
            + "temperature INT, "
            + "input_voltage INT) WITH "
            + "('connector' = 'filesystem', "
            + "'path' = '"
            + path
            + "', "
            + "'format' = 'csv', "
            + "'csv.ignore-parse-errors' = 'true', "
            + "'sink.rolling-policy.file-size' = '10MB', "
            + "'sink.rolling-policy.rollover-interval' = '5 min'"
            + ")";

    // Mapped to a reasonable type.
    String[] fieldNames =
        new String[] {"timestamp", "correlation_id", "temperature", "voltage_input"};
    DataStream<Row> tableMapped =
        dataStream
            .map(new SimpleRowMapper())
            .setParallelism(1)
            .returns(Types.ROW_NAMED(fieldNames, Types.INT, Types.STRING, Types.INT, Types.INT));

    // Let's create the table.
    tableEnv.executeSql(createTable);

    // Create a statement set, this will help us insert into the table created above easily.
    // StatementSet statementSet = tableEnv.createStatementSet();

    // Create the insert statement.
    // Now let's insert into the table.
    tableEnv.createTemporaryView(inputTableName, tableMapped);
    String insertStatement =
        "INSERT INTO "
            + outputTableName
            + " SELECT `timestamp`, correlation_id, temperature, voltage_input FROM "
            + inputTableName;
    // statementSet.addInsertSql(insertStatement);
    // Execute the insert statement.
    // statementSet.execute();
    tableEnv.executeSql(insertStatement);
  }

  // Maps transformed values to row that can be used in table api to output a csv file.
  public static class SimpleRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, IO>, Row> {
    private static final long serialVersionUID = -7516892233623765201L;

    @Override
    public Row map(Tuple3<Timestamp, Metadata, IO> value) throws Exception {
      IO io = value.f2;
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;

      // All analog inputs.
      List<AnalogInput> analogInputs = io.getAnalogInputs();
      // Corrected values.
      // Temperature
      AnalogInput temperature =
          analogInputs.stream()
              .filter(
                  (aInput) ->
                      aInput.getId() == AnalogInputIdentifier.TEMP.getAnalogInputIdentifier())
              .findFirst()
              .orElse(null);

      // Input voltage
      AnalogInput inputVoltage =
          analogInputs.stream()
              .filter(
                  (aInput) ->
                      aInput.getId() == AnalogInputIdentifier.VIN.getAnalogInputIdentifier())
              .findFirst()
              .orElse(null);

      return Row.of(
          timestamp.getTimestamp(),
          metadata.getCorrelationId(),
          temperature != null ? temperature.getValue() : 0,
          inputVoltage != null ? inputVoltage.getValue() : 0);
    }
  }
}
