package com.nichesolv.nds.datacleaning.function.sink.timescale;

import java.io.Serializable;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public abstract class AbstractWriter<T> implements Serializable {

  private static final long serialVersionUID = -3851423446131771504L;
  @Autowired private JdbcWriter jdbcWriter;

  protected void writer(
      DataStream<T> dataStream, String sql, JdbcStatementBuilder<T> statementBuilder) {
    this.jdbcWriter.write(dataStream, sql, statementBuilder);
  }

  protected void writer(
      DataStream<T> dataStream, String sql, JdbcStatementBuilder<T> statementBuilder, String name) {
    this.jdbcWriter.write(dataStream, sql, statementBuilder, name);
  }

  public abstract String getOperatorName();

  public abstract String getTableName();

  public abstract String getSql(boolean isError);

  public abstract void save(DataStream<T> dataStream, boolean isLate, boolean isError, boolean isRaw);

  public abstract void save(DataStream<T> dataStream, String insertStatement);

  public abstract void save(DataStream<T> dataStream, String insertStatement, String operatorName);
}
