package com.nichesolv.nds.datacleaning.function.sink.timescale;

import java.math.BigDecimal;
import java.sql.Types;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;

import com.nichesolv.nds.datacleaning.function.util.PreparedStatementSetNull;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 *
 *
 * <pre>
 * postgres=# \d+ vehicle_telemetry_data;
 *                                                    Table "public.vehicle_telemetry_data"
 *         Column         |            Type             | Collation | Nullable | Default | Storage  | Compression | Stats target | Description
 * -----------------------+-----------------------------+-----------+----------+---------+----------+-------------+--------------+-------------
 *  imei                  | text                        |           | not null |         | extended |             |              |
 *  timestamp             | timestamp(6) with time zone |           | not null |         | plain    |             |              |
 *  accel_x_axis          | real                        |           |          |         | plain    |             |              |
 *  accel_y_axis          | real                        |           |          |         | plain    |             |              |
 *  accel_z_axis          | real                        |           |          |         | plain    |             |              |
 *  ai_lean_angle         | integer                     |           |          |         | plain    |             |              |
 *  ai_system_voltage     | real                        |           |          |         | plain    |             |              |
 *  ai_temperature        | real                        |           |          |         | plain    |             |              |
 *  ai_vbuck              | real                        |           |          |         | plain    |             |              |
 *  ai_voltage_input      | real                        |           |          |         | plain    |             |              |
 *  ai_vusr1              | integer                     |           |          |         | plain    |             |              |
 *  ai_vusr2              | integer                     |           |          |         | plain    |             |              |
 *  co_relation_id        | character varying(255)      |           |          |         | extended |             |              |
 *  created_on            | timestamp(6) with time zone |           |          |         | plain    |             |              |
 *  di_ignition           | boolean                     |           |          |         | plain    |             |              |
 *  di_main_power         | boolean                     |           |          |         | plain    |             |              |
 *  di_motion             | boolean                     |           |          |         | plain    |             |              |
 *  di_tamper             | boolean                     |           |          |         | plain    |             |              |
 *  di_usr1               | boolean                     |           |          |         | plain    |             |              |
 *  di_usr2               | boolean                     |           |          |         | plain    |             |              |
 *  do_usr1               | boolean                     |           |          |         | plain    |             |              |
 *  do_usr2               | boolean                     |           |          |         | plain    |             |              |
 *  gyro_x_axis           | real                        |           |          |         | plain    |             |              |
 *  gyro_y_axis           | real                        |           |          |         | plain    |             |              |
 *  gyro_z_axis           | real                        |           |          |         | plain    |             |              |
 *  motor_brake           | boolean                     |           |          |         | plain    |             |              |
 *  motor_cruise          | boolean                     |           |          |         | plain    |             |              |
 *  motor_dc_current      | real                        |           |          |         | plain    |             |              |
 *  motor_dc_voltage      | real                        |           |          |         | plain    |             |              |
 *  motor_driving_mode    | character varying(255)      |           |          |         | extended |             |              |
 *  motor_fault_feedback  | character varying(255)      |           |          |         | extended |             |              |
 *  motor_mcs_temperature | real                        |           |          |         | plain    |             |              |
 *  motor_parking_sign    | boolean                     |           |          |         | plain    |             |              |
 *  motor_ready_sign      | boolean                     |           |          |         | plain    |             |              |
 *  motor_regeneration    | boolean                     |           |          |         | plain    |             |              |
 *  motor_reverse         | boolean                     |           |          |         | plain    |             |              |
 *  motor_side_stand      | boolean                     |           |          |         | plain    |             |              |
 *  motor_speed           | real                        |           |          |         | plain    |             |              |
 *  motor_temperature     | real                        |           |          |         | plain    |             |              |
 *  motor_throttle        | real                        |           |          |         | plain    |             |              |
 *  packet_received_on    | timestamp(6) with time zone |           |          |         | plain    |             |              |
 *  mfr_org_id            | bigint                      |           |          |         | plain    |             |              |
 *  owner_org             | bigint                      |           |          |         | plain    |             |              |
 *  vehicle_id            | bigint                      |           |          |         | plain    |             |              |
 *  vehicle_model_id      | bigint                      |           |          |         | plain    |             |              |
 * Indexes:
 *     "vehicle_telemetry_data_idx" btree (vehicle_id)
 *     "vehicle_telemetry_data_imei_idx" btree (imei)
 *     "vehicle_telemetry_data_org_idx" btree (owner_org)
 *     "vehicle_telemetry_data_ts_idx" btree ("timestamp")
 * Triggers:
 *     ts_insert_blocker BEFORE INSERT ON vehicle_telemetry_data FOR EACH ROW EXECUTE FUNCTION _timescaledb_internal.insert_blocker()
 * Child tables: _timescaledb_internal._hyper_3_100_chunk,
 *               _timescaledb_internal._hyper_3_104_chunk,
 *               _timescaledb_internal._hyper_3_109_chunk,
 *               _timescaledb_internal._hyper_3_110_chunk,
 *               _timescaledb_internal._hyper_3_112_chunk,
 *               _timescaledb_internal._hyper_3_2_chunk,
 *               _timescaledb_internal._hyper_3_4501_chunk,
 *               _timescaledb_internal._hyper_3_4506_chunk,
 *               _timescaledb_internal._hyper_3_4507_chunk,
 *               _timescaledb_internal._hyper_3_4508_chunk,
 *               _timescaledb_internal._hyper_3_4511_chunk,
 *               _timescaledb_internal._hyper_3_4513_chunk,
 *               _timescaledb_internal._hyper_3_4517_chunk,
 *               _timescaledb_internal._hyper_3_97_chunk
 * Access method: heap
 * </pre>
 */
@Component("analogInputWriterV2")
public class AnalogInputWriter extends TelemetryWriter {

  private static final long serialVersionUID = -6236323803392717116L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:writer/analog_input_writer.sql')}")
  private String sql;

  private final String tableName = "vehicle_telemetry_data";

  @Override
  public String getOperatorName() {
    return "analog_input_writer";
  }

  @Override
  public String getTableName() {
    return tableName;
  }

  @Override
  public String getSql(boolean isError) {
    return sql;
  }

  @Override
  public void save(DataStream<Row> dataStream, String insertStatement, String operatorSuffix) {
    JdbcStatementBuilder<Row> jdbcStatementBuilder =
        (preparedStatement, row) -> {
          long imei = row.getFieldAs("imei");
          int observedTimestamp = row.getFieldAs("timestamp");
          long ingestionTimestamp = row.getFieldAs("ingestion_timestamp");
          Instant observedTime = Instant.ofEpochSecond(observedTimestamp);
          Instant ingestionTime = Instant.ofEpochMilli(ingestionTimestamp);
          if (observedTime.isAfter(ingestionTime))
            observedTime = ingestionTime.truncatedTo(ChronoUnit.SECONDS);
          preparedStatement.setString(1, String.valueOf(imei)); // imei
          preparedStatement.setTimestamp(2, java.sql.Timestamp.from(observedTime)); // timestamp
          preparedStatement.setObject(3, UUID.fromString(row.getFieldAs("correlation_id"))); // co_relation_id
          preparedStatement.setTimestamp(4, java.sql.Timestamp.from(Instant.now())); // created_on
          preparedStatement.setTimestamp(
              5,
              java.sql.Timestamp.from(
                  Instant.ofEpochMilli(ingestionTimestamp))); // packet_received_on
          preparedStatement.setLong(6, row.getFieldAs("mfr_org_id")); // mfr_org_id
          preparedStatement.setLong(7, row.getFieldAs("owner_org_id")); // owner_org
          preparedStatement.setLong(8, row.getFieldAs("vehicle_id")); // vehicle_id

          //  Analog Input
          PreparedStatementSetNull.setIntOrNull(
              preparedStatement, 9, row.getFieldAs("ai_lean_angle")); // lean_angle
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 10, row.getFieldAs("ai_vsys")); // vsys
          PreparedStatementSetNull.setFloatOrNull(preparedStatement, 11, row.getFieldAs("ai_temp"));
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 12, row.getFieldAs("ai_vbuck")); // vbuck
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 13, row.getFieldAs("ai_vin")); // vin
          PreparedStatementSetNull.setIntOrNull(
              preparedStatement, 14, row.getFieldAs("ai_vusr1")); // vusr_1
          PreparedStatementSetNull.setIntOrNull(
              preparedStatement, 15, row.getFieldAs("ai_vusr2")); // vusr_2
        };

    this.writer(
        dataStream, insertStatement, jdbcStatementBuilder, getOperatorName() + operatorSuffix);
  }
}
