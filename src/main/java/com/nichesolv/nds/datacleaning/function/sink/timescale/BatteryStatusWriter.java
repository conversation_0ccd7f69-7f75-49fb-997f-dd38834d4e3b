package com.nichesolv.nds.datacleaning.function.sink.timescale;

import com.nichesolv.nds.datacleaning.function.util.PreparedStatementSetNull;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("batteryStatusWriter")
public class BatteryStatusWriter extends TelemetryWriter {

  private static final long serialVersionUID = 5310111386523105499L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:writer/battery_status_writer.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:writer/battery_status_error_writer.sql')}")
  private String errorSql;

  private final String tableName = "vehicle_battery_data";

  @Override
  public String getOperatorName() {
    return "battery_status_writer";
  }

  @Override
  public String getTableName() {
    return tableName;
  }

  @Override
  public String getSql(boolean isError) {
    return isError ? errorSql : sql;
  }

  @Override
  public void save(DataStream<Row> dataStream, String insertStatement, String operatorSuffix) {
    JdbcStatementBuilder<Row> jdbcStatementBuilder =
        (preparedStatement, row) -> {
          long imei = row.getFieldAs("imei");
          int observedTimestamp = row.getFieldAs("timestamp");
          long ingestionTimestamp = row.getFieldAs("ingestion_timestamp");
          Instant observedTime = Instant.ofEpochSecond(observedTimestamp);
          Instant ingestionTime = Instant.ofEpochMilli(ingestionTimestamp);
          if (observedTime.isAfter(ingestionTime))
            observedTime = ingestionTime.truncatedTo(ChronoUnit.SECONDS);
          preparedStatement.setString(1, String.valueOf(imei)); // imei
          preparedStatement.setTimestamp(2, java.sql.Timestamp.from(observedTime)); // timestamp
          preparedStatement.setObject(
              3, UUID.fromString(row.getFieldAs("correlation_id"))); // co_relation_id
          preparedStatement.setTimestamp(4, java.sql.Timestamp.from(Instant.now())); // created_on
          preparedStatement.setTimestamp(
              5, java.sql.Timestamp.from(ingestionTime)); // packet_received_on
          preparedStatement.setLong(6, row.getFieldAs("mfr_org_id")); // mfr_org_id
          preparedStatement.setLong(7, row.getFieldAs("owner_org_id")); // owner_org
          preparedStatement.setLong(8, row.getFieldAs("vehicle_id")); // vehicle_id

          // battery_volt, REAL
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 9, row.getFieldAs("battery_volt"));

          // chg_cycle_count, INTEGER
          PreparedStatementSetNull.setIntOrNull(
              preparedStatement, 10, row.getFieldAs("chg_cycle_count"));

          // current, REAL
          PreparedStatementSetNull.setFloatOrNull(preparedStatement, 11, row.getFieldAs("current"));

          // dsg_cycle_count, INTEGER
          PreparedStatementSetNull.setIntOrNull(
              preparedStatement, 12, row.getFieldAs("dsg_cycle_count"));

          // soc, REAL
          PreparedStatementSetNull.setFloatOrNull(preparedStatement, 13, row.getFieldAs("soc"));
          // soh, REAL
          PreparedStatementSetNull.setFloatOrNull(preparedStatement, 14, row.getFieldAs("soh"));

          // remaining_capacity, REAL
          PreparedStatementSetNull.setIntOrNull(
              preparedStatement, 15, row.getFieldAs("remaining_capacity"));

          // mosfet_temperature, REAL
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 16, row.getFieldAs("mosfet_temperature"));

          if (!operatorSuffix.contains("_error")) {
            PreparedStatementSetNull.setFloatOrNull(
                preparedStatement, 17, row.getFieldAs("discharge"));
          }
        };
    this.writer(
        dataStream, insertStatement, jdbcStatementBuilder, getOperatorName() + operatorSuffix);
  }
}
