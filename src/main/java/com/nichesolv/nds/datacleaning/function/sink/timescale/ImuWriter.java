package com.nichesolv.nds.datacleaning.function.sink.timescale;

import com.nichesolv.nds.datacleaning.function.util.PreparedStatementSetNull;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("imuWriterV2")
public class ImuWriter extends TelemetryWriter {

  private static final long serialVersionUID = 2776340125505454113L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:writer/imu_writer.sql')}")
  private String sql;

  private final String tableName="vehicle_imu_data";

  @Override
  public String getOperatorName() {
    return "imu_writer";
  }

  @Override
  public String getTableName() {
    return tableName;
  }

  @Override
  public String getSql(boolean isError) {
    return sql;
  }

  @Override
  public void save(DataStream<Row> dataStream, String insertStatement, String operatorSuffix) {
    JdbcStatementBuilder<Row> jdbcStatementBuilder =
        (preparedStatement, row) -> {
          long imei = row.getFieldAs("imei");
          int observedTimestamp = row.getFieldAs("timestamp");
          long ingestionTimestamp = row.getFieldAs("ingestion_timestamp");
          Instant observedTime = Instant.ofEpochSecond(observedTimestamp);
          Instant ingestionTime = Instant.ofEpochMilli(ingestionTimestamp);
          if (observedTime.isAfter(ingestionTime))
            observedTime = ingestionTime.truncatedTo(ChronoUnit.SECONDS);
          preparedStatement.setString(1, String.valueOf(imei)); // imei
          preparedStatement.setTimestamp(2, java.sql.Timestamp.from(observedTime)); // timestamp
          preparedStatement.setObject(3, UUID.fromString(row.getFieldAs("correlation_id"))); // co_relation_id
          preparedStatement.setTimestamp(4, java.sql.Timestamp.from(Instant.now())); // created_on
          preparedStatement.setTimestamp(
              5, java.sql.Timestamp.from(ingestionTime)); // packet_received_on
          preparedStatement.setLong(6, row.getFieldAs("mfr_org_id")); // mfr_org_id
          preparedStatement.setLong(7, row.getFieldAs("owner_org_id")); // owner_org
          preparedStatement.setLong(8, row.getFieldAs("vehicle_id")); // vehicle_id

          // Accelerometer
            PreparedStatementSetNull.setFloatOrNull(preparedStatement,9, row.getFieldAs("accel_x")); //
          // accel_x_axis
            PreparedStatementSetNull.setFloatOrNull(preparedStatement,10, row.getFieldAs("accel_y")); //
          // accel_y_axis
            PreparedStatementSetNull.setFloatOrNull(preparedStatement,11, row.getFieldAs("accel_z")); //
          // accel_z_axis


            // Digital Input
            PreparedStatementSetNull.setBoolOrNull(preparedStatement,12, row.getFieldAs("di_ignition")); // di_ignition
            PreparedStatementSetNull.setBoolOrNull(preparedStatement,13, row.getFieldAs("di_motion")); // di_motion

            // Gyro
            PreparedStatementSetNull.setFloatOrNull(
                    preparedStatement,14, row.getFieldAs("gyro_x")); // gyro_x_axis
            PreparedStatementSetNull.setFloatOrNull(
                    preparedStatement,15, row.getFieldAs("gyro_y")); // gyro_y_axis
            PreparedStatementSetNull.setFloatOrNull(
                    preparedStatement,16, row.getFieldAs("gyro_z")); // gyro_z_axis

            // Gravitational vector
            PreparedStatementSetNull.setFloatOrNull(
                    preparedStatement, 17, row.getFieldAs("grv_x")); // grv_x_axis
            PreparedStatementSetNull.setFloatOrNull(
                    preparedStatement, 18, row.getFieldAs("grv_y")); // grv_y_axis
            PreparedStatementSetNull.setFloatOrNull(
                    preparedStatement, 19, row.getFieldAs("grv_z")); // grv_z_axis

            PreparedStatementSetNull.setIntOrNull(
                    preparedStatement, 20, row.getFieldAs("ai_lean_angle"));
        };

    this.writer(
        dataStream, insertStatement, jdbcStatementBuilder, this.getOperatorName() + operatorSuffix);
  }
}
