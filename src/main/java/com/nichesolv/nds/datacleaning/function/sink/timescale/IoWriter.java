//package com.nichesolv.nds.datacleaning.function.sink.timescale;
//
//import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
//import java.sql.Types;
//import java.time.Instant;
//import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
//import org.apache.flink.streaming.api.datastream.DataStream;
//import org.apache.flink.types.Row;
//import org.springframework.stereotype.Component;
//
///**
// *
// *
// * <pre>
// * postgres=# \d+ vehicle_telemetry_data;
// *                                                    Table "public.vehicle_telemetry_data"
// *         Column         |            Type             | Collation | Nullable | Default | Storage  | Compression | Stats target | Description
// * -----------------------+-----------------------------+-----------+----------+---------+----------+-------------+--------------+-------------
// *  imei                  | text                        |           | not null |         | extended |             |              |
// *  timestamp             | timestamp(6) with time zone |           | not null |         | plain    |             |              |
// *  accel_x_axis          | real                        |           |          |         | plain    |             |              |
// *  accel_y_axis          | real                        |           |          |         | plain    |             |              |
// *  accel_z_axis          | real                        |           |          |         | plain    |             |              |
// *  ai_lean_angle         | integer                     |           |          |         | plain    |             |              |
// *  ai_system_voltage     | real                        |           |          |         | plain    |             |              |
// *  ai_temperature        | real                        |           |          |         | plain    |             |              |
// *  ai_vbuck              | real                        |           |          |         | plain    |             |              |
// *  ai_voltage_input      | real                        |           |          |         | plain    |             |              |
// *  ai_vusr1              | integer                     |           |          |         | plain    |             |              |
// *  ai_vusr2              | integer                     |           |          |         | plain    |             |              |
// *  co_relation_id        | character varying(255)      |           |          |         | extended |             |              |
// *  created_on            | timestamp(6) with time zone |           |          |         | plain    |             |              |
// *  di_ignition           | boolean                     |           |          |         | plain    |             |              |
// *  di_main_power         | boolean                     |           |          |         | plain    |             |              |
// *  di_motion             | boolean                     |           |          |         | plain    |             |              |
// *  di_tamper             | boolean                     |           |          |         | plain    |             |              |
// *  di_usr1               | boolean                     |           |          |         | plain    |             |              |
// *  di_usr2               | boolean                     |           |          |         | plain    |             |              |
// *  do_usr1               | boolean                     |           |          |         | plain    |             |              |
// *  do_usr2               | boolean                     |           |          |         | plain    |             |              |
// *  gyro_x_axis           | real                        |           |          |         | plain    |             |              |
// *  gyro_y_axis           | real                        |           |          |         | plain    |             |              |
// *  gyro_z_axis           | real                        |           |          |         | plain    |             |              |
// *  motor_brake           | boolean                     |           |          |         | plain    |             |              |
// *  motor_cruise          | boolean                     |           |          |         | plain    |             |              |
// *  motor_dc_current      | real                        |           |          |         | plain    |             |              |
// *  motor_dc_voltage      | real                        |           |          |         | plain    |             |              |
// *  motor_driving_mode    | character varying(255)      |           |          |         | extended |             |              |
// *  motor_fault_feedback  | character varying(255)      |           |          |         | extended |             |              |
// *  motor_mcs_temperature | real                        |           |          |         | plain    |             |              |
// *  motor_parking_sign    | boolean                     |           |          |         | plain    |             |              |
// *  motor_ready_sign      | boolean                     |           |          |         | plain    |             |              |
// *  motor_regeneration    | boolean                     |           |          |         | plain    |             |              |
// *  motor_reverse         | boolean                     |           |          |         | plain    |             |              |
// *  motor_side_stand      | boolean                     |           |          |         | plain    |             |              |
// *  motor_speed           | real                        |           |          |         | plain    |             |              |
// *  motor_temperature     | real                        |           |          |         | plain    |             |              |
// *  motor_throttle        | real                        |           |          |         | plain    |             |              |
// *  packet_received_on    | timestamp(6) with time zone |           |          |         | plain    |             |              |
// *  mfr_org_id            | bigint                      |           |          |         | plain    |             |              |
// *  owner_org             | bigint                      |           |          |         | plain    |             |              |
// *  vehicle_id            | bigint                      |           |          |         | plain    |             |              |
// *  vehicle_model_id      | bigint                      |           |          |         | plain    |             |              |
// * Indexes:
// *     "vehicle_telemetry_data_idx" btree (vehicle_id)
// *     "vehicle_telemetry_data_imei_idx" btree (imei)
// *     "vehicle_telemetry_data_org_idx" btree (owner_org)
// *     "vehicle_telemetry_data_ts_idx" btree ("timestamp")
// * Triggers:
// *     ts_insert_blocker BEFORE INSERT ON vehicle_telemetry_data FOR EACH ROW EXECUTE FUNCTION _timescaledb_internal.insert_blocker()
// * Child tables: _timescaledb_internal._hyper_3_100_chunk,
// *               _timescaledb_internal._hyper_3_104_chunk,
// *               _timescaledb_internal._hyper_3_109_chunk,
// *               _timescaledb_internal._hyper_3_110_chunk,
// *               _timescaledb_internal._hyper_3_112_chunk,
// *               _timescaledb_internal._hyper_3_2_chunk,
// *               _timescaledb_internal._hyper_3_4501_chunk,
// *               _timescaledb_internal._hyper_3_4506_chunk,
// *               _timescaledb_internal._hyper_3_4507_chunk,
// *               _timescaledb_internal._hyper_3_4508_chunk,
// *               _timescaledb_internal._hyper_3_4511_chunk,
// *               _timescaledb_internal._hyper_3_4513_chunk,
// *               _timescaledb_internal._hyper_3_4517_chunk,
// *               _timescaledb_internal._hyper_3_97_chunk
// * Access method: heap
// * </pre>
// */
//@Component("ioWriterV2")
//public class IoWriter extends AbstractWriter<Row> {
//
//  private static final long serialVersionUID = -6236323803392717116L;
//
//  @Override
//  public String getOperatorName() {
//    return "io_writer";
//  }
//
//  @Override
//  public void save(DataStream<Row> dataStream, boolean isLate) {
//    String insertStatement =
//        "INSERT INTO vehicle_telemetry_data ("
//            + "imei, "
//            + "timestamp, "
//            + "co_relation_id, "
//            + "created_on, "
//            + "packet_received_on, "
//            + "mfr_org_id, "
//            + "owner_org_id, "
//            + "vehicle_id, "
//            + "vehicle_model_id, "
//            + "accel_x_axis, "
//            + "accel_y_axis, "
//            + "accel_z_axis, "
//            + "ai_lean_angle, "
//            + "ai_system_voltage, "
//            + "ai_temperature, "
//            + "ai_vbuck, "
//            + "ai_voltage_input, "
//            + "ai_vusr1, "
//            + "ai_vusr2, "
//            + "di_ignition, "
//            + "di_main_power, "
//            + "di_motion, "
//            + "di_tamper, "
//            + "di_usr1, "
//            + "di_usr2, "
//            + "do_usr1, "
//            + "do_usr2, "
//            + "gyro_x_axis, "
//            + "gyro_y_axis, "
//            + "gyro_z_axis, "
//            + "motor_dc_current, "
//            + "motor_dc_voltage, "
//            + "motor_mcs_temperature, "
//            + "motor_speed, "
//            + "motor_temperature, "
//            + "motor_brake, "
//            + "motor_cruise, "
//            + "motor_driving_mode, "
//            + "motor_fault_feedback, "
//            + "motor_parking_sign, "
//            + "motor_ready_sign, "
//            + "motor_regeneration, "
//            + "motor_reverse, "
//            + "motor_side_stand, "
//            + "motor_throttle "
//            + ") "
//            + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, "
//            + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
//            + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
//            + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT (imei, timestamp) DO UPDATE"
//            + " SET accel_x_axis = COALESCE(excluded.accel_x_axis, vehicle_telemetry_data.accel_x_axis) , "
//            + "accel_y_axis = COALESCE(excluded.accel_y_axis, vehicle_telemetry_data.accel_y_axis), "
//            + "accel_z_axis = COALESCE(excluded.accel_z_axis, vehicle_telemetry_data.accel_z_axis), "
//            + "ai_lean_angle = COALESCE(excluded.ai_lean_angle, vehicle_telemetry_data.ai_lean_angle), "
//            + "ai_system_voltage = COALESCE(excluded.ai_system_voltage, vehicle_telemetry_data.ai_system_voltage), "
//            + "ai_temperature = COALESCE(excluded.ai_temperature, vehicle_telemetry_data.ai_temperature), "
//            + "ai_vbuck = COALESCE(excluded.ai_vbuck, vehicle_telemetry_data.ai_vbuck), "
//            + "ai_voltage_input = COALESCE(excluded.ai_voltage_input, vehicle_telemetry_data.ai_voltage_input), "
//            + "ai_vusr1 = COALESCE(excluded.ai_vusr1, vehicle_telemetry_data.ai_vusr1), "
//            + "ai_vusr2 = COALESCE(excluded.ai_vusr2, vehicle_telemetry_data.ai_vusr2), "
//            + "di_ignition = COALESCE(excluded.di_ignition, vehicle_telemetry_data.di_ignition), "
//            + "di_main_power = COALESCE(excluded.di_main_power, vehicle_telemetry_data.di_main_power), "
//            + "di_motion = COALESCE(excluded.di_motion, vehicle_telemetry_data.di_motion), "
//            + "di_tamper = COALESCE(excluded.di_tamper, vehicle_telemetry_data.di_tamper), "
//            + "di_usr1 = COALESCE(excluded.di_usr1, vehicle_telemetry_data.di_usr1), "
//            + "di_usr2 = COALESCE(excluded.di_usr2, vehicle_telemetry_data.di_usr2), "
//            + "do_usr1 = COALESCE(excluded.do_usr1, vehicle_telemetry_data.do_usr1), "
//            + "do_usr2 = COALESCE(excluded.do_usr2, vehicle_telemetry_data.do_usr2), "
//            + "gyro_x_axis = COALESCE(excluded.gyro_x_axis, vehicle_telemetry_data.gyro_x_axis), "
//            + "gyro_y_axis = COALESCE(excluded.gyro_y_axis, vehicle_telemetry_data.gyro_y_axis), "
//            + "gyro_z_axis = COALESCE(excluded.gyro_z_axis, vehicle_telemetry_data.gyro_z_axis), "
//            + "motor_dc_current = COALESCE(excluded.motor_dc_current, vehicle_telemetry_data.motor_dc_current), "
//            + "motor_dc_voltage = COALESCE(excluded.motor_dc_voltage, vehicle_telemetry_data.motor_dc_voltage), "
//            + "motor_mcs_temperature = COALESCE(excluded.motor_mcs_temperature, vehicle_telemetry_data.motor_mcs_temperature), "
//            + "motor_speed = COALESCE(excluded.motor_speed, vehicle_telemetry_data.motor_speed), "
//            + "motor_temperature = COALESCE(excluded.motor_temperature, vehicle_telemetry_data.motor_temperature), "
//            + "motor_brake = COALESCE(excluded.motor_brake, vehicle_telemetry_data.motor_brake), "
//            + "motor_cruise = COALESCE(excluded.motor_cruise, vehicle_telemetry_data.motor_cruise), "
//            + "motor_driving_mode = COALESCE(excluded.motor_driving_mode, vehicle_telemetry_data.motor_driving_mode), "
//            + "motor_fault_feedback = COALESCE(excluded.motor_fault_feedback, vehicle_telemetry_data.motor_fault_feedback), "
//            + "motor_parking_sign = COALESCE(excluded.motor_parking_sign, vehicle_telemetry_data.motor_parking_sign), "
//            + "motor_ready_sign = COALESCE(excluded.motor_ready_sign, vehicle_telemetry_data.motor_ready_sign), "
//            + "motor_regeneration = COALESCE(excluded.motor_regeneration, vehicle_telemetry_data.motor_regeneration), "
//            + "motor_reverse = COALESCE(excluded.motor_reverse, vehicle_telemetry_data.motor_reverse), "
//            + "motor_side_stand = COALESCE(excluded.motor_side_stand, vehicle_telemetry_data.motor_side_stand), "
//            + "motor_throttle = COALESCE(excluded.motor_throttle, vehicle_telemetry_data.motor_throttle);";
//    this.save(dataStream, insertStatement, isLate ? "_late" : "");
//  }
//
//  @Override
//  public void save(DataStream<Row> dataStream, String insertStatement, String operatorSuffix) {
//    JdbcStatementBuilder<Row> jdbcStatementBuilder =
//        (preparedStatement, row) -> {
//          long imei = row.getFieldAs("imei");
//          int observedTimestamp = row.getFieldAs("timestamp");
//          long ingestionTimestamp = row.getFieldAs("ingestion_timestamp");
//          preparedStatement.setString(1, String.valueOf(imei)); // imei
//          preparedStatement.setTimestamp(
//              2, java.sql.Timestamp.from(Instant.ofEpochSecond(observedTimestamp))); // timestamp
//          preparedStatement.setString(3, row.getFieldAs("correlation_id")); // co_relation_id
//          preparedStatement.setTimestamp(4, java.sql.Timestamp.from(Instant.now())); // created_on
//          preparedStatement.setTimestamp(
//              5,
//              java.sql.Timestamp.from(
//                  Instant.ofEpochMilli(ingestionTimestamp))); // packet_received_on
//          preparedStatement.setLong(6, row.getFieldAs("manufacturer_id")); // mfr_org_id
//          preparedStatement.setLong(7, row.getFieldAs("owner_id")); // owner_org
//          preparedStatement.setLong(8, row.getFieldAs("vehicle_id")); // vehicle_id
//          preparedStatement.setNull(9, Types.NULL); // vehicle_model_id
//
//          // Accelerometer
//          preparedStatement.setNull(10, Types.FLOAT); // accel_x_axis
//          preparedStatement.setNull(11, Types.FLOAT); // accel_y_axis
//          preparedStatement.setNull(12, Types.FLOAT); // accel_z_axis
//
//          //  Analog Input
//          preparedStatement.setInt(13, row.getFieldAs("ai_lean_angle")); // lean_angle
//          preparedStatement.setFloat(14, row.getFieldAs("ai_vsys")); // vsys
//          preparedStatement.setFloat(15, row.getFieldAs("ai_temp")); // temp
//          preparedStatement.setFloat(16, row.getFieldAs("ai_vbuck")); // vbuck
//          preparedStatement.setFloat(17, row.getFieldAs("ai_vin")); // vin
//          preparedStatement.setInt(18, row.getFieldAs("ai_vusr1")); // vusr_1
//          preparedStatement.setInt(19, row.getFieldAs("ai_vusr2")); // vusr_2
//
//          // Digital Input
//          preparedStatement.setBoolean(20, row.getFieldAs("di_ignition")); // di_ignition
//          preparedStatement.setBoolean(21, row.getFieldAs("di_main_power")); // di_main_power
//          preparedStatement.setBoolean(22, row.getFieldAs("di_motion")); // di_motion
//          preparedStatement.setBoolean(23, row.getFieldAs("di_tamper")); // di_tamper
//          preparedStatement.setBoolean(24, row.getFieldAs("di_usr1")); // di_usr1
//          preparedStatement.setBoolean(25, row.getFieldAs("di_usr2")); // di_usr2
//
//          // Digital Output
//          preparedStatement.setBoolean(26, row.getFieldAs("do_usr1")); // do_usr1
//          preparedStatement.setBoolean(27, row.getFieldAs("do_usr2")); // do_usr2
//
//          // Gyro
//          preparedStatement.setNull(28, Types.NULL); // gyro_x_axis
//          preparedStatement.setNull(29, Types.NULL); // gyro_y_axis
//          preparedStatement.setNull(30, Types.NULL); // gyro_z_axis
//
//          // mcd
//          preparedStatement.setNull(31, Types.FLOAT); // gyro_x_axis
//          preparedStatement.setNull(32, Types.FLOAT); // gyro_y_axis
//          preparedStatement.setNull(33, Types.FLOAT); // gyro_z_axis
//          preparedStatement.setNull(34, Types.FLOAT); // gyro_y_axis
//          preparedStatement.setNull(35, Types.FLOAT); // gyro_z_axis
//
//          // mcs
//          preparedStatement.setNull(36, Types.BOOLEAN); // motor_brake
//          preparedStatement.setNull(37, Types.BOOLEAN); // motor_cruise
//          preparedStatement.setNull(38, Types.VARCHAR); // motor_driving_mode
//          preparedStatement.setNull(39, Types.VARCHAR); // motor_fault_feedback
//          preparedStatement.setNull(40, Types.BOOLEAN); // parking
//          preparedStatement.setNull(41, Types.BOOLEAN); // ready
//          preparedStatement.setNull(42, Types.BOOLEAN); // regeneration
//          preparedStatement.setNull(43, Types.BOOLEAN); // reverse
//          preparedStatement.setNull(44, Types.BOOLEAN); // side_stand
//          preparedStatement.setNull(45, Types.FLOAT); // motor_throttle
//        };
//
//    this.writer(
//        dataStream, insertStatement, jdbcStatementBuilder, getOperatorName() + operatorSuffix);
//  }
//}
