package com.nichesolv.nds.datacleaning.function.sink.timescale;

import java.io.Serializable;
import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;
import org.apache.flink.connector.jdbc.JdbcSink;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class JdbcWriter implements Serializable {

  private static final long serialVersionUID = -794507112644298549L;
  @Autowired private JdbcConnectionOptions jdbcConnectionOptions;

  @Autowired private JdbcExecutionOptions jdbcExecutionOptions;

  public <T> void write(
      DataStream<T> dataStream, String sql, JdbcStatementBuilder<T> statementBuilder) {
    dataStream
        .addSink(JdbcSink.sink(sql, statementBuilder, jdbcExecutionOptions, jdbcConnectionOptions))
        .setParallelism(1);
  }

  public <T> void write(
      DataStream<T> dataStream, String sql, JdbcStatementBuilder<T> statementBuilder, String name) {
    dataStream
        .addSink(JdbcSink.sink(sql, statementBuilder, jdbcExecutionOptions, jdbcConnectionOptions))
        .name(name)
        .uid(name)
        .setParallelism(1);
  }
}
