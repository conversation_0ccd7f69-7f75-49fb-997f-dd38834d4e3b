package com.nichesolv.nds.datacleaning.function.sink.timescale;

import com.nichesolv.nds.datacleaning.function.util.PreparedStatementSetNull;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("locationWriterV2")
public class LocationWriter extends TelemetryWriter {

  private static final long serialVersionUID = -6290437110546867799L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:writer/location_writer.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:writer/location_error_writer.sql')}")
  private String errorSql;

  private final String tableName = "vehicle_location_data";

  @Override
  public String getOperatorName() {
    return "location_writer";
  }

  @Override
  public String getTableName() {
    return tableName;
  }

  @Override
  public String getSql(boolean isError) {
    return isError ? errorSql : sql;
  }

  @Override
  public void save(DataStream<Row> dataStream, String insertStatement, String operatorSuffix) {
    JdbcStatementBuilder<Row> jdbcStatementBuilder =
        (preparedStatement, row) -> {
          long imei = row.getFieldAs("imei");
          int observedTimestamp = row.getFieldAs("timestamp");
          long ingestionTimestamp = row.getFieldAs("ingestion_timestamp");
          Instant observedTime = Instant.ofEpochSecond(observedTimestamp);
          Instant ingestionTime = Instant.ofEpochMilli(ingestionTimestamp);
          if (observedTime.isAfter(ingestionTime))
            observedTime = ingestionTime.truncatedTo(ChronoUnit.SECONDS);
          preparedStatement.setString(1, String.valueOf(imei)); // imei
          preparedStatement.setTimestamp(2, java.sql.Timestamp.from(observedTime)); // timestamp

          // altitude           | real                        |           |          |
          PreparedStatementSetNull.setFloatOrNull(preparedStatement, 3, row.getFieldAs("altitude"));

          // brg                | real                        |           |          |
          PreparedStatementSetNull.setFloatOrNull(preparedStatement, 4, row.getFieldAs("bearing"));

          // co_relation_id     | text                        |           |          |
          preparedStatement.setObject(
              5, UUID.fromString(row.getFieldAs("correlation_id"))); // co_relation_id

          preparedStatement.setTimestamp(6, java.sql.Timestamp.from(Instant.now())); // created_on

          // hdop               | real                        |           |          |
          PreparedStatementSetNull.setFloatOrNull(preparedStatement, 7, row.getFieldAs("hdop"));

          // latitude           | real                        |           |          |
          PreparedStatementSetNull.setDoubleOrNull(
              preparedStatement, 8, row.getFieldAs("latitude"));

          // longitude          | real                        |           |          |
          PreparedStatementSetNull.setDoubleOrNull(
              preparedStatement, 9, row.getFieldAs("longitude"));

          preparedStatement.setTimestamp(
              10, java.sql.Timestamp.from(ingestionTime)); // packet_received_on

          // pdop               | real                        |           |          |
          PreparedStatementSetNull.setFloatOrNull(preparedStatement, 11, row.getFieldAs("pdop"));

          // speed              | real                        |           |          |
          PreparedStatementSetNull.setFloatOrNull(preparedStatement, 12, row.getFieldAs("speed"));

          // track_sats         | real                        |           |          |
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 13, row.getFieldAs("track_sats"));

          // vdop               | real                        |           |          |
          PreparedStatementSetNull.setFloatOrNull(preparedStatement, 14, row.getFieldAs("vdop"));

          // view_sats          | real                        |           |          |
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 15, row.getFieldAs("view_sats"));

          preparedStatement.setLong(16, row.getFieldAs("mfr_org_id")); // mfr_org_id
          preparedStatement.setLong(17, row.getFieldAs("owner_org_id")); // owner_org
          preparedStatement.setLong(18, row.getFieldAs("vehicle_id")); // vehicle_id
          PreparedStatementSetNull.setDoubleOrNull(
              preparedStatement, 19, row.getFieldAs("longitude"));
          PreparedStatementSetNull.setDoubleOrNull(
              preparedStatement, 20, row.getFieldAs("latitude"));
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 21, row.getFieldAs("altitude"));
          preparedStatement.setTimestamp(
              22, java.sql.Timestamp.from(Instant.ofEpochSecond(observedTimestamp)));
          if (!operatorSuffix.contains("_error")) {
            PreparedStatementSetNull.setDoubleOrNull(
                preparedStatement, 23, row.getFieldAs("gps_distance"));
          }
        };

    this.writer(
        dataStream, insertStatement, jdbcStatementBuilder, getOperatorName() + operatorSuffix);
  }
}
