package com.nichesolv.nds.datacleaning.function.sink.timescale;

import java.sql.Types;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;

import com.nichesolv.nds.datacleaning.function.util.PreparedStatementSetNull;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("motorControllerDataWriter")
public class MotorControllerDataWriter extends TelemetryWriter {

  private static final long serialVersionUID = -2955091233650820204L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:writer/motor_controller_data_writer.sql')}")
  private String sql;

  private final String tableName = "vehicle_telemetry_data";

  @Override
  public String getOperatorName() {
    return "motor_controller_data_writer";
  }

  @Override
  public String getTableName() {
    return tableName;
  }

  @Override
  public String getSql(boolean isError) {
    return sql;
  }

  @Override
  public void save(DataStream<Row> dataStream, String insertStatement, String operatorSuffix) {
    JdbcStatementBuilder<Row> jdbcStatementBuilder =
        (preparedStatement, row) -> {
          long imei = row.getFieldAs("imei");
          int observedTimestamp = row.getFieldAs("timestamp");
          long ingestionTimestamp = row.getFieldAs("ingestion_timestamp");
          Instant observedTime = Instant.ofEpochSecond(observedTimestamp);
          Instant ingestionTime = Instant.ofEpochMilli(ingestionTimestamp);
          if (observedTime.isAfter(ingestionTime))
            observedTime = ingestionTime.truncatedTo(ChronoUnit.SECONDS);
          preparedStatement.setString(1, String.valueOf(imei)); // imei
          preparedStatement.setTimestamp(2, java.sql.Timestamp.from(observedTime)); // timestamp
          preparedStatement.setObject(3, UUID.fromString(row.getFieldAs("correlation_id"))); // co_relation_id
          preparedStatement.setTimestamp(4, java.sql.Timestamp.from(Instant.now())); // created_on
          preparedStatement.setTimestamp(
              5, java.sql.Timestamp.from(ingestionTime)); // packet_received_on
          preparedStatement.setLong(6, row.getFieldAs("mfr_org_id")); // mfr_org_id
          preparedStatement.setLong(7, row.getFieldAs("owner_org_id")); // owner_org
          preparedStatement.setLong(8, row.getFieldAs("vehicle_id")); // vehicle_id

          // mcd
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 9, row.getFieldAs("dc_current"));
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 10, row.getFieldAs("dc_voltage"));

          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 11, row.getFieldAs("mcs_temperature")); // motor_speed

          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 12, row.getFieldAs("motor_speed"));
          PreparedStatementSetNull.setFloatOrNull(
              preparedStatement, 13, row.getFieldAs("motor_temperature"));
        };

    this.writer(
        dataStream, insertStatement, jdbcStatementBuilder, getOperatorName() + operatorSuffix);
  }
}
