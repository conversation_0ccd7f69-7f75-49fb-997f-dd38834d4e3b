package com.nichesolv.nds.datacleaning.function.sink.timescale;

import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

@Component("motorFaultsWriter")
public class MotorFaultsWriter extends TelemetryWriter {

  private static final long serialVersionUID = 1361800870745179832L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:writer/motor_faults_writer.sql')}")
  private String sql;

  private final String tableName = "battery_alarm";

  @Override
  public String getOperatorName() {
    return "motor_faults_writer";
  }

  @Override
  public String getTableName() {
    return tableName;
  }

  @Override
  public String getSql(boolean isError) {
    return sql;
  }

  @Override
  public void save(DataStream<Row> dataStream, String insertStatement, String operatorSuffix) {
    JdbcStatementBuilder<Row> jdbcStatementBuilder =
        (preparedStatement, row) -> {
          long imei = row.getFieldAs("imei");
          int observedTimestamp = row.getFieldAs("timestamp");
          Instant observedTime = Instant.ofEpochSecond(observedTimestamp);
          if (observedTime.isAfter(Instant.now()))
            observedTime = Instant.now().truncatedTo(ChronoUnit.SECONDS);

          preparedStatement.setString(1, String.valueOf(imei)); // imei
          preparedStatement.setTimestamp(2, java.sql.Timestamp.from(observedTime)); // timestamp
          preparedStatement.setBoolean(3, row.getFieldAs("alarm"));
          preparedStatement.setBoolean(4, row.getFieldAs("protection"));
          preparedStatement.setLong(5, row.getFieldAs("alarm_id"));
          preparedStatement.setString(6, row.getFieldAs("part_type"));
          preparedStatement.setLong(7, row.getFieldAs("mfr_org_id")); // mfr_org_id
          preparedStatement.setLong(8, row.getFieldAs("owner_org_id")); // owner_org
          preparedStatement.setLong(9, row.getFieldAs("vehicle_id")); // vehicle_id
        };
    this.writer(
        dataStream, insertStatement, jdbcStatementBuilder, getOperatorName() + operatorSuffix);
  }
}
