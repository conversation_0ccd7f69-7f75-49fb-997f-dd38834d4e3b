package com.nichesolv.nds.datacleaning.function.sink.timescale;

import com.nichesolv.nds.datacleaning.function.util.PreparedStatementSetNull;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("telemetryWriterV2")
public class TelemetryMergedWriter extends TelemetryWriter {

  private static final long serialVersionUID = 2776340125505454113L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:writer/telemetry_writer.sql')}")
  private String sql;

  private final String tableName="vehicle_telemetry_data";

  @Override
  public String getOperatorName() {
    return "telemetry_writer";
  }

  @Override
  public String getTableName() {
    return tableName;
  }

  @Override
  public String getSql(boolean isError) {
    return sql;
  }

  @Override
  public void save(DataStream<Row> dataStream, String insertStatement, String operatorSuffix) {
    JdbcStatementBuilder<Row> jdbcStatementBuilder =
        (preparedStatement, row) -> {
          long imei = row.getFieldAs("imei");
          int observedTimestamp = row.getFieldAs("timestamp");
          long ingestionTimestamp = row.getFieldAs("ingestion_timestamp");
          Instant observedTime = Instant.ofEpochSecond(observedTimestamp);
          Instant ingestionTime = Instant.ofEpochMilli(ingestionTimestamp);
          if (observedTime.isAfter(ingestionTime))
            observedTime = ingestionTime.truncatedTo(ChronoUnit.SECONDS);
          preparedStatement.setString(1, String.valueOf(imei)); // imei
          preparedStatement.setTimestamp(2, java.sql.Timestamp.from(observedTime)); // timestamp
          preparedStatement.setObject(3, UUID.fromString(row.getFieldAs("correlation_id"))); // co_relation_id
          preparedStatement.setTimestamp(4, java.sql.Timestamp.from(Instant.now())); // created_on
          preparedStatement.setTimestamp(
              5, java.sql.Timestamp.from(ingestionTime)); // packet_received_on
          preparedStatement.setLong(6, row.getFieldAs("mfr_org_id")); // mfr_org_id
          preparedStatement.setLong(7, row.getFieldAs("owner_org_id")); // owner_org
          preparedStatement.setLong(8, row.getFieldAs("vehicle_id")); // vehicle_id

            //  Analog Input
            // lean_angle
            PreparedStatementSetNull.setFloatOrNull(
                    preparedStatement, 9, row.getFieldAs("ai_vsys")); // vsys
            PreparedStatementSetNull.setFloatOrNull(preparedStatement, 10, row.getFieldAs("ai_temp"));
            PreparedStatementSetNull.setFloatOrNull(
                    preparedStatement, 11, row.getFieldAs("ai_vbuck")); // vbuck
            PreparedStatementSetNull.setFloatOrNull(
                    preparedStatement, 12, row.getFieldAs("ai_vin")); // vin
            PreparedStatementSetNull.setIntOrNull(
                    preparedStatement, 13, row.getFieldAs("ai_vusr1")); // vusr_1
            PreparedStatementSetNull.setIntOrNull(
                    preparedStatement, 14, row.getFieldAs("ai_vusr2")); // vusr_2

            // Digital Input
            PreparedStatementSetNull.setBoolOrNull(preparedStatement,15, row.getFieldAs("di_ignition")); // di_ignition
            PreparedStatementSetNull.setBoolOrNull(preparedStatement,16, row.getFieldAs("di_main_power")); // di_main_power
            PreparedStatementSetNull.setBoolOrNull(preparedStatement,17, row.getFieldAs("di_motion")); // di_motion
            PreparedStatementSetNull.setBoolOrNull(preparedStatement,18, row.getFieldAs("di_tamper")); // di_tamper
            PreparedStatementSetNull.setBoolOrNull(preparedStatement,19, row.getFieldAs("di_usr1")); // di_usr1
            PreparedStatementSetNull.setBoolOrNull(preparedStatement,20, row.getFieldAs("di_usr2")); // di_usr2

            // Digital Output
            PreparedStatementSetNull.setBoolOrNull(preparedStatement,21, row.getFieldAs("do_usr1")); // do_usr1
            PreparedStatementSetNull.setBoolOrNull(preparedStatement,22, row.getFieldAs("do_usr2")); // do_usr2

        };

    this.writer(
        dataStream, insertStatement, jdbcStatementBuilder, this.getOperatorName() + operatorSuffix);
  }
}
