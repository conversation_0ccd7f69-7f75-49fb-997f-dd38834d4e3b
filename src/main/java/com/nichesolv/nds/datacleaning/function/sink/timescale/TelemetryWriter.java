package com.nichesolv.nds.datacleaning.function.sink.timescale;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/** This class handles writing to both the clean data and error data tables. */
@Component
public class TelemetryWriter extends AbstractWriter<Row> {

  private static final long serialVersionUID = -4234679072610998358L;

  @Value("${appConfig.enableRawDataPersistence}")
  private Boolean isRawDataPersistenceEnabled;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:writer/accelerometer_writer.sql')}")
  private String sql;

  @Override
  public String getOperatorName() {
    return null;
  }

  @Override
  public String getTableName() {
    return "vehicle_telemetry_data";
  }

  @Override
  public String getSql(boolean isError) {
    return sql;
  }

    public void save(DataStream<Row> dataStream, boolean isLate, boolean isError, boolean isRaw) {
      String tableName = this.getTableName();
      String operatorSuffix = "_clean";

      if (isError) {
        tableName += "_error";
        operatorSuffix = isLate ? "_error_late" : "_error";
      } else if (isLate) {
        operatorSuffix = isRaw ? "_late_raw" : "_late";
        if (isRaw) {
          tableName += "_raw";
        }
      } else if (isRaw) {
        tableName += "_raw";
        operatorSuffix = "_raw";
      }

      this.save(dataStream, this.getSql(isError).replace("%s", tableName), operatorSuffix);
    }

  @Override
  public void save(DataStream<Row> dataStream, String insertStatement) {}

  @Override
  public void save(DataStream<Row> dataStream, String insertStatement, String operatorSuffix) {}
}
