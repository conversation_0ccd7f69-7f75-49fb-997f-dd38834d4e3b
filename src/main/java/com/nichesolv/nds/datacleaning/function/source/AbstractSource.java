package com.nichesolv.nds.datacleaning.function.source;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.apache.flink.streaming.connectors.rabbitmq.RMQSource;
import org.apache.flink.streaming.connectors.rabbitmq.common.RMQConnectionConfig;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractSource<T> implements Serializable {

  private static final long serialVersionUID = -6162943897063231927L;

  public abstract String getQueueName();

  public abstract Boolean useCorrelationId();

  @Autowired private RMQConnectionConfig rmqConnectionConfig;

  public abstract DeserializationSchema<Tuple3<Timestamp, Metadata, T>> getDeserializationSchema();

  public RichSourceFunction<Tuple3<Timestamp, Metadata, T>> getSource() {
    return new RMQSource<>(
        rmqConnectionConfig, getQueueName(), useCorrelationId(), getDeserializationSchema());
  }
}
