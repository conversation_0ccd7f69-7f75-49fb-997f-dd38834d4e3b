package com.nichesolv.nds.datacleaning.function.source.bms;

import com.nichesolv.nds.datacleaning.function.source.AbstractSource;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.java.tuple.Tuple3;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component("batteryMetadataSource")
public class MetadataSource extends AbstractSource<Metadata> {

  private static final long serialVersionUID = 6958043816755017076L;

  @Autowired
  @Qualifier("batteryMetadataDeserializer")
  private DeserializationSchema<
          Tuple3<Timestamp, com.nichesolv.nds.model.core.ajjas.metadata.Metadata, Metadata>>
      deserializationSchema;

  @Override
  public String getQueueName() {
    return "event.parsed.bms.metadata";
  }

  @Override
  public Boolean useCorrelationId() {
    return false;
  }

  @Override
  public DeserializationSchema<
          Tuple3<Timestamp, com.nichesolv.nds.model.core.ajjas.metadata.Metadata, Metadata>>
      getDeserializationSchema() {
    return deserializationSchema;
  }
}
