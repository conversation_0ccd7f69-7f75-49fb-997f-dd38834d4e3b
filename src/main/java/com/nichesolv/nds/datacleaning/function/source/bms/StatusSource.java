package com.nichesolv.nds.datacleaning.function.source.bms;

import com.nichesolv.nds.datacleaning.function.source.AbstractSource;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BatteryStatus;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.java.tuple.Tuple3;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component("batteryStatusSource")
public class StatusSource extends AbstractSource<BatteryStatus> {

  private static final long serialVersionUID = 361282532311109224L;

  @Autowired
  @Qualifier("batteryStatusSerializer")
  private DeserializationSchema<Tuple3<Timestamp, Metadata, BatteryStatus>> deserializationSchema;

  @Override
  public String getQueueName() {
    return "event.parsed.bms.status";
  }

  @Override
  public Boolean useCorrelationId() {
    return false;
  }

  @Override
  public DeserializationSchema<Tuple3<Timestamp, Metadata, BatteryStatus>>
      getDeserializationSchema() {
    return deserializationSchema;
  }
}
