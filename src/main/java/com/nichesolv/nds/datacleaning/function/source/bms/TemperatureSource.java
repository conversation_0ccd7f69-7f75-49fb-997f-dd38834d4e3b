package com.nichesolv.nds.datacleaning.function.source.bms;

import com.nichesolv.nds.datacleaning.function.source.AbstractSource;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.BatteryCellTemperature;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.java.tuple.Tuple3;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component("batteryTemperatureSource")
public class TemperatureSource extends AbstractSource<BatteryCellTemperature> {

  private static final long serialVersionUID = -1768217273808769439L;

  @Autowired
  @Qualifier("batteryTemperatureDeserializer")
  private DeserializationSchema<Tuple3<Timestamp, Metadata, BatteryCellTemperature>>
      deserializationSchema;

  @Override
  public String getQueueName() {
    return "event.parsed.bms.temperature";
  }

  @Override
  public Boolean useCorrelationId() {
    return false;
  }

  @Override
  public DeserializationSchema<Tuple3<Timestamp, Metadata, BatteryCellTemperature>>
      getDeserializationSchema() {
    return deserializationSchema;
  }
}
