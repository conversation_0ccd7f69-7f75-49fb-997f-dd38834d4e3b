package com.nichesolv.nds.datacleaning.function.source.bms;

import com.nichesolv.nds.datacleaning.function.source.AbstractSource;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltage;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.java.tuple.Tuple3;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component("batteryVoltageSource")
public class VoltageSource extends AbstractSource<BatteryCellVoltage> {

  private static final long serialVersionUID = 6476772099493048576L;

  @Autowired
  @Qualifier("batteryVoltageDeserializer")
  private DeserializationSchema<Tuple3<Timestamp, Metadata, BatteryCellVoltage>>
      deserializationSchema;

  @Override
  public String getQueueName() {
    return "event.parsed.bms.voltage";
  }

  @Override
  public Boolean useCorrelationId() {
    return false;
  }

  @Override
  public DeserializationSchema<Tuple3<Timestamp, Metadata, BatteryCellVoltage>>
      getDeserializationSchema() {
    return deserializationSchema;
  }
}
