package com.nichesolv.nds.datacleaning.function.source.telemetry;

import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInput;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.util.List;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.apache.flink.streaming.connectors.rabbitmq.RMQSource;
import org.apache.flink.streaming.connectors.rabbitmq.common.RMQConnectionConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("analogInputSource")
public class AnalogInputSource {

  @Value("${appConfig.source.analogInput.queueName}")
  private String queueName;

  @Value("${appConfig.source.analogInput.usesCorrelationId}")
  private Boolean usesCorrelationId;

  @Autowired private RMQConnectionConfig rmqConnectionConfig;

  @Autowired
  @Qualifier("analogInputDeserializationSchema")
  private DeserializationSchema<Tuple3<Timestamp, Metadata, List<AnalogInput>>>
      deserializationSchema;

  private static final long serialVersionUID = 3882769229888716882L;

  public RichSourceFunction<Tuple3<Timestamp, Metadata, List<AnalogInput>>> getSource() {
    return new RMQSource<>(
        rmqConnectionConfig, queueName, usesCorrelationId, deserializationSchema);
  }
}
