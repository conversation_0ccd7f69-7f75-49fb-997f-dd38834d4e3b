package com.nichesolv.nds.datacleaning.function.transformer.accelerometer;

import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import java.time.Instant;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("accelerometerRawTransformer")
public class AccelerometerRawTransformer implements Serializable {
    private static final long serialVersionUID = 5637529810427245241L;

    @Value(
            "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:raw/accelerometer_raw.sql')}")
    private String sql;

    @Value(
            "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:raw/accelerometer_raw_late_events.sql')}")
    private String lateEventsSql;

    @Autowired
    @Qualifier("accelerometerWriterV2")
    private AbstractWriter<Row> accelerometerWriter;

    public Table transform(
            StreamExecutionEnvironment env,
            StreamTableEnvironment tableEnv,
            DataStream<Tuple3<Timestamp, Metadata, Accelerometer>> dataStream) {
        // Analog input stream.
        // Let's print the analog input stream before we do any kind of imputation or anything else.
        dataStream.print("[AnalogInput]InitialAnalogInputStream::>> ").setParallelism(1);

        // Only simple types are allowed in the table.
        // This is a necessary evil.
        String[] fieldNames =
                new String[] {
                        "event_time",
                        "observed_timestamp",
                        "ingestion_timestamp",
                        "correlation_id",
                        "imei",
                        "x",
                        "y",
                        "z"
                };
        DataStream<Row> analogInputStream =
                dataStream
                        .map(new AccelerometerRawToRowMapper())
                        .name("accelerometer_raw_to_row_mapper")
                        .uid("accelerometer_raw_to_row_mapper")
                        .setParallelism(1)
                        .returns(
                                Types.ROW_NAMED(
                                        fieldNames,
                                        Types.INSTANT,
                                        Types.INT,
                                        Types.LONG,
                                        Types.STRING,
                                        Types.LONG,
                                        Types.INT,
                                        Types.INT,
                                        Types.INT));

        tableEnv.createTemporaryView(
                "accelerometer_raw",
                analogInputStream,
                Schema.newBuilder()
                        .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
                        .columnByExpression("proc_time", "PROCTIME()")
                        .watermark("rowtime", "SOURCE_WATERMARK()")
                        .build());

        tableEnv.executeSql(lateEventsSql);

        DataStream<Row> lateEvents =
                tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `accelerometer_raw_late`"));
        lateEvents
                .print("AccelerometerRawLateEvents::>> ")
                .name("accelerometer_raw_late_events")
                .uid("accelerometer_raw_late_events")
                .setParallelism(1);

        this.accelerometerWriter.save(lateEvents, true, false, true);

        // todo: We should seriously think about do this in a single query.
        //  Group by imei, partition again by imei, call the udf, cross join unnest.
        // Or a simpler implementation would be to take a look at udfs that take pandas dataframes as
        // input.
        return tableEnv.sqlQuery(sql);
    }

    public static class AccelerometerRawToRowMapper
            extends RichMapFunction<Tuple3<Timestamp, Metadata, Accelerometer>, Row> {

        private static final long serialVersionUID = 8992353614702019283L;

        @Override
        public Row map(Tuple3<Timestamp, Metadata, Accelerometer> value) throws Exception {
            Timestamp timestamp = value.f0;
            Metadata metadata = value.f1;
            Accelerometer accelerometer = value.f2;

            // Table schema.
            return Row.of(
                    Instant.ofEpochSecond(timestamp.getTimestamp()),
                    timestamp.getTimestamp(),
                    timestamp.getIngestionTime(),
                    metadata.getCorrelationId(),
                    metadata.getImei(),
                    accelerometer.getX(),
                    accelerometer.getY(),
                    accelerometer.getZ());
        }
    }
}
