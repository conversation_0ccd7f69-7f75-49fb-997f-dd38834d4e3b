package com.nichesolv.nds.datacleaning.function.transformer.accelerometer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.datacleaning.function.util.Corrector;
import com.nichesolv.nds.model.cache.GrvAggData;
import com.nichesolv.nds.model.cache.Vec3;
import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.IOException;
import java.io.Serializable;
import java.time.Instant;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("accelerometerTransformer")
public class AccelerometerTransformer implements Serializable {
  private static final long serialVersionUID = 5637529810427245241L;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/accelerometer.sql')}")
  private String sql;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/accelerometer_late_events.sql')}")
  private String lateEventsSql;

  private static final Logger log = LoggerFactory.getLogger(AccelerometerTransformer.class);

  @Autowired
  @Qualifier("accelerometerWriterV2")
  private AbstractWriter<Row> accelerometerWriter;

  @Autowired
  private RedisConnectionManagerFactory2 redisConnectionManagerFactory;

  public Table transform(
          StreamExecutionEnvironment env,
          StreamTableEnvironment tableEnv,
          DataStream<Tuple3<Timestamp, Metadata, Accelerometer>> dataStream) {
    // Analog input stream.
    // Let's print the analog input stream before we do any kind of imputation or anything else.
    dataStream.print("[AnalogInput]InitialAnalogInputStream::>> ").setParallelism(1);

    RedisConnectionManager2 redisConnectionManager =
            redisConnectionManagerFactory.createRedisConnectionManager();

    // Only simple types are allowed in the table.
    // This is a necessary evil.
    String[] fieldNames =
            new String[] {
                    "event_time",
                    "observed_timestamp",
                    "ingestion_timestamp",
                    "correlation_id",
                    "imei",
                    "x",
                    "y",
                    "z"
            };
    DataStream<Row> analogInputStream =
            dataStream
                    .map(new AccelerometerToRowMapper(redisConnectionManager))
                    .name("accelerometer_to_row_mapper")
                    .uid("accelerometer_to_row_mapper")
                    .setParallelism(1)
                    .returns(
                            Types.ROW_NAMED(
                                    fieldNames,
                                    Types.INSTANT,
                                    Types.INT,
                                    Types.LONG,
                                    Types.STRING,
                                    Types.LONG,
                                    Types.INT,
                                    Types.INT,
                                    Types.INT));

    tableEnv.createTemporaryView(
            "accelerometer",
            analogInputStream,
            Schema.newBuilder()
                    .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
                    .columnByExpression("proc_time", "PROCTIME()")
                    .watermark("rowtime", "SOURCE_WATERMARK()")
                    .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
            tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `accelerometer_late`"));
    lateEvents
            .print("AccelerometerLateEvents::>> ")
            .name("accelerometer_late_events")
            .uid("accelerometer_late_events")
            .setParallelism(1);

    this.accelerometerWriter.save(lateEvents, true, false, false);

    // todo: We should seriously think about do this in a single query.
    //  Group by imei, partition again by imei, call the udf, cross join unnest.
    // Or a simpler implementation would be to take a look at udfs that take pandas dataframes as
    // input.
    return tableEnv.sqlQuery(sql);
  }

  public static class AccelerometerToRowMapper
          extends RichMapFunction<Tuple3<Timestamp, Metadata, Accelerometer>, Row> {

    private static final long serialVersionUID = 8992353614702019283L;

    private final RedisConnectionManager2 redisConnectionManager;

    public AccelerometerToRowMapper(RedisConnectionManager2 redisConnectionManager) {
      this.redisConnectionManager = redisConnectionManager;
    }

    @Override
    public void open(Configuration parameters) {
      try {
        redisConnectionManager.init();
        log.info("RedisConnectionManager initialized successfully for AccelerometerToRowMapper.");
      } catch (Exception e) {
        log.error("Error initializing RedisConnectionManager", e);
        throw new RuntimeException("Failed to initialize Redis connection.", e);
      }
    }

    @Override
    public Row map(Tuple3<Timestamp, Metadata, Accelerometer> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      Accelerometer accelerometer = value.f2;
      String imei = String.valueOf(metadata.getImei());

      String redisKey = "IMEI_GRV_RAW_AGG::" + imei;

      Object redisValue = redisConnectionManager.getValue(redisKey);

      boolean isNullRedisValue =
              (redisValue == null || "NULL".equalsIgnoreCase(redisValue.toString()));

      String cleanedJson = null; // Initialize to null
      GrvAggData grvAggData = null; // Initialize to null

      if (!isNullRedisValue) {
        cleanedJson = preprocessJson(redisValue.toString());
        if (cleanedJson != null && !cleanedJson.equalsIgnoreCase("NULL")) {
          try {
            grvAggData = new ObjectMapper().readValue(cleanedJson, GrvAggData.class);
          } catch (JsonProcessingException e) {
            log.debug("NULL CACHE ENTRY: defaulting to null");
          }
        }
      }

      Vec3 correctedAccVector =
              (grvAggData == null
                      || accelerometer == null
                      || grvAggData.getXstill() == null
                      || grvAggData.getYstill() == null
                      || grvAggData.getZstill() == null
                      || grvAggData.getXrunning() == null
                      || grvAggData.getYrunning() == null
                      || grvAggData.getZrunning() == null)
                      ? null
                      : Corrector.Correct(
                      new Vec3(
                              (float) accelerometer.getX(),
                              (float) accelerometer.getY(),
                              (float) accelerometer.getZ()),
                      new Vec3(
                              grvAggData.getXstill()[0],
                              grvAggData.getYstill()[0],
                              grvAggData.getZstill()[0]),
                      new Vec3(
                              grvAggData.getXrunning()[0],
                              grvAggData.getYrunning()[0],
                              grvAggData.getZrunning()[0]));

      return Row.of(
              Instant.ofEpochSecond(timestamp.getTimestamp()),
              timestamp.getTimestamp(),
              timestamp.getIngestionTime(),
              metadata.getCorrelationId(),
              metadata.getImei(),
              (correctedAccVector == null) ? null : (int) correctedAccVector.getX(),
              (correctedAccVector == null) ? null : (int) correctedAccVector.getY(),
              (correctedAccVector == null) ? null : (int) correctedAccVector.getZ());
    }

    private static String preprocessJson(String redisValue) {
      try {
        // Parse the JSON string into a JsonNode
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(redisValue);

        rootNode
                .fields()
                .forEachRemaining(
                        entry -> {
                          JsonNode valueNode = entry.getValue();
                          if (valueNode.isArray()) {
                            // If the value is an array, replace it with the second element (the actual
                            // value)
                            ((com.fasterxml.jackson.databind.node.ArrayNode) valueNode).remove(0);
                          }
                        });

        String s = objectMapper.writeValueAsString(rootNode);
        return s;

      } catch (IOException e) {
        e.printStackTrace();
        return redisValue; // Return the original value in case of error
      }
    }
  }
}