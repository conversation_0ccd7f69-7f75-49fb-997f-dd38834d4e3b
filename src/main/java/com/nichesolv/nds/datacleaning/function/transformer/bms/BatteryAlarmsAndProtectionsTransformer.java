package com.nichesolv.nds.datacleaning.function.transformer.bms;

// todo: Move to a different package, these are not transformers.

import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.Alarm;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BatteryStatus;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.Protection;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

// todo: This need to be moved to some other package or something, this is not alarms and
// protections.
@Component("batteryAlarmsAndProtectionsTransformer")
public class BatteryAlarmsAndProtectionsTransformer {

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/alarms_protections.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/alarm_protection_late_events.sql')}")
  private String lateEventsSql;

  @Autowired
  @Qualifier("alarmsAndProtectionsWriter")
  private AbstractWriter<Row> alarmsAndProtectionsWriter;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, BatteryStatus>> dataStream) {
    DataStream<Tuple3<Timestamp, Metadata, Tuple2<List<Alarm>, List<Protection>>>> stream =
        dataStream
            .process(new FetchAlarmsAndProtectionsFromBatteryStatus())
            .name("alarms_protections_from_battery_status")
            .uid("alarms_protections_from_battery_status")
            .setParallelism(1);
    ;

    // Let's print how the stream records look like.
    stream
        .print("BatteryAlarmsPrint::>> ")
        .name("BatteryAlarmsPrint")
        .uid("BatteryAlarmsPrint")
        .setParallelism(1);

    // Convert it to map.
    final String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "alarm",
          "protection",
          "alarm_name",
          "protection_name",
          "part_type"
        };

    // Let's make them into maps.
    DataStream<Row> alarmsAndProtections =
        stream
            .process(new BatteryAlarmsAndProtectionsToRowMapper())
            .name("alarms_and_protections_to_row")
            .uid("alarms_and_protections_to_row")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.STRING,
                    Types.STRING,
                    Types.STRING));

    // Let's make it into rows.

    // Get a temporary view.
    tableEnv.createTemporaryView(
        "bms_alarms_protections",
        alarmsAndProtections,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `bms_alarm_protection_late`"));

    lateEvents
        .print("BmsAlarmProtectionLateEvents::>> ")
        .name("bms_alarm_protection_late_events")
        .uid("bms_alarm_protection_late_events")
        .setParallelism(1);
    this.alarmsAndProtectionsWriter.save(lateEvents, true,false, false);

    return tableEnv.sqlQuery(sql);
  }

  // Maps alarms and protections to rows.
  public static class BatteryAlarmsAndProtectionsToRowMapper
      extends ProcessFunction<
          Tuple3<Timestamp, Metadata, Tuple2<List<Alarm>, List<Protection>>>, Row> {

    private static final long serialVersionUID = 131159592693919469L;

    @Override
    public void processElement(
        Tuple3<Timestamp, Metadata, Tuple2<List<Alarm>, List<Protection>>> value,
        ProcessFunction<Tuple3<Timestamp, Metadata, Tuple2<List<Alarm>, List<Protection>>>, Row>
                .Context
            ctx,
        Collector<Row> out)
        throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      Tuple2<List<Alarm>, List<Protection>> alarmsAndProtections = value.f2;
      // List of alarms.
      List<Alarm> alarms = alarmsAndProtections.f0;
      // List of protections.
      List<Protection> protections = alarmsAndProtections.f1;
      int size =
          Math.max(
              alarms != null ? alarms.size() : 0, protections != null ? protections.size() : 0);
      for (int i = 0; i < size; i++) {
        assert alarms != null;
        assert protections != null;
        out.collect(
            Row.of(
                Instant.ofEpochSecond(timestamp.getTimestamp()),
                timestamp.getTimestamp(),
                timestamp.getIngestionTime(),
                metadata.getCorrelationId(),
                metadata.getImei(),
                i < alarms.size() && alarms.get(i) != null && alarms.get(i).isSet(),
                i < protections.size() && protections.get(i) != null && protections.get(i).isSet(),
                i < alarms.size() && alarms.get(i) != null
                    ? alarms.get(i).getAlarmType().name()
                    : "NO_PROTECTION",
                i < protections.size() && protections.get(i) != null
                    ? protections.get(i).getProtectionType().name()
                    : "NO_PROTECTION",
                "BMS"));
      }
    }
  }

  private static class FetchAlarmsAndProtectionsFromBatteryStatus
      extends ProcessFunction<
          Tuple3<Timestamp, Metadata, BatteryStatus>,
          Tuple3<Timestamp, Metadata, Tuple2<List<Alarm>, List<Protection>>>> {
    private static final long serialVersionUID = 1492206333797395087L;

    @Override
    public void processElement(
        Tuple3<Timestamp, Metadata, BatteryStatus> value,
        ProcessFunction<
                    Tuple3<Timestamp, Metadata, BatteryStatus>,
                    Tuple3<Timestamp, Metadata, Tuple2<List<Alarm>, List<Protection>>>>
                .Context
            ctx,
        Collector<Tuple3<Timestamp, Metadata, Tuple2<List<Alarm>, List<Protection>>>> out)
        throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      List<Alarm> alarms = value.f2.getAlarms();
      List<Protection> protections = value.f2.getProtections();
      Tuple2<List<Alarm>, List<Protection>> alarmsAndProtections =
          new Tuple2<>(
              alarms != null ? alarms : new ArrayList<>(),
              protections != null ? protections : new ArrayList<>());
      out.collect(new Tuple3<>(timestamp, metadata, alarmsAndProtections));
    }
  }
}
