package com.nichesolv.nds.datacleaning.function.transformer.bms;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.nds.datacleaning.function.cache.VehicleAttributeFinder;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.BatteryCellTemperature;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.CellTemperature;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.time.Instant;
import java.util.List;
import java.util.Map;

import org.apache.commons.text.StringEscapeUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("batteryCellTemperatureTransformer")
public class BatteryCellTemperatureTransformer {

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/bms_cell_temp.sql')}")
  private String sql;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/bms_cell_temp_late_events.sql')}")
  private String lateEventsSql;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:error/bms_cell_temp_error.sql')}")
  private String errorEventsSql;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/bms_status_temp_max_and_min_from_cell_temp.sql')}")
  private String batteryStatusTempMaxAndMinSql;

  @Autowired
  @Qualifier("batteryTemperatureWriter")
  private AbstractWriter<Row> batteryTemperatureWriter;

  @Autowired
  @Qualifier("batteryStatusTempMaxAndMinWriter")
  private AbstractWriter<Row> batteryStatusTempMaxAndMinWriter;

  @Autowired
  private RedisConnectionManagerFactory2 redisConnectionManagerFactory;

  public Table transform(
          StreamExecutionEnvironment env,
          StreamTableEnvironment tableEnv,
          DataStream<Tuple3<Timestamp, Metadata, BatteryCellTemperature>> dataStream) {

    RedisConnectionManager2 redisConnectionManager =
            redisConnectionManagerFactory.createRedisConnectionManager();

    // Let's print the stream for inspection.
    dataStream
            .print("BatteryManagementSystemCellTemperature::>> ")
            .name("battery_management_system_temperature")
            .uid("battery_management_system_temperature")
            .setParallelism(1);

    // Convert it to map.
    final String[] fieldNames =
            new String[] {
                    "event_time",
                    "observed_timestamp",
                    "ingestion_timestamp",
                    "correlation_id",
                    "imei",
                    "stack_id", // INTEGER
                    "temperature",
                    "maxTemp",
                    "minTemp",
                    "battery_cell_temperature_upper_limit",
                    "battery_cell_temperature_lower_limit"
            };

    // Data stream of rows.
    DataStream<Row> batteryTemperature =
            dataStream
                    .process(new BatteryCellTemperatureToRowMapper(redisConnectionManager))
                    .name("battery_management_system_cell_temp_to_row_mapper")
                    .uid("battery_management_system_cell_temp_to_row_mapper")
                    .setParallelism(1)
                    .returns(
                            Types.ROW_NAMED(
                                    fieldNames,
                                    Types.INSTANT,
                                    Types.INT,
                                    Types.LONG,
                                    Types.STRING,
                                    Types.LONG,
                                    Types.INT,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.FLOAT));

    OutputTag<Row> batteryStatusTempMaxAndMinWriterStreamTag =
            new OutputTag<Row>("batteryStatusTempMaxAndMinWriterStream") {
              private static final long serialVersionUID = -480336470575956756L;
            };

    OutputTag<Row> temperatureStreamTag =
            new OutputTag<Row>("temperatureStream") {
              private static final long serialVersionUID = -3369837270848731296L;
            };

    SingleOutputStreamOperator<Row> processStream =
            batteryTemperature.process(
                    new ProcessFunction<Row, Row>() {

                      private static final long serialVersionUID = -5327536566184926954L;

                      public void processElement(Row value, Context ctx, Collector<Row> out)
                              throws Exception {
                        int stackId = value.getFieldAs("stack_id");
                        Row row = Row.withNames();
                        row.setField("event_time", value.getFieldAs("event_time"));
                        row.setField("observed_timestamp", value.getFieldAs("observed_timestamp"));
                        row.setField("ingestion_timestamp", value.getFieldAs("ingestion_timestamp"));
                        row.setField("correlation_id", value.getFieldAs("correlation_id"));
                        row.setField("imei", value.getFieldAs("imei"));
                        row.setField("stack_id", stackId);
                        row.setField("temperature", value.getFieldAs("temperature"));
                        row.setField(
                                "battery_cell_temperature_upper_limit",
                                value.getFieldAs("battery_cell_temperature_upper_limit"));
                        row.setField(
                                "battery_cell_temperature_lower_limit",
                                value.getFieldAs("battery_cell_temperature_lower_limit"));

                        //                out.collect(row);

                        if (stackId != -1) {
                          ctx.output(temperatureStreamTag, row);
                        } else {
                          row.setField("maxTemp", value.getFieldAs("maxTemp"));
                          row.setField("minTemp", value.getFieldAs("minTemp"));
                          ctx.output(batteryStatusTempMaxAndMinWriterStreamTag, row);
                        }
                      }
                    });

    DataStream<Row> temperatureStreamSideOutput = processStream.getSideOutput(temperatureStreamTag);
    DataStream<Row> batteryStatusTempMaxAndMinWriterStreamSideOutput =
            processStream.getSideOutput(batteryStatusTempMaxAndMinWriterStreamTag);

    temperatureStreamSideOutput.print("TemperatureStreamSideOutput::>> ").setParallelism(1);
    DataStream<Row> temperatureStreamMain =
            temperatureStreamSideOutput
                    .map(new BmsStatusToRowMapper())
                    .name("battery_management_system_cell_temp_to_row_mapper_new")
                    .uid("battery_management_system_cell_temp_to_row_mapper_new")
                    .setParallelism(1)
                    .returns(
                            Types.ROW_NAMED(
                                    new String[] {
                                            "event_time",
                                            "observed_timestamp",
                                            "ingestion_timestamp",
                                            "correlation_id",
                                            "imei",
                                            "stack_id", // INTEGER
                                            "temperature",
                                            "battery_cell_temperature_upper_limit",
                                            "battery_cell_temperature_lower_limit"
                                    },
                                    Types.INSTANT,
                                    Types.INT,
                                    Types.LONG,
                                    Types.STRING,
                                    Types.LONG,
                                    Types.INT,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.FLOAT));

    temperatureStreamMain.print("TemperatureStreamSideOutputNew::>> ").setParallelism(1);

    // Get a temporary view.
    tableEnv.createTemporaryView(
            "bms_cell_temp",
            temperatureStreamMain,
            Schema.newBuilder()
                    .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
                    .columnByExpression("proc_time", "PROCTIME()")
                    .watermark("rowtime", "SOURCE_WATERMARK()")
                    .build());

    DataStream<Row> batteryStatusTempMaxAndMinStreamMain =
            batteryStatusTempMaxAndMinWriterStreamSideOutput
                    .map(new BmsStatus2ToRowMapper())
                    .name("battery_management_system_cell_temp_to_row_mapper_testing_2")
                    .uid("battery_management_system_cell_temp_to_row_mapper_testing_2")
                    .setParallelism(1)
                    .returns(
                            Types.ROW_NAMED(
                                    new String[] {
                                            "event_time",
                                            "observed_timestamp",
                                            "ingestion_timestamp",
                                            "correlation_id",
                                            "imei",
                                            "stack_id", // INTEGER
                                            "temperature",
                                            "maxTemp",
                                            "minTemp",
                                            "battery_cell_temperature_upper_limit",
                                            "battery_cell_temperature_lower_limit"
                                    },
                                    Types.INSTANT,
                                    Types.INT,
                                    Types.LONG,
                                    Types.STRING,
                                    Types.LONG,
                                    Types.INT,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.FLOAT));

    batteryStatusTempMaxAndMinStreamMain
            .print("BatteryStatusTempMaxAndMinStreamMain::>> ")
            .setParallelism(1);

    tableEnv.createTemporaryView(
            "bms_status_temp_max_and_min_from_cell_temp",
            batteryStatusTempMaxAndMinStreamMain,
            Schema.newBuilder()
                    .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
                    .columnByExpression("proc_time", "PROCTIME()")
                    .watermark("rowtime", "SOURCE_WATERMARK()")
                    .build());


    // Events for bms_status_temp_max_and_min_from_cell_temp_error need not be populated as they are eliminated in BatteryCellTemperatureToRowMapper

    /*
    Events for bms_status_temp_max_and_min_from_cell_temp
     */
    Table batteryStatusTempMaxAndMinFromCellTempTable =
            tableEnv.sqlQuery(batteryStatusTempMaxAndMinSql);

    DataStream<Row> batteryStatusTempMinAndMaxFromCellTempStream =
            tableEnv.toDataStream(batteryStatusTempMaxAndMinFromCellTempTable);

    // print
    batteryStatusTempMinAndMaxFromCellTempStream
            .print("BatteryStatusTempMinAndMaxFromCellTemp::>> ")
            .setParallelism(1);

    // Write battery status to the db
    this.batteryStatusTempMaxAndMinWriter.save(
            batteryStatusTempMinAndMaxFromCellTempStream, false, false, false);

    /*
    Late Events for bms_cell_temp_late
     */
    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
            tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `bms_cell_temp_late`"));

    lateEvents
            .print("BmsCellTempLateEvents::>> ")
            .name("bms_cell_temp_late_events")
            .uid("bms_cell_temp_late_events")
            .setParallelism(1);

    this.batteryTemperatureWriter.save(lateEvents, true, false, false);

    DataStream<Row> errorEvents = tableEnv.toDataStream(tableEnv.sqlQuery(errorEventsSql));

    // error events
    this.batteryTemperatureWriter.save(errorEvents, false, true, false);

    // Enrich with vehicle_id, mfg_id etc...
    return tableEnv.sqlQuery(sql);
  }

  public static class BatteryCellTemperatureToRowMapper
          extends ProcessFunction<Tuple3<Timestamp, Metadata, BatteryCellTemperature>, Row> {

    private static final Logger LOG =
            LoggerFactory.getLogger(BatteryCellTemperatureToRowMapper.class);

    private static final long serialVersionUID = -377334779674839191L;

    private final RedisConnectionManager2 redisConnectionManager;

    public BatteryCellTemperatureToRowMapper(RedisConnectionManager2 redisConnectionManager) {
      this.redisConnectionManager = redisConnectionManager;
    }

    @Override
    public void open(Configuration parameters) {
      try {
        redisConnectionManager.init();
        LOG.info("RedisConnectionManager initialized successfully for BatteryCellTemperatureToRowMapper.");
      } catch (Exception e) {
        LOG.error("Error initializing RedisConnectionManager in BatteryCellTemperatureToRowMapper", e);
        throw new RuntimeException("Failed to initialize Redis connection in BatteryCellTemperatureToRowMapper.", e);
      }
    }

    @Override
    public void processElement(
            Tuple3<Timestamp, Metadata, BatteryCellTemperature> value,
            ProcessFunction<Tuple3<Timestamp, Metadata, BatteryCellTemperature>, Row>.Context ctx,
            Collector<Row> out)
            throws Exception {

      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      long imei = metadata.getImei();
      final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
      String imeiVehicleModelVariant =
              redisConnectionManager.getValue("IMEI_VEHICLE_MODEL_VARIANT_KEY::" + imei);
      float batteryCellTemperatureUpperLimit = 80F, batteryCellTemperatureLowerLimit = -25F;
      if (imeiVehicleModelVariant != null && !imeiVehicleModelVariant.isEmpty()) {

        if (imeiVehicleModelVariant.startsWith("\"") && imeiVehicleModelVariant.endsWith("\"")) {
          imeiVehicleModelVariant =
                  imeiVehicleModelVariant.substring(1, imeiVehicleModelVariant.length() - 1);
        }

        String vehicleModelVariant = redisConnectionManager.getValue(imeiVehicleModelVariant);
        vehicleModelVariant = StringEscapeUtils.unescapeJava(vehicleModelVariant);

        if (vehicleModelVariant.startsWith("\"") && vehicleModelVariant.endsWith("\"")) {
          vehicleModelVariant = vehicleModelVariant.substring(1, vehicleModelVariant.length() - 1);
        }
        JsonNode vehicleData = objectMapper.readTree(vehicleModelVariant);

        // Initialize the attribute finder with JSON data
        VehicleAttributeFinder finder = new VehicleAttributeFinder(vehicleData);

        // Example usage for multiple attributes
        String partType = "BATTERY";
        String[] attributeNames = {
                "batteryCellTemperatureUpperLimit", "batteryCellTemperatureLowerLimit"
        };

        // Retrieve multiple specific attributes
        Map<String, String> specificAttributes =
                finder.getSpecificAttributesByPartType(partType, attributeNames);

        batteryCellTemperatureUpperLimit =
                Float.parseFloat(specificAttributes.get("batteryCellTemperatureUpperLimit"));
        batteryCellTemperatureLowerLimit =
                Float.parseFloat(specificAttributes.get("batteryCellTemperatureLowerLimit"));
      }

      List<CellTemperature> cellTemperatures = value.f2.getCellTemperatures();
      LOG.info("CellTempSizeBatteryCellTemperatureToRowMapper: {}", cellTemperatures.size());
      Instant eventTime = Instant.ofEpochSecond(timestamp.getTimestamp());
      if (!cellTemperatures.isEmpty()) {
        float maxTemp = batteryCellTemperatureLowerLimit;
        float minTemp = batteryCellTemperatureUpperLimit;
        for (CellTemperature cellTemperature : cellTemperatures) {
          if (maxTemp < cellTemperature.getTemperature()
                  && cellTemperature.getTemperature() < batteryCellTemperatureUpperLimit) {
            maxTemp = cellTemperature.getTemperature();
          }
          if (minTemp > cellTemperature.getTemperature()
                  && cellTemperature.getTemperature() > batteryCellTemperatureLowerLimit) {
            minTemp = cellTemperature.getTemperature();
          }
          Row row = Row.withNames();
          row.setField("event_time", eventTime);
          row.setField("observed_timestamp", timestamp.getTimestamp());
          row.setField("ingestion_timestamp", timestamp.getIngestionTime());
          row.setField("correlation_id", metadata.getCorrelationId());
          row.setField("imei", metadata.getImei());
          row.setField("stack_id", cellTemperature.getId());
          row.setField("temperature", cellTemperature.getTemperature());
          row.setField("maxTemp", -1F);
          row.setField("minTemp", -1F);
          row.setField("battery_cell_temperature_upper_limit", batteryCellTemperatureUpperLimit);
          row.setField("battery_cell_temperature_lower_limit", batteryCellTemperatureLowerLimit);
          out.collect(row);
        }
        Row row = Row.withNames();
        row.setField("event_time", eventTime);
        row.setField("observed_timestamp", timestamp.getTimestamp());
        row.setField("ingestion_timestamp", timestamp.getIngestionTime());
        row.setField("correlation_id", metadata.getCorrelationId());
        row.setField("imei", metadata.getImei());
        row.setField("stack_id", -1);
        row.setField("temperature", -1F);
        row.setField("maxTemp", maxTemp);
        row.setField("minTemp", minTemp);
        row.setField("battery_cell_temperature_upper_limit", batteryCellTemperatureUpperLimit);
        row.setField("battery_cell_temperature_lower_limit", batteryCellTemperatureLowerLimit);
        out.collect(row);
      }
    }
  }

  public static class BmsStatusToRowMapper extends RichMapFunction<Row, Row> {

    private static final long serialVersionUID = -3103307643038418856L;

    @Override
    public Row map(Row value) throws Exception {
      return Row.of(
              value.getField("event_time"),
              value.getField("observed_timestamp"),
              value.getField("ingestion_timestamp"),
              value.getField("correlation_id"),
              value.getField("imei"),
              value.getField("stack_id"),
              value.getField("temperature"),
              value.getField("battery_cell_temperature_upper_limit"),
              value.getField("battery_cell_temperature_lower_limit"));
    }
  }

  public static class BmsStatus2ToRowMapper extends RichMapFunction<Row, Row> {

    private static final long serialVersionUID = -3103307643038418856L;

    @Override
    public Row map(Row value) throws Exception {
      return Row.of(
              value.getField("event_time"),
              value.getField("observed_timestamp"),
              value.getField("ingestion_timestamp"),
              value.getField("correlation_id"),
              value.getField("imei"),
              value.getField("stack_id"),
              value.getField("temperature"),
              value.getField("maxTemp"),
              value.getField("minTemp"),
              value.getField("battery_cell_temperature_upper_limit"),
              value.getField("battery_cell_temperature_lower_limit"));
    }
  }
}