package com.nichesolv.nds.datacleaning.function.transformer.bms;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.nds.datacleaning.function.cache.VehicleAttributeFinder;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BalancingStatus;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BatteryStatus;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltage;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.CellVoltage;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.metadata.MetadataImpl;
import com.nichesolv.nds.model.domain.CompositeKey;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.text.StringEscapeUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.tuple.Tuple6;
import org.apache.flink.api.java.tuple.Tuple9;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("batteryCellVoltageTransformer")
public class BatteryCellVoltageTransformer {

  @Value("${appConfig.streaming.maxParallelism}")
  private Integer maxParallelism;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/cell_voltage_and_balancing_status.sql')}")
  private String sql;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/cell_voltage_and_balancing_status_late_events.sql')}")
  private String lateEventsSql;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/bms_status_volt_max_and_min_from_cell_volt.sql')}")
  private String batteryStatusVoltMaxAndMinSql;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:error/cell_voltage_and_balancing_status_error.sql')}")
  private String errorEventsSql;

  @Autowired
  @Qualifier("cellVoltagesWriter")
  private AbstractWriter<Row> batteryCellVoltagesWriter;

  @Autowired
  @Qualifier("batteryStatusVoltMaxAndMinWriter")
  private AbstractWriter<Row> batteryStatusVoltMaxAndMinWriter;

  @Autowired
  private RedisConnectionManagerFactory2 redisConnectionManagerFactory;

  public Table transform(
          StreamExecutionEnvironment env,
          StreamTableEnvironment tableEnv,
          DataStream<Tuple3<Timestamp, Metadata, BatteryStatus>> batteryStatusStream,
          DataStream<Tuple3<Timestamp, Metadata, BatteryCellVoltage>> batteryCellVoltageStream) {

    RedisConnectionManager2 redisConnectionManager =
            redisConnectionManagerFactory.createRedisConnectionManager();

    // Convert it to map.
    final String[] balancingStatusRowFieldNames =
            new String[] {
                    "event_time",
                    "observed_timestamp",
                    "ingestion_timestamp",
                    "correlation_id",
                    "imei",
                    "cell_id", // INTEGER
                    "balancing_status", // BOOLEAN
                    "volts",
                    "is_balancing_status",
                    "minVolt",
                    "maxVolt",
                    "voltage_sensor_count",
                    "battery_cell_voltage_lower_limit",
                    "battery_cell_voltage_upper_limit"
            };

    // Battery status and cell voltages streams need to be joined.
    // KeyBy is an important aspect of working with connected streams.
    DataStream<Row> balancingStatusRow =
            batteryStatusStream
                    .process(new BalancingStatusRowBuilder())
                    .name("battery_status_row_builder")
                    .uid("battery_status_row_builder")
                    .setParallelism(1)
                    .returns(
                            Types.ROW_NAMED(
                                    balancingStatusRowFieldNames,
                                    Types.INSTANT,
                                    Types.INT,
                                    Types.LONG,
                                    Types.STRING,
                                    Types.LONG,
                                    Types.INT,
                                    Types.BOOLEAN,
                                    Types.FLOAT,
                                    Types.BOOLEAN,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.INT,
                                    Types.FLOAT,
                                    Types.FLOAT));

    // Convert it to map.
    final String[] cellVoltagesRowFieldNames =
            new String[] {
                    "event_time",
                    "observed_timestamp",
                    "ingestion_timestamp",
                    "correlation_id",
                    "imei",
                    "cell_id", // INTEGER
                    "balancing_status", // BOOLEAN
                    "volts",
                    "is_balancing_status", // BOOLEAN
                    "minVolt",
                    "maxVolt",
                    "voltage_sensor_count",
                    "battery_cell_voltage_lower_limit",
                    "battery_cell_voltage_upper_limit"
            };

    // Row builder for battery cell voltage stream.
    DataStream<Row> cellVoltagesRow =
            batteryCellVoltageStream
                    .process(new BatteryCellVoltageRowBuilder(redisConnectionManager))
                    .name("battery_cell_voltages_row_builder")
                    .uid("battery_cell_voltages_row_builder")
                    .setParallelism(1)
                    .returns(
                            Types.ROW_NAMED(
                                    cellVoltagesRowFieldNames,
                                    Types.INSTANT,
                                    Types.INT,
                                    Types.LONG,
                                    Types.STRING,
                                    Types.LONG,
                                    Types.INT,
                                    Types.BOOLEAN,
                                    Types.FLOAT,
                                    Types.BOOLEAN,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.INT,
                                    Types.FLOAT,
                                    Types.FLOAT));

    // We form the final object that represents a record in the cell voltages table.
    KeyedStream<Row, CompositeKey> keyedStream =
            balancingStatusRow.union(cellVoltagesRow).keyBy(new CompositeKey.FromRow());

    // Convert it to map.
    final String[] recordFieldNames =
            new String[] {
                    "event_time",
                    "observed_timestamp",
                    "ingestion_timestamp",
                    "correlation_id",
                    "imei",
                    "cell_id", // INTEGER
                    "balancing_status", // BOOLEAN
                    "volts", // INTEGER
                    "minVolt",
                    "maxVolt",
                    "voltage_sensor_count",
                    "battery_cell_voltage_lower_limit",
                    "battery_cell_voltage_upper_limit"
            };

    // Let's transform the stream.
    DataStream<Row> voltageStream =
            keyedStream
                    .process(new TableRecordBuilder1())
                    .name("balancing_status_and_cell_voltage_combiner")
                    .uid("balancing_status_and_cell_voltage_combiner")
                    .setParallelism(1)
                    .setMaxParallelism(maxParallelism)
                    .returns(
                            Types.ROW_NAMED(
                                    recordFieldNames,
                                    Types.INSTANT,
                                    Types.INT,
                                    Types.LONG,
                                    Types.STRING,
                                    Types.LONG,
                                    Types.INT,
                                    Types.BOOLEAN,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.INT,
                                    Types.FLOAT,
                                    Types.FLOAT));

    /*
    BMS voltage max and min from individual cell voltages sensor
     */

    OutputTag<Row> batteryStatusVoltMaxAndMinOutputTag =
            new OutputTag<Row>("batteryStatusVoltMaxAndMinOutputTag") {
              private static final long serialVersionUID = -480336470575956756L;
            };

    OutputTag<Row> voltageOutputTag =
            new OutputTag<Row>("voltageOutputTag") {
              private static final long serialVersionUID = -3369837270848731296L;
            };

    SingleOutputStreamOperator<Row> processStream =
            voltageStream.process(
                    new ProcessFunction<Row, Row>() {

                      private static final long serialVersionUID = -5327536566184926954L;

                      public void processElement(Row value, Context ctx, Collector<Row> out)
                              throws Exception {
                        int cellId = value.getFieldAs("cell_id");
                        Row row = Row.withNames();
                        row.setField("event_time", value.getFieldAs("event_time"));
                        row.setField("observed_timestamp", value.getFieldAs("observed_timestamp"));
                        row.setField("ingestion_timestamp", value.getFieldAs("ingestion_timestamp"));
                        row.setField("correlation_id", value.getFieldAs("correlation_id"));
                        row.setField("imei", value.getFieldAs("imei"));
                        row.setField("cell_id", cellId);
                        row.setField("balancing_status", value.getFieldAs("balancing_status"));
                        row.setField("volts", value.getFieldAs("volts"));
                        row.setField("voltage_sensor_count", value.getFieldAs("voltage_sensor_count"));
                        row.setField(
                                "battery_cell_voltage_lower_limit",
                                value.getFieldAs("battery_cell_voltage_lower_limit"));
                        row.setField(
                                "battery_cell_voltage_upper_limit",
                                value.getFieldAs("battery_cell_voltage_upper_limit"));
                        //                out.collect(row);

                        if (cellId != -1) {
                          ctx.output(voltageOutputTag, row);
                        } else {
                          row.setField("minVolt", value.getFieldAs("minVolt"));
                          row.setField("maxVolt", value.getFieldAs("maxVolt"));
                          ctx.output(batteryStatusVoltMaxAndMinOutputTag, row);
                        }
                      }
                    });

    DataStream<Row> voltageStreamSideOutput = processStream.getSideOutput(voltageOutputTag);

    DataStream<Row> batteryStatusVoltMaxAndMinWriterStreamSideOutput =
            processStream.getSideOutput(batteryStatusVoltMaxAndMinOutputTag);

    voltageStreamSideOutput.print("VoltageStreamSideOutput::>> ").setParallelism(1);

    DataStream<Row> voltageStreamMain =
            voltageStreamSideOutput
                    .map(new BmsStatusToRowMapper())
                    .name("battery_management_system_cell_voltage_to_row_mapper_new")
                    .uid("battery_management_system_cell_voltage_to_row_mapper_new")
                    .setParallelism(1)
                    .returns(
                            Types.ROW_NAMED(
                                    new String[] {
                                            "event_time",
                                            "observed_timestamp",
                                            "ingestion_timestamp",
                                            "correlation_id",
                                            "imei",
                                            "cell_id",
                                            "balancing_status",
                                            "volts",
                                            "voltage_sensor_count",
                                            "battery_cell_voltage_lower_limit",
                                            "battery_cell_voltage_upper_limit"
                                    },
                                    Types.INSTANT,
                                    Types.INT,
                                    Types.LONG,
                                    Types.STRING,
                                    Types.LONG,
                                    Types.INT,
                                    Types.BOOLEAN,
                                    Types.FLOAT,
                                    Types.INT,
                                    Types.FLOAT,
                                    Types.FLOAT));

    voltageStreamMain.print("VoltageStreamSideOutputNew::>> ").setParallelism(1);

    // Get a temporary view.
    tableEnv.createTemporaryView(
            "bms_cell_voltages",
            voltageStreamMain,
            Schema.newBuilder()
                    .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
                    .columnByExpression("proc_time", "PROCTIME()")
                    .watermark("rowtime", "SOURCE_WATERMARK()")
                    .build());

    DataStream<Row> batteryStatusVoltMaxAndMinStreamMain =
            batteryStatusVoltMaxAndMinWriterStreamSideOutput
                    .map(new BmsStatus2ToRowMapper())
                    .name("battery_management_system_cell_voltage_to_row_mapper_testing_2")
                    .uid("battery_management_system_cell_voltage_to_row_mapper_testing_2")
                    .setParallelism(1)
                    .returns(
                            Types.ROW_NAMED(
                                    new String[] {
                                            "event_time",
                                            "observed_timestamp",
                                            "ingestion_timestamp",
                                            "correlation_id",
                                            "imei",
                                            "minVolt",
                                            "maxVolt",
                                            "battery_cell_voltage_lower_limit",
                                            "battery_cell_voltage_upper_limit"
                                    },
                                    Types.INSTANT,
                                    Types.INT,
                                    Types.LONG,
                                    Types.STRING,
                                    Types.LONG,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.FLOAT,
                                    Types.FLOAT));

    batteryStatusVoltMaxAndMinStreamMain
            .print("BatteryStatusVoltMaxAndMinStreamMain::>> ")
            .setParallelism(1);

    tableEnv.createTemporaryView(
            "bms_status_volt_max_and_min_from_cell_volt",
            batteryStatusVoltMaxAndMinStreamMain,
            Schema.newBuilder()
                    .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
                    .columnByExpression("proc_time", "PROCTIME()")
                    .watermark("rowtime", "SOURCE_WATERMARK()")
                    .build());

    // Events for batteryStatusVoltMaxAndMinSqlError need not be populated as they are eliminated in BatteryCellVoltageRowBuilder

    /*
    cell voltage min and max events
     */
    Table batteryStatusVoltMaxAndMinFromCellVoltTable =
            tableEnv.sqlQuery(batteryStatusVoltMaxAndMinSql);

    DataStream<Row> batteryStatusVoltMinAndMaxFromCellVoltStream =
            tableEnv.toDataStream(batteryStatusVoltMaxAndMinFromCellVoltTable);

    // print
    batteryStatusVoltMinAndMaxFromCellVoltStream
            .print("BatteryStatusVoltMinAndMaxFromCellVolt::>> ")
            .setParallelism(1);

    // Write battery status to the db
    this.batteryStatusVoltMaxAndMinWriter.save(batteryStatusVoltMinAndMaxFromCellVoltStream, false,false, false);

    /*
    late cell voltages sensor events
     */

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
            tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `bms_cell_voltage_late`"));

    lateEvents
            .print("BmsCellVoltageLateEvents::>> ")
            .name("bms_cell_voltage_late_events")
            .uid("bms_cell_voltage_late_events")
            .setParallelism(1);

    this.batteryCellVoltagesWriter.save(lateEvents, true, false,false);

    DataStream<Row> errorEvents = tableEnv.toDataStream(tableEnv.sqlQuery(errorEventsSql));
    // error events
    this.batteryCellVoltagesWriter.save(errorEvents, false, true,false);

    // Enrich with vehicle_id, mfg_id etc...
    return tableEnv.sqlQuery(sql);
  }

  /** Builds balancing status records. */
  public static class BalancingStatusRowBuilder
          extends ProcessFunction<Tuple3<Timestamp, Metadata, BatteryStatus>, Row> {

    private static final long serialVersionUID = -8363297397325246505L;

    @Override
    public void processElement(
            Tuple3<Timestamp, Metadata, BatteryStatus> value,
            ProcessFunction<Tuple3<Timestamp, Metadata, BatteryStatus>, Row>.Context ctx,
            Collector<Row> out)
            throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      List<BalancingStatus> balancingStatuses = value.f2.getBalancingStatuses();

      if (balancingStatuses != null && balancingStatuses.size() > 0) {
        for (BalancingStatus balancingStatus : balancingStatuses) {
          Row row = Row.withNames();
          row.setField("event_time", Instant.ofEpochSecond(timestamp.getTimestamp()));
          row.setField("observed_timestamp", timestamp.getTimestamp());
          row.setField("ingestion_timestamp", timestamp.getIngestionTime());
          row.setField("correlation_id", metadata.getCorrelationId());
          row.setField("imei", metadata.getImei());
          row.setField("cell_id", balancingStatus.getCellId());
          row.setField("balancing_status", balancingStatus.isStatus());
          row.setField("volts", 0.0F);
          row.setField("is_balancing_status", true);
          row.setField("minVolt", -1.0F);
          row.setField("maxVolt", -1.0F);
          row.setField("voltage_sensor_count", -1);
          row.setField("battery_cell_voltage_lower_limit", -1);
          row.setField("battery_cell_voltage_upper_limit", -1);
        }
      }
    }
  }

  /** Builds cell voltages. */
  public static class BatteryCellVoltageRowBuilder
          extends ProcessFunction<Tuple3<Timestamp, Metadata, BatteryCellVoltage>, Row> {

    private static final long serialVersionUID = 550862106226857424L;
    private static final Logger LOG =
            LoggerFactory.getLogger(BatteryCellVoltageTransformer.BatteryCellVoltageRowBuilder.class);

    private final RedisConnectionManager2 redisConnectionManager;

    public BatteryCellVoltageRowBuilder(RedisConnectionManager2 redisConnectionManager) {
      this.redisConnectionManager = redisConnectionManager;
    }

    @Override
    public void open(Configuration parameters) {
      try {
        redisConnectionManager.init();
        LOG.info("RedisConnectionManager initialized successfully for BatteryCellVoltageRowBuilder.");
      } catch (Exception e) {
        LOG.error("Error initializing RedisConnectionManager in BatteryCellVoltageRowBuilder", e);
        throw new RuntimeException("Failed to initialize Redis connection in BatteryCellVoltageRowBuilder.", e);
      }
    }

    @Override
    public void processElement(
            Tuple3<Timestamp, Metadata, BatteryCellVoltage> value,
            ProcessFunction<Tuple3<Timestamp, Metadata, BatteryCellVoltage>, Row>.Context ctx,
            Collector<Row> out)
            throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      List<CellVoltage> cellVoltages = value.f2.getCellVoltages();
      long imei = metadata.getImei();
      final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
      String imeiVehicleModelVariant =
              redisConnectionManager.getValue("IMEI_VEHICLE_MODEL_VARIANT_KEY::" + imei);
      int voltageSensorCount = 24;
      float batteryCellVoltageUpperLimit = 5F, batteryCellVoltageLowerLimit = 0F;
      if (imeiVehicleModelVariant != null && !imeiVehicleModelVariant.isEmpty()) {

        if (imeiVehicleModelVariant.startsWith("\"") && imeiVehicleModelVariant.endsWith("\"")) {
          imeiVehicleModelVariant =
                  imeiVehicleModelVariant.substring(1, imeiVehicleModelVariant.length() - 1);
        }

        String vehicleModelVariant = redisConnectionManager.getValue(imeiVehicleModelVariant);
        vehicleModelVariant = StringEscapeUtils.unescapeJava(vehicleModelVariant);

        if (vehicleModelVariant.startsWith("\"") && vehicleModelVariant.endsWith("\"")) {
          vehicleModelVariant = vehicleModelVariant.substring(1, vehicleModelVariant.length() - 1);
        }
        JsonNode vehicleData = objectMapper.readTree(vehicleModelVariant);

        // Initialize the attribute finder with JSON data
        VehicleAttributeFinder finder = new VehicleAttributeFinder(vehicleData);

        // Example usage for multiple attributes
        String partType = "BATTERY";
        String[] attributeNames = {
                "voltageSensorCount", "batteryCellVoltageUpperLimit", "batteryCellVoltageLowerLimit"
        };

        // Retrieve multiple specific attributes
        Map<String, String> specificAttributes =
                finder.getSpecificAttributesByPartType(partType, attributeNames);

        voltageSensorCount = Integer.parseInt(specificAttributes.get("voltageSensorCount"));
        batteryCellVoltageLowerLimit =
                Float.parseFloat(specificAttributes.get("batteryCellVoltageLowerLimit"));
        batteryCellVoltageUpperLimit =
                Float.parseFloat(specificAttributes.get("batteryCellVoltageUpperLimit"));
      }

      if (cellVoltages != null && cellVoltages.size() > 0) {
        float minVolt = batteryCellVoltageUpperLimit;
        float maxVolt = batteryCellVoltageLowerLimit;

        for (CellVoltage cellVoltage : cellVoltages) {
          if (minVolt > cellVoltage.getVolts()
                  && cellVoltage.getVolts() > batteryCellVoltageLowerLimit) {
            minVolt = cellVoltage.getVolts();
          }
          if (maxVolt < cellVoltage.getVolts()
                  && cellVoltage.getVolts() < batteryCellVoltageUpperLimit) {
            maxVolt = cellVoltage.getVolts();
          }
          Row row = Row.withNames();
          row.setField("event_time", Instant.ofEpochSecond(timestamp.getTimestamp()));
          row.setField("observed_timestamp", timestamp.getTimestamp());
          row.setField("ingestion_timestamp", timestamp.getIngestionTime());
          row.setField("correlation_id", metadata.getCorrelationId());
          row.setField("imei", metadata.getImei());
          row.setField("cell_id", cellVoltage.getId());
          row.setField("balancing_status", false);
          row.setField("volts", cellVoltage.getVolts());
          row.setField("is_balancing_status", false);
          row.setField("minVolt", -1.0F);
          row.setField("maxVolt", -1.0F);
          row.setField("battery_cell_voltage_lower_limit", batteryCellVoltageLowerLimit);
          row.setField("battery_cell_voltage_upper_limit", batteryCellVoltageUpperLimit);
          row.setField("voltage_sensor_count", voltageSensorCount);
          out.collect(row);
        }
        Row row = Row.withNames();
        row.setField("event_time", Instant.ofEpochSecond(timestamp.getTimestamp()));
        row.setField("observed_timestamp", timestamp.getTimestamp());
        row.setField("ingestion_timestamp", timestamp.getIngestionTime());
        row.setField("correlation_id", metadata.getCorrelationId());
        row.setField("imei", metadata.getImei());
        row.setField("cell_id", -1);
        row.setField("balancing_status", false);
        row.setField("volts", -1F);
        row.setField("is_balancing_status", false);
        row.setField("minVolt", minVolt);
        row.setField("maxVolt", maxVolt);
        row.setField("battery_cell_voltage_lower_limit", batteryCellVoltageLowerLimit);
        row.setField("battery_cell_voltage_upper_limit", batteryCellVoltageUpperLimit);
        row.setField("voltage_sensor_count", voltageSensorCount);
        out.collect(row);
      }
    }
  }

  // This logic forms the cell voltages table record.
  public static class TableRecordBuilder1 extends KeyedProcessFunction<CompositeKey, Row, Row> {

    private static final Logger logger = LoggerFactory.getLogger(TableRecordBuilder1.class);

    private static final long serialVersionUID = -1315836199410078681L;

    // We use a map state here to map values against the cell ids without much effort.
    private MapState<
            Integer,
            Tuple9<Timestamp, Metadata, Float, Boolean, Float, Float, Integer, Float, Float>>
            mapState;

    @Override
    public void open(Configuration parameters) throws Exception {
      final String mapStateIdentifier = "TableRecordBuilderMapState";
      // This map will store cell ids against balancing status and cell voltages.
      MapStateDescriptor<
              Integer,
              Tuple9<Timestamp, Metadata, Float, Boolean, Float, Float, Integer, Float, Float>>
              mapStateDescriptor =
              new MapStateDescriptor<>(
                      mapStateIdentifier,
                      Types.INT,
                      Types.TUPLE(
                              Types.POJO(Timestamp.class, new HashMap<>()),
                              Types.POJO(Metadata.class, new HashMap<>()),
                              Types.FLOAT,
                              Types.BOOLEAN,
                              Types.FLOAT,
                              Types.FLOAT,
                              Types.INT,
                              Types.FLOAT,
                              Types.FLOAT));
      this.mapState = getRuntimeContext().getMapState(mapStateDescriptor);
    }

    @Override
    public void processElement(
            Row row, KeyedProcessFunction<CompositeKey, Row, Row>.Context ctx, Collector<Row> out)
            throws Exception {
      // For evey row that comes in we need to store it in map state, extract the cell id first.
      Integer cellId = row.getFieldAs("cell_id");
      Boolean isBalancingStatus = row.getFieldAs("is_balancing_status");
      Long imei = row.getFieldAs("imei");
      Integer observedTimestamp = row.getFieldAs("observed_timestamp");
      Long ingestionTimestamp = row.getFieldAs("ingestion_timestamp");
      String correlationId = row.getFieldAs("correlation_id");
      Float minVolt = row.getFieldAs("minVolt");
      Float maxVolt = row.getFieldAs("maxVolt");
      Integer voltageSensorCount = row.getFieldAs("voltage_sensor_count");
      Float batteryCellVoltageLowerLimit = row.getFieldAs("battery_cell_voltage_lower_limit");
      Float batteryCellVoltageUpperLimit = row.getFieldAs("battery_cell_voltage_upper_limit");

      // Timestamp.
      Timestamp timestamp = new TimestampImpl();
      timestamp.setTimestamp(observedTimestamp);
      timestamp.setIngestionTime(ingestionTimestamp);

      // Metadata.
      Metadata metadata = new MetadataImpl();
      metadata.setImei(imei);
      metadata.setCorrelationId(correlationId);

      // Get the cell id tuple from map.
      Tuple9<Timestamp, Metadata, Float, Boolean, Float, Float, Integer, Float, Float> value =
              this.mapState.get(cellId);
      if (isBalancingStatus) {
        Boolean status = row.getFieldAs("balancing_status");
        if (value == null) {
          value =
                  new Tuple9<>(
                          timestamp,
                          metadata,
                          Float.MIN_VALUE,
                          status,
                          minVolt,
                          maxVolt,
                          voltageSensorCount,
                          batteryCellVoltageLowerLimit,
                          batteryCellVoltageUpperLimit);
        } else {
          value.f3 = status;
        }
      } else {
        Float volts = row.getFieldAs("volts");
        if (value == null) {
          value =
                  new Tuple9<>(
                          timestamp,
                          metadata,
                          volts,
                          Boolean.FALSE,
                          minVolt,
                          maxVolt,
                          voltageSensorCount,
                          batteryCellVoltageLowerLimit,
                          batteryCellVoltageUpperLimit);
        } else {
          value.f2 = volts;
        }
      }

      this.mapState.put(cellId, value);
      int time = timestamp.getTimestamp();
      // Fire after 10 milliseconds, which I am assuming is a good amount of time to collect all
      // values for a specific imei - correlationId pair.
      final long window = 10L;
      ctx.timerService().registerEventTimeTimer((time * 1000L) + window);
    }

    @Override
    public void onTimer(
            long firedAt,
            KeyedProcessFunction<CompositeKey, Row, Row>.OnTimerContext ctx,
            Collector<Row> out)
            throws Exception {
      // We get all the values from map state , create a row and output it.
      for (Map.Entry<
              Integer,
              Tuple9<Timestamp, Metadata, Float, Boolean, Float, Float, Integer, Float, Float>>
              entry : this.mapState.entries()) {
        Integer cellId = entry.getKey();
        Timestamp timestamp = entry.getValue().f0;
        Metadata metadata = entry.getValue().f1;
        Float volts = entry.getValue().f2;
        Boolean balancingStatus = entry.getValue().f3;

        // Emit records.
        Row row = Row.withNames();
        row.setField("event_time", Instant.ofEpochSecond(timestamp.getTimestamp()));
        row.setField("observed_timestamp", timestamp.getTimestamp());
        row.setField("ingestion_timestamp", timestamp.getIngestionTime());
        row.setField("correlation_id", metadata.getCorrelationId());
        row.setField("imei", metadata.getImei());
        row.setField("cell_id", cellId);
        row.setField("balancing_status", balancingStatus);
        row.setField("volts", volts);
        row.setField("minVolt", entry.getValue().f4);
        row.setField("maxVolt", entry.getValue().f5);
        row.setField("voltage_sensor_count", entry.getValue().f6);
        row.setField("battery_cell_voltage_lower_limit", entry.getValue().f7);
        row.setField("battery_cell_voltage_upper_limit", entry.getValue().f8);
        out.collect(row);
      }
      this.mapState.clear();
    }
  }

  public static class BmsStatusToRowMapper extends RichMapFunction<Row, Row> {

    private static final long serialVersionUID = -3103307643038418856L;

    @Override
    public Row map(Row value) throws Exception {
      return Row.of(
              value.getField("event_time"),
              value.getField("observed_timestamp"),
              value.getField("ingestion_timestamp"),
              value.getField("correlation_id"),
              value.getField("imei"),
              value.getField("cell_id"),
              value.getField("balancing_status"),
              value.getField("volts"),
              value.getField("voltage_sensor_count"),
              value.getField("battery_cell_voltage_lower_limit"),
              value.getField("battery_cell_voltage_upper_limit"));
    }
  }

  public static class BmsStatus2ToRowMapper extends RichMapFunction<Row, Row> {

    private static final long serialVersionUID = -3103307643038418856L;

    @Override
    public Row map(Row value) throws Exception {
      return Row.of(
              value.getField("event_time"),
              value.getField("observed_timestamp"),
              value.getField("ingestion_timestamp"),
              value.getField("correlation_id"),
              value.getField("imei"),
              value.getField("minVolt"),
              value.getField("maxVolt"),
              value.getField("battery_cell_voltage_lower_limit"),
              value.getField("battery_cell_voltage_upper_limit"));
    }
  }
}