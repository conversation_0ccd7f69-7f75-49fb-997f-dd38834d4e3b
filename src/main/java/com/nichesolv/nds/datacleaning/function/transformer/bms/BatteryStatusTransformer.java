package com.nichesolv.nds.datacleaning.function.transformer.bms;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.nds.datacleaning.function.cache.VehicleAttributeFinder;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.*;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/** Transforms the bms data to a table. */
@Component("bmsStatusTransformer")
public class BatteryStatusTransformer {

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/bms_status.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/bms_status_late_events.sql')}")
  private String lateEventsSql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:error/bms_status_error.sql')}")
  private String errorEventsSql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:error/bms_status_error_late_events.sql')}")
  private String errorLateEventsSql;

  @Autowired
  @Qualifier("batteryStatusWriter")
  private AbstractWriter<Row> batteryStatusWriter;

  @Autowired private RedisConnectionManagerFactory2 redisConnectionManagerFactory;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, BatteryStatus>> dataStream) {

    RedisConnectionManager2 redisConnectionManager =
        redisConnectionManagerFactory.createRedisConnectionManager();
    // Let's print the stream for inspection.
    dataStream
        .print("BatteryManagementSystemStatus::>> ")
        .name("battery_management_system_status")
        .uid("battery_management_system_status")
        .setParallelism(1);

    // Convert it to map.
    final String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "chg_cycle_count", // int
          "dsg_cycle_count", // int
          "cell_volt_min", // float
          "cell_volt_max", // float
          "temperature_min", // float
          "temperature_max", // float
          "battery_volt", // float
          "soc", // float
          "soh", // float
          "current", // float
          "remaining_capacity", // int
          "mosfet_temperature", // float
          "battery_soc_lower_limit", // int
          "battery_volt_lower_limit", // int
          "battery_current_lower_limit", // int
          "battery_mosfet_temperature_lower_limit", // int
          "battery_soc_upper_limit", // int
          "battery_volt_upper_limit", // int
          "battery_current_upper_limit", // int
          "battery_mosfet_temperature_upper_limit", // int
          "discharge" // float
        };

    // Data stream of rows.
    DataStream<Row> batteryStatus =
        dataStream
            .map(new BmsStatusToRowMapper(redisConnectionManager))
            .name("battery_management_system_status_to_row_mapper")
            .uid("battery_management_system_status_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.INT,
                    Types.INT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.INT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT));

    // Get a temporary view.
    tableEnv.createTemporaryView(
        "bms_status",
        batteryStatus,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `bms_status_late`"));

    lateEvents
        .print("BmsStatusLateEvents::>> ")
        .name("bms_status_late_events")
        .uid("bms_status_late_events")
        .setParallelism(1);

    this.batteryStatusWriter.save(lateEvents, true, false, false);

    // error events
    DataStream<Row> errorEvents = tableEnv.toDataStream(tableEnv.sqlQuery(errorEventsSql));
    this.batteryStatusWriter.save(errorEvents, false, true, false);

    // error late events
    tableEnv.executeSql(errorLateEventsSql);
    DataStream<Row> errorLateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `bms_status_error_late`"));

    errorLateEvents
        .print("BmsStatusErrorLateEvents::>> ")
        .name("bms_status_error_late_events")
        .uid("bms_status_error_late_events")
        .setParallelism(1);

    this.batteryStatusWriter.save(errorLateEvents, true, true, false);

    // Enrich with vehicle_id, mfg_id etc...
    return tableEnv.sqlQuery(sql);
  }

  public static class BmsStatusToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, BatteryStatus>, Row> {
    private static final Logger LOG =
        LoggerFactory.getLogger(BatteryStatusTransformer.BmsStatusToRowMapper.class);

    private static final long serialVersionUID = -3103307643038418856L;
    private final RedisConnectionManager2 redisConnectionManager;

    private transient Map<Long, PreviousRecord> previousRemainingCapacityRecordsByImei;

    public BmsStatusToRowMapper(RedisConnectionManager2 redisConnectionManager) {
      this.redisConnectionManager = redisConnectionManager;
    }

    @Override
    public void open(Configuration parameters) {
      try {
        redisConnectionManager.init();
        previousRemainingCapacityRecordsByImei = new HashMap<>();
        LOG.info("RedisConnectionManager initialized successfully for BmsStatusToRowMapper.");
      } catch (Exception e) {
        LOG.error("Error initializing RedisConnectionManager in BmsStatusToRowMapper", e);
        throw new RuntimeException(
            "Failed to initialize Redis connection in BmsStatusToRowMapper.", e);
      }
    }

    @Override
    public Row map(Tuple3<Timestamp, Metadata, BatteryStatus> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      BatteryStatus batteryStatus = value.f2;
      long imei = metadata.getImei();
      final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
      String imeiVehicleModelVariant =
          redisConnectionManager.getValue("IMEI_VEHICLE_MODEL_VARIANT_KEY::" + imei);
      float batterySocLowerLimit = 0F,
          batteryVoltLowerLimit = 0F,
          batteryCurrentLowerLimit = -100F,
          batteryMosfetTemperatureLowerLimit = -25F,
          batterySocUpperLimit = 100F,
          batteryVoltUpperLimit = 100F,
          batteryCurrentUpperLimit = 100F,
          batteryMosfetTemperatureUpperLimit = 80F;
      Float discharge = null;

      if (imeiVehicleModelVariant != null && !imeiVehicleModelVariant.isEmpty()) {

        if (imeiVehicleModelVariant.startsWith("\"") && imeiVehicleModelVariant.endsWith("\"")) {
          imeiVehicleModelVariant =
              imeiVehicleModelVariant.substring(1, imeiVehicleModelVariant.length() - 1);
        }

        String vehicleModelVariant = redisConnectionManager.getValue(imeiVehicleModelVariant);
        vehicleModelVariant = StringEscapeUtils.unescapeJava(vehicleModelVariant);

        if (vehicleModelVariant.startsWith("\"") && vehicleModelVariant.endsWith("\"")) {
          vehicleModelVariant = vehicleModelVariant.substring(1, vehicleModelVariant.length() - 1);
        }
        JsonNode vehicleData = objectMapper.readTree(vehicleModelVariant);

        // Initialize the attribute finder with JSON data
        VehicleAttributeFinder finder = new VehicleAttributeFinder(vehicleData);

        // Example usage for multiple attributes
        String partType = "BATTERY";
        String[] attributeNames = {
          "batterySocLowerLimit",
          "batteryVoltLowerLimit",
          "batteryCurrentLowerLimit",
          "batteryMosfetTemperatureLowerLimit",
          "batterySocUpperLimit",
          "batteryVoltUpperLimit",
          "batteryCurrentUpperLimit",
          "batteryMosfetTemperatureUpperLimit",
          "fullCapacity"
        };

        // Retrieve multiple specific attributes
        Map<String, String> specificAttributes =
            finder.getSpecificAttributesByPartType(partType, attributeNames);

        batterySocLowerLimit = Float.parseFloat(specificAttributes.get("batterySocLowerLimit"));
        batteryVoltLowerLimit = Float.parseFloat(specificAttributes.get("batteryVoltLowerLimit"));
        batteryCurrentLowerLimit =
            Float.parseFloat(specificAttributes.get("batteryCurrentLowerLimit"));
        batteryMosfetTemperatureLowerLimit =
            Float.parseFloat(specificAttributes.get("batteryMosfetTemperatureLowerLimit"));
        batterySocUpperLimit = Float.parseFloat(specificAttributes.get("batterySocUpperLimit"));
        batteryVoltUpperLimit = Float.parseFloat(specificAttributes.get("batteryVoltUpperLimit"));
        batteryCurrentUpperLimit =
            Float.parseFloat(specificAttributes.get("batteryCurrentUpperLimit"));
        batteryMosfetTemperatureUpperLimit =
            Float.parseFloat(specificAttributes.get("batteryMosfetTemperatureUpperLimit"));

        Float previousRemainingCapacity = null;
        PreviousRecord previousRecord = previousRemainingCapacityRecordsByImei.get(imei);

        if (previousRecord != null) {
          long timeDifference = timestamp.getTimestamp() - previousRecord.getTimestamp();
          if (timeDifference <= 90) { // Within seconds
            previousRemainingCapacity = previousRecord.getRemainingCapacity();
          }
        }

        float fullCapacity =
            Optional.ofNullable(specificAttributes.get("fullCapacity"))
                .filter(v -> !v.isBlank())
                .map(
                    v -> {
                      try {
                        return Float.parseFloat(v);
                      } catch (NumberFormatException e) {
                        return 0f;
                      }
                    })
                .orElse(0f);
        if (fullCapacity != 0f
            && previousRemainingCapacity != null
            && batteryStatus.getRemainingCapacity() != null) {
          discharge =
              ((batteryStatus.getRemainingCapacity() - previousRemainingCapacity) / fullCapacity)
                  * 100;
        }
      }

      if (batteryStatus.getRemainingCapacity() != null) {
        previousRemainingCapacityRecordsByImei.put(
            imei,
            new PreviousRecord(timestamp.getTimestamp(), batteryStatus.getRemainingCapacity()));
      }

      return Row.of(
          Instant.ofEpochSecond(timestamp.getTimestamp()),
          timestamp.getTimestamp(),
          timestamp.getIngestionTime(),
          metadata.getCorrelationId(),
          imei,
          batteryStatus.getChgCycleCount(),
          batteryStatus.getDsgCycleCount(),
          batteryStatus.getCellVoltMin(),
          batteryStatus.getCellVoltMax(),
          batteryStatus.getTemperatureMin(),
          batteryStatus.getTemperatureMax(),
          batteryStatus.getBatteryVolt(),
          batteryStatus.getSoc(),
          batteryStatus.getSoh(),
          batteryStatus.getCurrent(),
          batteryStatus.getRemainingCapacity(),
          batteryStatus.getMosfetTemperature(),
          batterySocLowerLimit,
          batteryVoltLowerLimit,
          batteryCurrentLowerLimit,
          batteryMosfetTemperatureLowerLimit,
          batterySocUpperLimit,
          batteryVoltUpperLimit,
          batteryCurrentUpperLimit,
          batteryMosfetTemperatureUpperLimit,
          discharge);
    }
  }

  @Setter
  @Getter
  @AllArgsConstructor
  private static class PreviousRecord {
    private final long timestamp;
    private final float remainingCapacity;
  }
}
