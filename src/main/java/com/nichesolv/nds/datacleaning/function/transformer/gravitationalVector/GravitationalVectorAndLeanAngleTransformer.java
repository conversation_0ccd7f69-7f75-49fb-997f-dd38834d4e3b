package com.nichesolv.nds.datacleaning.function.transformer.gravitationalVector;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVector;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager;
import com.nichesolv.nds.model.cache.Vec3;
import com.nichesolv.nds.model.cache.GrvAggData;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;
import com.nichesolv.nds.datacleaning.function.util.Corrector;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.configuration.Configuration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.time.Instant;

@Component("gravitationalVectorAndLeanAngleTransformer")
public class GravitationalVectorAndLeanAngleTransformer implements Serializable {

  private static final long serialVersionUID = -7864308770385176624L;
  private static final Logger log = LoggerFactory.getLogger(GravitationalVectorAndLeanAngleTransformer.class);

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/gravitational_vector_and_lean_angle.sql')}")
  private String sql;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/gravitational_vector_and_lean_angle_late_events.sql')}")
  private String lateEventsSql;

  @Autowired
  @Qualifier("gravitationalVectorWriterV2")
  private AbstractWriter<Row> gravitationalVectorWriter;

  @Autowired
  private RedisConnectionManagerFactory2 redisConnectionManagerFactory;


  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, GravitationalVector>> dataStream) {

    dataStream.print("[GravitationalVectorAndLeanAngleTransformer]GravitationalVectorAndLeanAngleStream::>> ").setParallelism(1);

    RedisConnectionManager2 redisConnectionManager =
            redisConnectionManagerFactory.createRedisConnectionManager();

    String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "x",
          "y",
          "z",
          "lean_angle"
        };
    DataStream<Row> gravitationalVectorAndLeanAngleStream =
        dataStream
            .map(new GravitationalVectorAndLeanAngleToRowMapper(redisConnectionManager))
            .name("gravitational_vector_and_lean_angle_to_row_mapper")
            .uid("gravitational_vector_and_lean_angle_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.INT));

    tableEnv.createTemporaryView(
        "gravitational_vector_and_lean_angle",
        gravitationalVectorAndLeanAngleStream,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `gravitational_vector_and_lean_angle_late`"));

    lateEvents
            .print("GravitationalVectorAndLeanAngleLateEvents::>> ")
            .name("gravitational_vector_and_lean_angle_late_events")
            .uid("gravitational_vector_and_lean_angle_late_events")
            .setParallelism(1);

    this.gravitationalVectorWriter.save(lateEvents, true,false, false);

    return tableEnv.sqlQuery(sql);
  }

  public static class GravitationalVectorAndLeanAngleToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, GravitationalVector>, Row> {

    private static final long serialVersionUID = -8870435692698741898L;

    private final RedisConnectionManager2 redisConnectionManager;

    public GravitationalVectorAndLeanAngleToRowMapper(RedisConnectionManager2 redisConnectionManager) {
      this.redisConnectionManager = redisConnectionManager;
    }

    @Override
    public void open(Configuration parameters) {
      try {
        redisConnectionManager.init();
        log.info("RedisConnectionManager initialized successfully for AccelerometerToRowMapper.");
      } catch (Exception e) {
        log.error("Error initializing RedisConnectionManager", e);
        throw new RuntimeException("Failed to initialize Redis connection.", e);
      }
    }

    @Override
    public Row map(Tuple3<Timestamp, Metadata, GravitationalVector> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      GravitationalVector gravitationalVector = value.f2;

      String imei = String.valueOf(metadata.getImei());

      String redisKey = "IMEI_GRV_RAW_AGG::" + imei;

      Object redisValue = redisConnectionManager.getValue(redisKey);;
      boolean isNullRedisValue = (redisValue == null || "NULL".equalsIgnoreCase(redisValue.toString()));

      String cleanedJson = null; // Initialize to null
      GrvAggData grvAggData = null; // Initialize to null

      if (!isNullRedisValue) {
        cleanedJson = preprocessJson(redisValue.toString());
        if (cleanedJson != null && !cleanedJson.equalsIgnoreCase("NULL")) {
          try {
            grvAggData = new ObjectMapper().readValue(cleanedJson, GrvAggData.class);
          } catch (JsonProcessingException e) {
            log.debug("NULL CACHE ENTRY: defaulting to null");
          }
        }
      }

      Vec3 correctedGrvVector =
              (grvAggData == null ||
                      gravitationalVector == null ||
                      grvAggData.getXstill() == null ||
                      grvAggData.getYstill() == null ||
                      grvAggData.getZstill() == null ||
                      grvAggData.getXrunning() == null ||
                      grvAggData.getYrunning() == null ||
                      grvAggData.getZrunning() == null)
                      ? null
                      : Corrector.Correct(
                      new Vec3(gravitationalVector.getX(), gravitationalVector.getY(), gravitationalVector.getZ()),
                      new Vec3(grvAggData.getXstill()[0], grvAggData.getYstill()[0], grvAggData.getZstill()[0]),
                      new Vec3(grvAggData.getXrunning()[0], grvAggData.getYrunning()[0], grvAggData.getZrunning()[0])
              );

      Float leanAngle = (isNullRedisValue || correctedGrvVector == null) ? null : (float) Math.toDegrees(Math.atan2(correctedGrvVector.getY(), correctedGrvVector.getZ()));

      return Row.of(
              Instant.ofEpochSecond(timestamp.getTimestamp()),
              timestamp.getTimestamp(),
              timestamp.getIngestionTime(),
              metadata.getCorrelationId(),
              metadata.getImei(),
              (correctedGrvVector == null)? null : correctedGrvVector.getX(),
              (correctedGrvVector == null) ? null : correctedGrvVector.getY(),
              (correctedGrvVector == null) ? null : correctedGrvVector.getZ(),
              (correctedGrvVector == null) ? null : (leanAngle == null ? null : (int) leanAngle.floatValue())
      );

    }


    // Preprocess the JSON to remove type information
    static private String preprocessJson(String redisValue) {
      try {
        // Parse the JSON string into a JsonNode
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(redisValue);

        rootNode.fields().forEachRemaining(entry -> {
          JsonNode valueNode = entry.getValue();
          if (valueNode.isArray()) {
            // If the value is an array, replace it with the second element (the actual value)
            ((com.fasterxml.jackson.databind.node.ArrayNode) valueNode).remove(0);
          }
        });

        String s = objectMapper.writeValueAsString(rootNode);
        return s;

      } catch (IOException e) {
        e.printStackTrace();
        return redisValue; // Return the original value in case of error
      }
    }
  }
}


