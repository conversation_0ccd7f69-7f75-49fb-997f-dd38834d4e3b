package com.nichesolv.nds.datacleaning.function.transformer.gravitationalVector;

import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVector;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import java.time.Instant;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("gravitationalVectorRawTransformer")
public class GravitationalVectorRawTransformer implements Serializable {

  private static final long serialVersionUID = -1495517192617577985L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:raw/gravitational_vector_raw.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:raw/gravitational_vector_raw_late_events.sql')}")
  private String lateEventsSql;

  @Autowired
  @Qualifier("gravitationalVectorWriterV2")
  private AbstractWriter<Row> gravitationalVectorWriter;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, GravitationalVector>> dataStream) {
    // GravitationalVector.
    // Let's print the GravitationalVector stream before we do any kind of imputation or anything else.
    dataStream.print("[GravitationalVectorRawTransformer]GravitationalVectorRawStream::>> ").setParallelism(1);

    // Only simple types are allowed in the table.
    // This is a necessary evil.
    String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "x",
          "y",
          "z",
          "lean_angle"
        };
    DataStream<Row> gravitationalVectorStream =
        dataStream
            .map(new GravitationalVectorRawToRowMapper())
            .name("gravitational_vector_raw_to_row_mapper")
            .uid("gravitational_vector_raw_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.INT));

    tableEnv.createTemporaryView(
        "gravitational_vector_raw",
        gravitationalVectorStream,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `gravitational_vector_raw_late`"));

    lateEvents
        .print("GravitationalVectorRawLateEvents::>> ")
        .name("gravitational_vector_raw_late_events")
        .uid("gravitational_vector_raw_late_events")
        .setParallelism(1);

    this.gravitationalVectorWriter.save(lateEvents, true,false, true);

    return tableEnv.sqlQuery(sql);
  }

  public static class GravitationalVectorRawToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, GravitationalVector>, Row> {

    private static final long serialVersionUID = 3874892839487512732L;

    @Override
    public Row map(Tuple3<Timestamp, Metadata, GravitationalVector> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      GravitationalVector gravitationalVector = value.f2;
      // Table schema.
      return Row.of(
          Instant.ofEpochSecond(timestamp.getTimestamp()),
          timestamp.getTimestamp(),
          timestamp.getIngestionTime(),
          metadata.getCorrelationId(),
          metadata.getImei(),
          gravitationalVector.getX(),
          gravitationalVector.getY(),
          gravitationalVector.getZ(),
          null);
    }
  }
}