package com.nichesolv.nds.datacleaning.function.transformer.io;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.nds.datacleaning.function.cache.VehicleAttributeFinder;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.transformer.bms.BatteryStatusTransformer;
import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInput;
import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInputIdentifier;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.time.Instant;
import java.util.List;
import java.util.Map;

import org.apache.commons.text.StringEscapeUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// This class maps analog input data to a row.
public class AnalogInputToRowMapper
    extends RichMapFunction<Tuple3<Timestamp, Metadata, List<AnalogInput>>, Row> {
  private static final Logger LOG =
          LoggerFactory.getLogger(AnalogInputToRowMapper.class);

  private static final long serialVersionUID = -3500940008657235910L;
  private final RedisConnectionManager2 redisConnectionManager;

  public AnalogInputToRowMapper(RedisConnectionManager2 redisConnectionManager) {
    this.redisConnectionManager = redisConnectionManager;
  }

  @Override
  public void open(Configuration parameters) {
    try {
      redisConnectionManager.init();
      LOG.info("RedisConnectionManager initialized successfully for AnalogInputToRowMapper.");
    } catch (Exception e) {
      LOG.error("Error initializing RedisConnectionManager in AnalogInputToRowMapper", e);
      throw new RuntimeException("Failed to initialize Redis connection in AnalogInputToRowMapper.", e);
    }
  }

  @Override
  public Row map(Tuple3<Timestamp, Metadata, List<AnalogInput>> value) throws Exception {
    Timestamp timestamp = value.f0;
    Metadata metadata = value.f1;
    List<AnalogInput> analogInputs = value.f2;

    // temperature.
    AnalogInput temp =
        analogInputs.stream()
            .filter(
                (input) -> input.getId() == AnalogInputIdentifier.TEMP.getAnalogInputIdentifier())
            .findFirst()
            .orElse(null);
    // vin
    AnalogInput vin =
        analogInputs.stream()
            .filter(
                (intput) -> intput.getId() == AnalogInputIdentifier.VIN.getAnalogInputIdentifier())
            .findFirst()
            .orElse(null);

    // vsys
    AnalogInput vsys =
        analogInputs.stream()
            .filter(
                (intput) -> intput.getId() == AnalogInputIdentifier.VSYS.getAnalogInputIdentifier())
            .findFirst()
            .orElse(null);

    // vbuck
    AnalogInput vbuck =
        analogInputs.stream()
            .filter(
                (input) -> input.getId() == AnalogInputIdentifier.VBUCK.getAnalogInputIdentifier())
            .findFirst()
            .orElse(null);

    // vusr_1
    AnalogInput vusr1 =
        analogInputs.stream()
            .filter(
                (input) -> input.getId() == AnalogInputIdentifier.VUSR_1.getAnalogInputIdentifier())
            .findFirst()
            .orElse(null);

    // vusr_2
    AnalogInput vusr2 =
        analogInputs.stream()
            .filter(
                (input) -> input.getId() == AnalogInputIdentifier.VUSR_2.getAnalogInputIdentifier())
            .findFirst()
            .orElse(null);

    // lean_angle
    AnalogInput leanAngle =
        analogInputs.stream()
            .filter(
                (input) ->
                    input.getId() == AnalogInputIdentifier.LEAN_ANG.getAnalogInputIdentifier())
            .findFirst()
            .orElse(null);


    long imei = metadata.getImei();
    final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
    String imeiVehicleModelVariant =
            redisConnectionManager.getValue("IMEI_VEHICLE_MODEL_VARIANT_KEY::" + imei);
    float aiTempLowerLimit = -25, aiVinLowerLimit= 0, aiTempUpperLimit= 80, aiVinUpperLimit= 100;
    if (imeiVehicleModelVariant != null && !imeiVehicleModelVariant.isEmpty()) {

      if (imeiVehicleModelVariant.startsWith("\"") && imeiVehicleModelVariant.endsWith("\"")) {
        imeiVehicleModelVariant =
                imeiVehicleModelVariant.substring(1, imeiVehicleModelVariant.length() - 1);
      }

      String vehicleModelVariant = redisConnectionManager.getValue(imeiVehicleModelVariant);
      vehicleModelVariant = StringEscapeUtils.unescapeJava(vehicleModelVariant);

      if (vehicleModelVariant.startsWith("\"") && vehicleModelVariant.endsWith("\"")) {
        vehicleModelVariant = vehicleModelVariant.substring(1, vehicleModelVariant.length() - 1);
      }
      JsonNode vehicleData = objectMapper.readTree(vehicleModelVariant);

      // Initialize the attribute finder with JSON data
      VehicleAttributeFinder finder = new VehicleAttributeFinder(vehicleData);

      // Example usage for multiple attributes
      String partType = "TCU";
      String[] attributeNames = {
              "aiTempLowerLimit",
              "aiVinLowerLimit",
              "aiTempUpperLimit",
              "aiVinUpperLimit"
      };

      // Retrieve multiple specific attributes
      Map<String, String> specificAttributes =
              finder.getSpecificAttributesByPartType(partType, attributeNames);

      aiTempLowerLimit = Float.parseFloat(specificAttributes.get("aiTempLowerLimit"));
      aiVinLowerLimit = Float.parseFloat(specificAttributes.get("aiVinLowerLimit"));
      aiTempUpperLimit = Float.parseFloat(specificAttributes.get("aiTempUpperLimit"));
      aiVinUpperLimit = Float.parseFloat(specificAttributes.get("aiVinUpperLimit"));
    }

    // Table schema.
    return Row.of(
        Instant.ofEpochSecond(timestamp.getTimestamp()),
        timestamp.getTimestamp(),
        timestamp.getIngestionTime(),
        metadata.getCorrelationId(),
        metadata.getImei(),
        temp != null ? temp.getValue() : null,
        vin != null ? vin.getValue() : null,
        vsys != null ? vsys.getValue() : null,
        vbuck != null ? vbuck.getValue() : null,
        vusr1 != null ? vusr1.getValue() : null,
        vusr2 != null ? vusr2.getValue() : null,
        leanAngle != null ? leanAngle.getValue() : null,
        aiTempLowerLimit,
        aiVinLowerLimit,
        aiTempUpperLimit,
        aiVinUpperLimit);
  }
}
