package com.nichesolv.nds.datacleaning.function.transformer.io;

import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInput;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import java.util.List;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/** Handles motor controller data transformations. */
@Component
public class AnalogInputTransformer implements Serializable {

  private static final long serialVersionUID = 5482480366239574941L;

  private final boolean writeDataForComparisons = false;

  @Value("${appConfig.windowWidth}")
  private int windowWidth;

  @Value("${appConfig.enableRestPersistence}")
  private Boolean enableRestPersistence;

  @Value("${appConfig.enableDbPersistence}")
  private Boolean enableDbPersistence;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/analog_input.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/analog_input_late_events.sql')}")
  private String lateEventsSql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:error/analog_input_error.sql')}")
  private String errorEventsSql;

  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:raw/analog_input_raw.sql')}")
  private String rawEventsSql;

  @Autowired
  @Qualifier("analogInputWriterV2")
  private AbstractWriter<Row> analogInputWriter;
  @Autowired
  private RedisConnectionManagerFactory2 redisConnectionManagerFactory;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, List<AnalogInput>>> dataStream) {

    RedisConnectionManager2 redisConnectionManager =
            redisConnectionManagerFactory.createRedisConnectionManager();

    // Analog input stream.
    // Let's print the analog input stream before we do any kind of imputation or anything else.
    dataStream.print("[AnalogInput]InitialAnalogInputStream::>> ").setParallelism(1);

    // Only simple types are allowed in the table.
    // This is a necessary evil.
    String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "temp",
          "vin",
          "vsys",
          "vbuck",
          "vusr_1",
          "vusr_2",
          "lean_angle",
          "ai_temp_lower_limit",
          "ai_vin_lower_limit",
          "ai_temp_upper_limit",
          "ai_vin_upper_limit"
        };
    DataStream<Row> analogInputStream =
        dataStream
            .map(new AnalogInputToRowMapper(redisConnectionManager))
            .name("analog_input_to_row_mapper")
            .uid("analog_input_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT));

    tableEnv.createTemporaryView(
        "analog_inputs",
        analogInputStream,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);
    Table lateTable = tableEnv.sqlQuery("SELECT * FROM `analog_input_late`");
    lateTable.printSchema();
    // Handles analog input late events.
    DataStream<Row> lateEvents = tableEnv.toDataStream(lateTable);

    lateEvents
        .print("AnalogInputLateEvents::>> ")
        .name("analog_input_late_events")
        .uid("analog_input_late_events")
        .setParallelism(1);

    this.analogInputWriter.save(lateEvents, true, false, false);

    DataStream<Row> errorEvents = tableEnv.toDataStream(tableEnv.sqlQuery(errorEventsSql));
    // error events
    this.analogInputWriter.save(errorEvents, false, true, false);

    DataStream<Row> rawEvents = tableEnv.toDataStream(tableEnv.sqlQuery(rawEventsSql));
    // raw events
    this.analogInputWriter.save(rawEvents, false, false, true);

    // todo: We should seriously think about do this in a single query.
    //  Group by imei, partition again by imei, call the udf, cross join unnest.
    // Or a simpler implementation would be to take a look at udfs that take pandas dataframes as
    // input.
    return tableEnv.sqlQuery(sql);
  }
}
