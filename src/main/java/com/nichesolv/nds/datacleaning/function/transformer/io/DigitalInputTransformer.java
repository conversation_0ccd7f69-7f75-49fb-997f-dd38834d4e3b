package com.nichesolv.nds.datacleaning.function.transformer.io;

import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalInput;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import java.time.Instant;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * message DigitalInput {
 *
 * <p>optional bool usr1 = 1;
 *
 * <p>optional bool usr2 = 3;
 *
 * <p>optional bool motion = 5;
 *
 * <p>optional bool tamper = 7;
 *
 * <p>optional bool mainPower = 9;
 *
 * <p>optional bool ignition = 11;
 *
 * <p>Timestamp timestamp = 12;
 *
 * <p>Metadata metadata = 13;
 *
 * <p>}
 */
@Component
public class DigitalInputTransformer implements Serializable {
  private static final long serialVersionUID = 4232622673808307853L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/digital_input.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/digital_input_late_events.sql')}")
  private String lateEventsSql;
  @Value(
          "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:raw/digital_input_raw.sql')}")
  private String rawEventsSql;

  @Autowired
  @Qualifier("digitalInputWriterV2")
  private AbstractWriter<Row> digitalInputWriter;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, DigitalInput>> dataStream) {
    // Analog input stream.
    // Let's print the analog input stream before we do any kind of imputation or anything else.
    dataStream.print("[AnalogInput]InitialDigitalInputStream::>> ").setParallelism(1);

    // Only simple types are allowed in the table.
    // This is a necessary evil.
    String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "usr1",
          "usr2",
          "motion",
          "main_power",
          "ignition",
          "tamper"
        };
    DataStream<Row> row =
        dataStream
            .map(new DigitalInputToRowMapper())
            .name("digital_input_to_row_mapper")
            .uid("digital_input_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN));

    // Digital input temporary view.
    tableEnv.createTemporaryView(
        "digital_inputs",
        row,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    // Handles digital input late events.
    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `digital_input_late`"));

    lateEvents
        .print("DigitalInputLateEvents::>> ")
        .name("digital_input_late_events")
        .uid("digital_input_late_events")
        .setParallelism(1);

    this.digitalInputWriter.save(lateEvents, true, false, false);
    DataStream<Row> rawEvents = tableEnv.toDataStream(tableEnv.sqlQuery(rawEventsSql));
    // raw events
    this.digitalInputWriter.save(rawEvents, false, false, true);
    // Get only the result
    return tableEnv.sqlQuery(sql);
  }

  public static class DigitalInputToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, DigitalInput>, Row> {

    private static final long serialVersionUID = -4847762189876047727L;

    @Override
    public Row map(Tuple3<Timestamp, Metadata, DigitalInput> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      DigitalInput digitalInput = value.f2;

      // Table schema.
      return Row.of(
          Instant.ofEpochSecond(timestamp.getTimestamp()),
          timestamp.getTimestamp(),
          timestamp.getIngestionTime(),
          metadata.getCorrelationId(),
          metadata.getImei(),
          digitalInput.isUsr1(),
          digitalInput.isUsr2(),
          digitalInput.isMotion(),
          digitalInput.isMainPower(),
          digitalInput.isIgnition(),
          digitalInput.isTamper());
    }
  }
}
