package com.nichesolv.nds.datacleaning.function.transformer.io;

import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalOutput;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import java.time.Instant;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * message DigitalOutput {
 *
 * <p>optional bool usr1 = 1;
 *
 * <p>optional bool usr2 = 3;
 *
 * <p>Timestamp timestamp = 4;
 *
 * <p>Metadata metadata = 5;
 *
 * <p>}
 */
@Component
public class DigitalOutputTransformer implements Serializable {

  private static final long serialVersionUID = -7198369405395528362L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/digital_output.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/digital_output_late_events.sql')}")
  private String lateEventsSql;

  @Autowired
  @Qualifier("digitalOutputWriterV2")
  private AbstractWriter<Row> digitalOutputWriter;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, DigitalOutput>> dataStream) {

    // Analog input stream.
    // Let's print the analog input stream before we do any kind of imputation or anything else.
    dataStream.print("[AnalogInput]InitialDigitalOutputStream::>> ").setParallelism(1);

    // Only simple types are allowed in the table.
    // This is a necessary evil.
    String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "usr1",
          "usr2"
        };
    DataStream<Row> row =
        dataStream
            .map(new DigitalOutputToRowMapper())
            .name("digital_output_to_row_mapper")
            .uid("digital_output_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.BOOLEAN,
                    Types.BOOLEAN));

    // Digital input temporary view.
    tableEnv.createTemporaryView(
        "digital_outputs",
        row,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    // Handles digital output late events.
    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `digital_output_late`"));

    lateEvents
        .print("DigitalOutputLateEvents::>> ")
        .name("digital_output_late_events")
        .uid("digital_output_late_events")
        .setParallelism(1);

    this.digitalOutputWriter.save(lateEvents, true,false, false);

    // Get only the result
    return tableEnv.sqlQuery(sql);
  }

  public static class DigitalOutputToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, DigitalOutput>, Row> {

    private static final long serialVersionUID = -3275327717164191611L;

    @Override
    public Row map(Tuple3<Timestamp, Metadata, DigitalOutput> value) throws Exception {

      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      DigitalOutput digitalOutput = value.f2;

      // Table schema.
      return Row.of(
          Instant.ofEpochSecond(timestamp.getTimestamp()),
          timestamp.getTimestamp(),
          timestamp.getIngestionTime(),
          metadata.getCorrelationId(),
          metadata.getImei(),
          digitalOutput.isUsr1(),
          digitalOutput.isUsr2());
    }
  }
}
