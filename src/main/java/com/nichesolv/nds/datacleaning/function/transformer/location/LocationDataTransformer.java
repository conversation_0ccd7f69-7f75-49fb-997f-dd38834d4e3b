package com.nichesolv.nds.datacleaning.function.transformer.location;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.nds.datacleaning.function.cache.VehicleAttributeFinder;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.location.Location;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * message Location {
 *
 * <p>optional double latitude = 1;
 *
 * <p>optional double longitude = 3;
 *
 * <p>optional float altitude = 5;
 *
 * <p>optional float speed = 7;
 *
 * <p>optional float bearing = 9;
 *
 * <p>optional sint32 pdop = 11;
 *
 * <p>optional sint32 hdop = 13;
 *
 * <p>optional sint32 vdop = 15;
 *
 * <p>optional sint32 view_sats = 17;
 *
 * <p>optional sint32 track_sats = 19;
 *
 * <p>Timestamp timestamp = 20;
 *
 * <p>Metadata metadata = 21;
 *
 * <p>}
 */

/** Handles motor controller data transformations. */
@Component
public class LocationDataTransformer implements Serializable {

  private static final long serialVersionUID = -7745582118806574097L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/location.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/location_late_events.sql')}")
  private String lateEventsSql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:error/location_error.sql')}")
  private String errorEventsSql;

  @Autowired
  @Qualifier("locationWriterV2")
  private AbstractWriter<Row> locationWriter;

  @Autowired private RedisConnectionManagerFactory2 redisConnectionManagerFactory;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, Location>> dataStream) {
    RedisConnectionManager2 redisConnectionManager =
        redisConnectionManagerFactory.createRedisConnectionManager();

    // Analog input stream.
    // Let's print the analog input stream before we do any kind of imputation or anything else.
    dataStream.print("[AnalogInput]InitialAnalogInputStream::>> ").setParallelism(1);

    // Only simple types are allowed in the table.
    // This is a necessary evil.
    String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "latitude",
          "longitude",
          "altitude",
          "speed",
          "bearing",
          "pdop",
          "hdop",
          "vdop",
          "view_sats",
          "track_sats",
          "latitude_lower_limit",
          "longitude_lower_limit",
          "altitude_lower_limit",
          "speed_lower_limit",
          "latitude_upper_limit",
          "longitude_upper_limit",
          "altitude_upper_limit",
          "speed_upper_limit",
          "gps_distance"
        };
    DataStream<Row> analogInputStream =
        dataStream
            .map(new LocationToRowMapper(redisConnectionManager))
            .name("location_to_row_mapper")
            .uid("location_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.DOUBLE,
                    Types.DOUBLE,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.DOUBLE,
                    Types.DOUBLE,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.DOUBLE,
                    Types.DOUBLE,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.DOUBLE));

    tableEnv.createTemporaryView(
        "location",
        analogInputStream,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `location_late`"));

    lateEvents
        .print("LocationLateEvents::>> ")
        .name("location_late_events")
        .uid("location_late_events")
        .setParallelism(1);

    this.locationWriter.save(lateEvents, true, false, false);

    DataStream<Row> errorEvents = tableEnv.toDataStream(tableEnv.sqlQuery(errorEventsSql));
    // error events
    this.locationWriter.save(errorEvents, false, true, false);

    // todo: We should seriously think about do this in a single query.
    //  Group by imei, partition again by imei, call the udf, cross join unnest.
    // Or a simpler implementation would be to take a look at udfs that take pandas dataframes as
    // input.
    return tableEnv.sqlQuery(sql);
  }

  public static class LocationToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, Location>, Row> {

    private static final long serialVersionUID = -4102827071002899452L;
    private static final Logger LOG = LoggerFactory.getLogger(LocationToRowMapper.class);
    private final RedisConnectionManager2 redisConnectionManager;
    private transient Map<Long, PreviousRecord> previousRecordsByImei;
    private static final double EARTH_RADIUS_METERS = 6371000;

    public LocationToRowMapper(RedisConnectionManager2 redisConnectionManager) {
      this.redisConnectionManager = redisConnectionManager;
    }

    @Override
    public void open(Configuration parameters) {
      try {
        redisConnectionManager.init();
        previousRecordsByImei = new HashMap<>();
        LOG.info("RedisConnectionManager initialized successfully for LocationToRowMapper.");
      } catch (Exception e) {
        LOG.error("Error initializing RedisConnectionManager in LocationToRowMapper", e);
        throw new RuntimeException(
            "Failed to initialize Redis connection in LocationToRowMapper.", e);
      }
    }

    @Override
    public Row map(Tuple3<Timestamp, Metadata, Location> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      Location location = value.f2;
      long imei = metadata.getImei();
      final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
      String imeiVehicleModelVariant =
          redisConnectionManager.getValue("IMEI_VEHICLE_MODEL_VARIANT_KEY::" + imei);
      double latitudeLowerLimit = 6.746803,
          longitudeLowerLimit = 68.62074,
          latitudeUpperLimit = 37.604363,
          longitudeUpperLimit = 97.41568;
      float altitudeLowerLimit = 0,
          speedLowerLimit = 0,
          altitudeUpperLimit = 10000,
          speedUpperLimit = 100;
      double gps_distance = 0;
      if (imeiVehicleModelVariant != null && !imeiVehicleModelVariant.isEmpty()) {

        if (imeiVehicleModelVariant.startsWith("\"") && imeiVehicleModelVariant.endsWith("\"")) {
          imeiVehicleModelVariant =
              imeiVehicleModelVariant.substring(1, imeiVehicleModelVariant.length() - 1);
        }

        String vehicleModelVariant = redisConnectionManager.getValue(imeiVehicleModelVariant);
        vehicleModelVariant = StringEscapeUtils.unescapeJava(vehicleModelVariant);

        if (vehicleModelVariant.startsWith("\"") && vehicleModelVariant.endsWith("\"")) {
          vehicleModelVariant = vehicleModelVariant.substring(1, vehicleModelVariant.length() - 1);
        }
        JsonNode vehicleData = objectMapper.readTree(vehicleModelVariant);

        // Initialize the attribute finder with JSON data
        VehicleAttributeFinder finder = new VehicleAttributeFinder(vehicleData);

        // Example usage for multiple attributes
        String partType = "TCU";
        String[] attributeNames = {
          "latitudeLowerLimit",
          "longitudeLowerLimit",
          "altitudeLowerLimit",
          "speedLowerLimit",
          "latitudeUpperLimit",
          "longitudeUpperLimit",
          "altitudeUpperLimit",
          "speedUpperLimit"
        };

        // Retrieve multiple specific attributes
        Map<String, String> specificAttributes =
            finder.getSpecificAttributesByPartType(partType, attributeNames);

        latitudeLowerLimit = Double.parseDouble(specificAttributes.get("latitudeLowerLimit"));
        longitudeLowerLimit = Double.parseDouble(specificAttributes.get("longitudeLowerLimit"));
        altitudeLowerLimit = Float.parseFloat(specificAttributes.get("altitudeLowerLimit"));
        speedLowerLimit = Float.parseFloat(specificAttributes.get("speedLowerLimit"));
        latitudeUpperLimit = Double.parseDouble(specificAttributes.get("latitudeUpperLimit"));
        longitudeUpperLimit = Double.parseDouble(specificAttributes.get("longitudeUpperLimit"));
        altitudeUpperLimit = Float.parseFloat(specificAttributes.get("altitudeUpperLimit"));
        speedUpperLimit = Float.parseFloat(specificAttributes.get("speedUpperLimit"));
      }
      PreviousRecord previousRecord = previousRecordsByImei.get(imei);
      if (previousRecord != null) {
        long timeDifference = timestamp.getTimestamp() - previousRecord.getTimestamp();
        if (timeDifference <= 90) { // Within seconds
          gps_distance =
              distanceInMeters(
                  previousRecord.lat,
                  previousRecord.lng,
                  location.getLatitude(),
                  location.getLongitude());
        }
      }
      previousRecordsByImei.put(
          imei,
          new PreviousRecord(
              timestamp.getTimestamp(), location.getLatitude(), location.getLongitude()));
      // Table schema.
      return Row.of(
          Instant.ofEpochSecond(timestamp.getTimestamp()),
          timestamp.getTimestamp(),
          timestamp.getIngestionTime(),
          metadata.getCorrelationId(),
          metadata.getImei(),
          location.getLatitude(),
          location.getLongitude(),
          location.getAltitude(),
          location.getSpeed(),
          location.getBearing(),
          location.getPdop(),
          location.getHdop(),
          location.getVdop(),
          location.getViewSats(),
          location.getTrackSats(),
          latitudeLowerLimit,
          longitudeLowerLimit,
          altitudeLowerLimit,
          speedLowerLimit,
          latitudeUpperLimit,
          longitudeUpperLimit,
          altitudeUpperLimit,
          speedUpperLimit,
          gps_distance);
    }

    public static double distanceInMeters(double lat1, double lon1, double lat2, double lon2) {

      double dLat = Math.toRadians(lat2 - lat1);
      double dLon = Math.toRadians(lon2 - lon1);
      lat1 = Math.toRadians(lat1);
      lat2 = Math.toRadians(lat2);

      // Haversine formula
      double a =
          Math.sin(dLat / 2) * Math.sin(dLat / 2)
              + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLon / 2) * Math.sin(dLon / 2);

      double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      return EARTH_RADIUS_METERS * c; // Distance in meters
    }
  }

  @Getter
  @AllArgsConstructor
  private static class PreviousRecord {
    private final long timestamp;
    private final double lat;
    private final double lng;
  }
}
