package com.nichesolv.nds.datacleaning.function.transformer.motorcontroller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.nds.datacleaning.function.cache.VehicleAttributeFinder;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.DriveSelection;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerData;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerMerged;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/** Handles motor controller data transformations. */

/**
 * message MotorControllerData {
 *
 * <p>optional float dcVoltage = 1;
 *
 * <p>optional sint32 motorSpeed = 3;
 *
 * <p>optional float dcCurrent = 5;
 *
 * <p>optional float motorTemperature = 7;
 *
 * <p>optional float mcsTemperature = 9;
 *
 * <p>Timestamp timestamp = 10;
 *
 * <p>Metadata metadata = 11;
 *
 * <p>}
 */
@Component
public class MotorControllerMergedTransformer implements Serializable {

  private static final long serialVersionUID = -5488315078466049792L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/motor_controller_merged.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/motor_controller_merged_late_events.sql')}")
  private String lateEventsSql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:error/motor_controller_merged_error.sql')}")
  private String errorEventsSql;

  @Autowired
  @Qualifier("motorControllerMergedWriter")
  private AbstractWriter<Row> motorControllerMergedWriter;

  @Autowired private RedisConnectionManagerFactory2 redisConnectionManagerFactory;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, MotorControllerMerged>> dataStream) {

    RedisConnectionManager2 redisConnectionManager =
        redisConnectionManagerFactory.createRedisConnectionManager();

    dataStream.print("[AnalogInput]MotorControllerMergedStream::>> ").setParallelism(1);

    // Only simple types are allowed in the table.
    // This is a necessary evil.
    String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "dc_voltage",
          "motor_speed",
          "dc_current",
          "motor_temperature",
          "mcs_temperature",
          "motor_dc_voltage_lower_limit",
          "motor_speed_lower_limit",
          "motor_dc_current_lower_limit",
          "motor_temperature_lower_limit",
          "motor_mcs_temperature_lower_limit",
          "motor_dc_voltage_upper_limit",
          "motor_speed_upper_limit",
          "motor_dc_current_upper_limit",
          "motor_temperature_upper_limit",
          "motor_mcs_temperature_upper_limit",
          "drive_selection",
          "regeneration",
          "ready_sign",
          "p_light",
          "reverse",
          "cruise",
          "vehicle_brake",
          "side_stand",
          "throttle_percentage",
          "motor_throttle_percentage_lower_limit",
          "motor_throttle_percentage_upper_limit",
          "motor_id",
          "di_motion",
          "di_ignition",
          "motor_speed_kmph",
          "motor_distance"
        };
    DataStream<Row> row =
        dataStream
            .map(new MotorControllerDataToRowMapper(redisConnectionManager))
            .name("motor_controller_merged_to_row_mapper")
            .uid("motor_controller_merged_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.FLOAT,
                    Types.INT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.INT,
                    Types.FLOAT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.FLOAT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.STRING,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.INT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.INT,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.FLOAT,
                    Types.FLOAT));

    tableEnv.createTemporaryView(
        "motor_controller_merged",
        row,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `motor_controller_merged_late`"));

    lateEvents
        .print("MotorControllerMergedLateEvents::>> ")
        .name("motor_controller_merged_late_events")
        .uid("motor_controller_merged_late_events")
        .setParallelism(1);

    this.motorControllerMergedWriter.save(lateEvents, true, false, false);

    DataStream<Row> errorEvents = tableEnv.toDataStream(tableEnv.sqlQuery(errorEventsSql));
    // error events
    this.motorControllerMergedWriter.save(errorEvents, false, true, false);

    // todo: We should seriously think about do this in a single query.
    //  Group by imei, partition again by imei, call the udf, cross join unnest.
    // Or a simpler implementation would be to take a look at udfs that take pandas dataframes as
    // input.
    return tableEnv.sqlQuery(sql);
  }

  public static class MotorControllerDataToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, MotorControllerMerged>, Row> {

    private static final Logger LOG = LoggerFactory.getLogger(MotorControllerDataToRowMapper.class);
    private static final long serialVersionUID = -3081479185168679645L;
    private final RedisConnectionManager2 redisConnectionManager;
    private transient Map<Long, PreviousRecord> previousRecordsByImei;

    public MotorControllerDataToRowMapper(RedisConnectionManager2 redisConnectionManager) {
      this.redisConnectionManager = redisConnectionManager;
    }

    @Override
    public void open(Configuration parameters) {
      try {
        redisConnectionManager.init();
        previousRecordsByImei = new HashMap<>();
        LOG.info(
            "RedisConnectionManager initialized successfully for MotorControllerDataToRowMapper.");
      } catch (Exception e) {
        LOG.error("Error initializing RedisConnectionManager in MotorControllerDataToRowMapper", e);
        throw new RuntimeException(
            "Failed to initialize Redis connection in MotorControllerDataToRowMapper.", e);
      }
    }

    @Override
    public Row map(Tuple3<Timestamp, Metadata, MotorControllerMerged> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      MotorControllerMerged motorControllerMerged = value.f2;
      long imei = metadata.getImei();
      final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
      String imeiVehicleModelVariant =
          redisConnectionManager.getValue("IMEI_VEHICLE_MODEL_VARIANT_KEY::" + imei);
      int motorDcVoltageLowerLimit = 0,
          motorDcCurrentLowerLimit = -100,
          motorTemperatureLowerLimit = -25,
          motorMcsTemperatureLowerLimit = -25,
          motorDcVoltageUpperLimit = 100,
          motorDcCurrentUpperLimit = 1000,
          motorTemperatureUpperLimit = 160,
          motorMcsTemperatureUpperLimit = 160;
      float motorSpeedLowerLimit = 0F, motorSpeedUpperLimit = 1000F;
      float motorThrottlePercentageLowerLimit = 0F, motorThrottlePercentageUpperLimit = 100F;
      float motor_speed_kmph = 0F, motor_distance = 0F;
      if (imeiVehicleModelVariant != null && !imeiVehicleModelVariant.isEmpty()) {

        if (imeiVehicleModelVariant.startsWith("\"") && imeiVehicleModelVariant.endsWith("\"")) {
          imeiVehicleModelVariant =
              imeiVehicleModelVariant.substring(1, imeiVehicleModelVariant.length() - 1);
        }

        String vehicleModelVariant = redisConnectionManager.getValue(imeiVehicleModelVariant);
        vehicleModelVariant = StringEscapeUtils.unescapeJava(vehicleModelVariant);

        if (vehicleModelVariant.startsWith("\"") && vehicleModelVariant.endsWith("\"")) {
          vehicleModelVariant = vehicleModelVariant.substring(1, vehicleModelVariant.length() - 1);
        }
        JsonNode vehicleData = objectMapper.readTree(vehicleModelVariant);

        // Initialize the attribute finder with JSON data
        VehicleAttributeFinder finder = new VehicleAttributeFinder(vehicleData);

        // Example usage for multiple attributes
        String partType = "MOTOR";
        String[] attributeNames = {
          "motorDcVoltageLowerLimit",
          "motorSpeedLowerLimit",
          "motorDcCurrentLowerLimit",
          "motorTemperatureLowerLimit",
          "motorMcsTemperatureLowerLimit",
          "motorDcVoltageUpperLimit",
          "motorSpeedUpperLimit",
          "motorDcCurrentUpperLimit",
          "motorTemperatureUpperLimit",
          "motorMcsTemperatureUpperLimit",
          "motorThrottlePercentageLowerLimit",
          "motorThrottlePercentageUpperLimit"
        };

        // Retrieve multiple specific attributes
        Map<String, String> specificAttributes =
            finder.getSpecificAttributesByPartType(partType, attributeNames);

        motorDcVoltageLowerLimit =
            Integer.parseInt(specificAttributes.get("motorDcVoltageLowerLimit"));
        motorSpeedLowerLimit = Float.parseFloat(specificAttributes.get("motorSpeedLowerLimit"));
        motorDcCurrentLowerLimit =
            Integer.parseInt(specificAttributes.get("motorDcCurrentLowerLimit"));
        motorTemperatureLowerLimit =
            Integer.parseInt(specificAttributes.get("motorTemperatureLowerLimit"));
        motorMcsTemperatureLowerLimit =
            Integer.parseInt(specificAttributes.get("motorMcsTemperatureLowerLimit"));
        motorDcVoltageUpperLimit =
            Integer.parseInt(specificAttributes.get("motorDcVoltageUpperLimit"));
        motorSpeedUpperLimit = Float.parseFloat(specificAttributes.get("motorSpeedUpperLimit"));
        motorDcCurrentUpperLimit =
            Integer.parseInt(specificAttributes.get("motorDcCurrentUpperLimit"));
        motorTemperatureUpperLimit =
            Integer.parseInt(specificAttributes.get("motorTemperatureUpperLimit"));
        motorMcsTemperatureUpperLimit =
            Integer.parseInt(specificAttributes.get("motorMcsTemperatureUpperLimit"));

        motorThrottlePercentageLowerLimit =
            Float.parseFloat(specificAttributes.get("motorThrottlePercentageLowerLimit"));
        motorThrottlePercentageUpperLimit =
            Float.parseFloat(specificAttributes.get("motorThrottlePercentageUpperLimit"));

        partType = "REAR_TYRE";
        attributeNames = new String[] {"rearTyreDiameter"};

        specificAttributes = finder.getSpecificAttributesByPartType(partType, attributeNames);

        float tyreDiameter = Float.parseFloat(specificAttributes.get("rearTyreDiameter"));
        int motorSpeed =
            Optional.ofNullable(motorControllerMerged)
                .map(MotorControllerMerged::getMotorControllerData)
                .map(MotorControllerData::getMotorSpeed)
                .orElse(0);
        motor_speed_kmph = (3.1416F * tyreDiameter * motorSpeed * 60) / 1000;

        long previousTimestamp = 0L;
        PreviousRecord previousRecord = previousRecordsByImei.get(imei);

        if (previousRecord != null) {
          long timeDifference = timestamp.getTimestamp() - previousRecord.getTimestamp();
          if (timeDifference <= 90) { // Within 10 seconds
            previousTimestamp = previousRecord.getTimestamp();
          }
          if (previousTimestamp > 0L) {
            motor_distance = timeDifference * motor_speed_kmph * 1000 / 3600;
          }
        }
      }
      previousRecordsByImei.put(imei, new PreviousRecord(timestamp.getTimestamp()));
      String imeiParts = redisConnectionManager.getValue("VEHICLE_PARTS::" + imei);
      Integer motorId = null;
      try {
        if (imeiParts != null) {
          String cleanedJson = objectMapper.readValue(imeiParts, String.class);
          Map partsMap = objectMapper.readValue(cleanedJson, Map.class);
          motorId = (Integer) partsMap.get("MOTOR");
        }
      } catch (Exception e) {
        LOG.error("Failed to parse imeiParts JSON", e);
      }

      return Row.of(
          Instant.ofEpochSecond(timestamp.getTimestamp()),
          timestamp.getTimestamp(),
          timestamp.getIngestionTime(),
          metadata.getCorrelationId(),
          metadata.getImei(),
          motorControllerMerged.getMotorControllerData().getDcVoltage(),
          motorControllerMerged.getMotorControllerData().getMotorSpeed(),
          motorControllerMerged.getMotorControllerData().getDcCurrent(),
          motorControllerMerged.getMotorControllerData().getMotorTemperature(),
          motorControllerMerged.getMotorControllerData().getMcsTemperature(),
          motorDcVoltageLowerLimit,
          motorSpeedLowerLimit,
          motorDcCurrentLowerLimit,
          motorTemperatureLowerLimit,
          motorMcsTemperatureLowerLimit,
          motorDcVoltageUpperLimit,
          motorSpeedUpperLimit,
          motorDcCurrentUpperLimit,
          motorTemperatureUpperLimit,
          motorMcsTemperatureUpperLimit,
          motorControllerMerged.getMotorControllerStatus().getStatusFeedback() != null
                  && motorControllerMerged
                          .getMotorControllerStatus()
                          .getStatusFeedback()
                          .getDriveSelection()
                      != null
              ? motorControllerMerged
                  .getMotorControllerStatus()
                  .getStatusFeedback()
                  .getDriveSelection()
                  .getName()
              : DriveSelection.NULL_DRIVE_SELECTION,
          motorControllerMerged.getMotorControllerStatus().getStatusFeedback() != null
              && motorControllerMerged
                  .getMotorControllerStatus()
                  .getStatusFeedback()
                  .isRegeneration(),
          motorControllerMerged.getMotorControllerStatus().getStatusFeedback() != null
              && motorControllerMerged.getMotorControllerStatus().getStatusFeedback().isReadySign(),
          motorControllerMerged.getMotorControllerStatus().getStatusFeedback() != null
              && motorControllerMerged.getMotorControllerStatus().getStatusFeedback().isPLight(),
          motorControllerMerged.getMotorControllerStatus().getStatusFeedback() != null
              && motorControllerMerged.getMotorControllerStatus().getStatusFeedback().isReverse(),
          motorControllerMerged.getMotorControllerStatus().getStatusFeedback() != null
              && motorControllerMerged.getMotorControllerStatus().getStatusFeedback().isCruise(),
          motorControllerMerged.getMotorControllerStatus().getStatusFeedback() != null
              && motorControllerMerged
                  .getMotorControllerStatus()
                  .getStatusFeedback()
                  .isVehicleBrake(),
          motorControllerMerged.getMotorControllerStatus().getStatusFeedback() != null
              && motorControllerMerged.getMotorControllerStatus().getStatusFeedback().isSideStand(),
          motorControllerMerged.getMotorControllerStatus().getThrottlePercentage(),
          motorThrottlePercentageLowerLimit,
          motorThrottlePercentageUpperLimit,
          motorId,
          motorControllerMerged.isDiMotion(),
          motorControllerMerged.isDiIgnition(),
          motor_speed_kmph,
          motor_distance);
    }

    @Setter
    @Getter
    @AllArgsConstructor
    private static class PreviousRecord {
      private final long timestamp;
    }
  }
}
