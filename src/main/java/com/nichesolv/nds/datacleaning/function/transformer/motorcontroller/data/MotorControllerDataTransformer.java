package com.nichesolv.nds.datacleaning.function.transformer.motorcontroller.data;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.nds.datacleaning.function.cache.VehicleAttributeFinder;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.datacleaning.function.transformer.io.AnalogInputToRowMapper;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerData;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import java.time.Instant;
import java.util.Map;

import org.apache.commons.text.StringEscapeUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/** Handles motor controller data transformations. */

/**
 * message MotorControllerData {
 *
 * <p>optional float dcVoltage = 1;
 *
 * <p>optional sint32 motorSpeed = 3;
 *
 * <p>optional float dcCurrent = 5;
 *
 * <p>optional float motorTemperature = 7;
 *
 * <p>optional float mcsTemperature = 9;
 *
 * <p>Timestamp timestamp = 10;
 *
 * <p>Metadata metadata = 11;
 *
 * <p>}
 */
@Component
public class MotorControllerDataTransformer implements Serializable {

  private static final long serialVersionUID = -5488315078466049792L;

  @Value("${appConfig.enableRestPersistence}")
  private Boolean enableRestPersistence;

  @Value("${appConfig.enableDbPersistence}")
  private Boolean enableDbPersistence;

  @Value("${appConfig.persistence.catalog.name}")
  private String catalogName;

  @Value("${appConfig.windowWidth}")
  private int windowWidth;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/motor_controller_data.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/motor_controller_data_late_events.sql')}")
  private String lateEventsSql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:error/motor_controller_data_error.sql')}")
  private String errorEventsSql;

  @Autowired
  @Qualifier("motorControllerDataWriter")
  private AbstractWriter<Row> motorControllerDataWriter;

  @Autowired
  private RedisConnectionManagerFactory2 redisConnectionManagerFactory;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, MotorControllerData>> dataStream) {

    RedisConnectionManager2 redisConnectionManager =
            redisConnectionManagerFactory.createRedisConnectionManager();

    dataStream.print("[AnalogInput]MotorControllerDataStream::>> ").setParallelism(1);

    // Only simple types are allowed in the table.
    // This is a necessary evil.
    String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "dc_voltage",
          "motor_speed",
          "dc_current",
          "motor_temperature",
          "mcs_temperature",
          "motor_dc_voltage_lower_limit",
          "motor_speed_lower_limit",
          "motor_dc_current_lower_limit",
          "motor_temperature_lower_limit",
          "motor_mcs_temperature_lower_limit",
          "motor_dc_voltage_upper_limit",
          "motor_speed_upper_limit",
          "motor_dc_current_upper_limit",
          "motor_temperature_upper_limit",
          "motor_mcs_temperature_upper_limit"
        };
    DataStream<Row> row =
        dataStream
            .map(new MotorControllerDataToRowMapper(redisConnectionManager))
            .name("motor_controller_data_to_row_mapper")
            .uid("motor_controller_data_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.FLOAT,
                    Types.INT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.INT,
                    Types.FLOAT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.FLOAT,
                    Types.INT,
                    Types.INT,
                    Types.INT));

    tableEnv.createTemporaryView(
        "motor_controller_data",
        row,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `motor_controller_data_late`"));

    lateEvents
        .print("MotorControllerDataLateEvents::>> ")
        .name("motor_controller_data_late_events")
        .uid("motor_controller_data_late_events")
        .setParallelism(1);

    this.motorControllerDataWriter.save(lateEvents, true, false, false);

    DataStream<Row> errorEvents = tableEnv.toDataStream(tableEnv.sqlQuery(errorEventsSql));
    // error events
    this.motorControllerDataWriter.save(errorEvents, false, true, false);

    // todo: We should seriously think about do this in a single query.
    //  Group by imei, partition again by imei, call the udf, cross join unnest.
    // Or a simpler implementation would be to take a look at udfs that take pandas dataframes as
    // input.
    return tableEnv.sqlQuery(sql);
  }

  public static class MotorControllerDataToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, MotorControllerData>, Row> {

    private static final Logger LOG =
            LoggerFactory.getLogger(MotorControllerDataToRowMapper.class);
    private static final long serialVersionUID = -3081479185168679645L;
    private final RedisConnectionManager2 redisConnectionManager;

    public MotorControllerDataToRowMapper(RedisConnectionManager2 redisConnectionManager) {
      this.redisConnectionManager = redisConnectionManager;
    }

    @Override
    public void open(Configuration parameters) {
      try {
        redisConnectionManager.init();
        LOG.info("RedisConnectionManager initialized successfully for MotorControllerDataToRowMapper.");
      } catch (Exception e) {
        LOG.error("Error initializing RedisConnectionManager in MotorControllerDataToRowMapper", e);
        throw new RuntimeException("Failed to initialize Redis connection in MotorControllerDataToRowMapper.", e);
      }
    }

    @Override
    public Row map(Tuple3<Timestamp, Metadata, MotorControllerData> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      MotorControllerData motorControllerData = value.f2;
      long imei = metadata.getImei();
      final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
      String imeiVehicleModelVariant =
              redisConnectionManager.getValue("IMEI_VEHICLE_MODEL_VARIANT_KEY::" + imei);
      int motorDcVoltageLowerLimit = 0, motorDcCurrentLowerLimit = -100, motorTemperatureLowerLimit = -25, motorMcsTemperatureLowerLimit=-25, motorDcVoltageUpperLimit = 100, motorDcCurrentUpperLimit=1000, motorTemperatureUpperLimit=160, motorMcsTemperatureUpperLimit=160;
      float motorSpeedLowerLimit=0F, motorSpeedUpperLimit = 1000F;
      if (imeiVehicleModelVariant != null && !imeiVehicleModelVariant.isEmpty()) {

        if (imeiVehicleModelVariant.startsWith("\"") && imeiVehicleModelVariant.endsWith("\"")) {
          imeiVehicleModelVariant =
                  imeiVehicleModelVariant.substring(1, imeiVehicleModelVariant.length() - 1);
        }

        String vehicleModelVariant = redisConnectionManager.getValue(imeiVehicleModelVariant);
        vehicleModelVariant = StringEscapeUtils.unescapeJava(vehicleModelVariant);

        if (vehicleModelVariant.startsWith("\"") && vehicleModelVariant.endsWith("\"")) {
          vehicleModelVariant = vehicleModelVariant.substring(1, vehicleModelVariant.length() - 1);
        }
        JsonNode vehicleData = objectMapper.readTree(vehicleModelVariant);

        // Initialize the attribute finder with JSON data
        VehicleAttributeFinder finder = new VehicleAttributeFinder(vehicleData);

        // Example usage for multiple attributes
        String partType = "MOTOR";
        String[] attributeNames = {
                "motorDcVoltageLowerLimit",
                "motorSpeedLowerLimit",
                "motorDcCurrentLowerLimit",
                "motorTemperatureLowerLimit",
                "motorMcsTemperatureLowerLimit",
                "motorDcVoltageUpperLimit",
                "motorSpeedUpperLimit",
                "motorDcCurrentUpperLimit",
                "motorTemperatureUpperLimit",
                "motorMcsTemperatureUpperLimit"
        };

        // Retrieve multiple specific attributes
        Map<String, String> specificAttributes =
                finder.getSpecificAttributesByPartType(partType, attributeNames);

        motorDcVoltageLowerLimit = Integer.parseInt(specificAttributes.get("motorDcVoltageLowerLimit"));
        motorSpeedLowerLimit = Float.parseFloat(specificAttributes.get("motorSpeedLowerLimit"));
        motorDcCurrentLowerLimit = Integer.parseInt(specificAttributes.get("motorDcCurrentLowerLimit"));
        motorTemperatureLowerLimit = Integer.parseInt(specificAttributes.get("motorTemperatureLowerLimit"));
        motorMcsTemperatureLowerLimit = Integer.parseInt(specificAttributes.get("motorMcsTemperatureLowerLimit"));
        motorDcVoltageUpperLimit = Integer.parseInt(specificAttributes.get("motorDcVoltageUpperLimit"));
        motorSpeedUpperLimit = Float.parseFloat(specificAttributes.get("motorSpeedUpperLimit"));
        motorDcCurrentUpperLimit = Integer.parseInt(specificAttributes.get("motorDcCurrentUpperLimit"));
        motorTemperatureUpperLimit = Integer.parseInt(specificAttributes.get("motorTemperatureUpperLimit"));
        motorMcsTemperatureUpperLimit = Integer.parseInt(specificAttributes.get("motorMcsTemperatureUpperLimit"));
      }
      return Row.of(
          Instant.ofEpochSecond(timestamp.getTimestamp()),
          timestamp.getTimestamp(),
          timestamp.getIngestionTime(),
          metadata.getCorrelationId(),
          metadata.getImei(),
          motorControllerData.getDcVoltage(),
          motorControllerData.getMotorSpeed(),
          motorControllerData.getDcCurrent(),
          motorControllerData.getMotorTemperature(),
          motorControllerData.getMcsTemperature(),
          motorDcVoltageLowerLimit,
          motorSpeedLowerLimit,
          motorDcCurrentLowerLimit,
          motorTemperatureLowerLimit,
          motorMcsTemperatureLowerLimit,
          motorDcVoltageUpperLimit,
          motorSpeedUpperLimit,
          motorDcCurrentUpperLimit,
          motorTemperatureUpperLimit,
          motorMcsTemperatureUpperLimit);
    }
  }
}
