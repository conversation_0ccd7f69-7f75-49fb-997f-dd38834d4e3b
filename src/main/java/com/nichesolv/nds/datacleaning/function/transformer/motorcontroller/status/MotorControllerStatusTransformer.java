package com.nichesolv.nds.datacleaning.function.transformer.motorcontroller.status;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.nds.datacleaning.function.cache.VehicleAttributeFinder;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.DriveSelection;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerStatus;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import java.time.Instant;
import java.util.Map;

import org.apache.commons.text.StringEscapeUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * message MotorControllerStatus {
 *
 * <p>optional string driveSelection = 1;
 *
 * <p>optional bool regeneration = 2;
 *
 * <p>optional bool readySign = 3;
 *
 * <p>optional bool pLight = 4;
 *
 * <p>optional bool reverse = 5;
 *
 * <p>optional bool cruise = 6;
 *
 * <p>optional bool vehicleBrake = 7;
 *
 * <p>optional bool sideStand = 8;
 *
 * <p>optional sint32 throttlePercentage = 9;
 *
 * <p>optional string motor_status = 10;
 *
 * <p>Timestamp timestamp = 11;
 *
 * <p>Metadata metadata = 12;
 *
 * <p>}
 */

/** Handles motor controller data transformations. */
@Component
public class MotorControllerStatusTransformer implements Serializable {

  private static final Logger LOG = LoggerFactory.getLogger(MotorControllerStatusTransformer.class);

  private static final long serialVersionUID = -4327593901013081005L;

  @Value("${appConfig.enableRestPersistence}")
  private Boolean enableRestPersistence;

  @Value("${appConfig.enableDbPersistence}")
  private Boolean enableDbPersistence;

  @Value("${appConfig.persistence.catalog.name}")
  private String catalogName;

  @Value("${appConfig.windowWidth}")
  private int windowWidth;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/motor_controller_status.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/motor_controller_status_late_events.sql')}")
  private String lateEventsSql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:error/motor_controller_status_error.sql')}")
  private String errorEventsSql;

  @Autowired
  @Qualifier("motorControllerStatusWriter")
  private AbstractWriter<Row> motorControllerStatusWriter;

  @Autowired
  private RedisConnectionManagerFactory2 redisConnectionManagerFactory;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, MotorControllerStatus>> dataStream) {

    RedisConnectionManager2 redisConnectionManager =
            redisConnectionManagerFactory.createRedisConnectionManager();

    dataStream.print("[AnalogInput]MotorControllerStatusStream::>> ").setParallelism(1);

    // Only simple types are allowed in the table.
    // This is a necessary evil.
    String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "drive_selection",
          "regeneration",
          "ready_sign",
          "p_light",
          "reverse",
          "cruise",
          "vehicle_brake",
          "side_stand",
          "throttle_percentage",
          "motor_throttle_percentage_lower_limit",
          "motor_throttle_percentage_upper_limit"
        };
    DataStream<Row> row =
        dataStream
            .map(new MotorControllerStatusToRowMapper(redisConnectionManager))
            .name("motor_controller_status_to_row_mapper")
            .uid("motor_controller_status_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.STRING,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.INT,
                    Types.FLOAT,
                    Types.FLOAT));

    tableEnv.createTemporaryView(
        "motor_controller_status",
        row,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `motor_controller_status_late`"));

    lateEvents
        .print("MotorControllerStatusLateEvents::>> ")
        .name("motor_controller_status_late_events")
        .uid("motor_controller_status_late_events")
        .setParallelism(1);

    this.motorControllerStatusWriter.save(lateEvents, true, false, false);

    DataStream<Row> errorEvents = tableEnv.toDataStream(tableEnv.sqlQuery(errorEventsSql));
    // error events
    this.motorControllerStatusWriter.save(errorEvents, false, true, false);

    // todo: We should seriously think about do this in a single query.
    //  Group by imei, partition again by imei, call the udf, cross join unnest.
    // Or a simpler implementation would be to take a look at udfs that take pandas dataframes as
    // input.
    return tableEnv.sqlQuery(sql);
  }

  public static class MotorControllerStatusToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, MotorControllerStatus>, Row> {

    private static final long serialVersionUID = 7493187469310621943L;
    private final RedisConnectionManager2 redisConnectionManager;

    public MotorControllerStatusToRowMapper(RedisConnectionManager2 redisConnectionManager) {
      this.redisConnectionManager = redisConnectionManager;
    }

    @Override
    public void open(Configuration parameters) {
      try {
        redisConnectionManager.init();
        LOG.info("RedisConnectionManager initialized successfully for MotorControllerStatusToRowMapper.");
      } catch (Exception e) {
        LOG.error("Error initializing RedisConnectionManager in MotorControllerStatusToRowMapper", e);
        throw new RuntimeException(
            "Failed to initialize Redis connection in MotorControllerStatusToRowMapper.", e);
      }
    }

    @Override
    public Row map(Tuple3<Timestamp, Metadata, MotorControllerStatus> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      MotorControllerStatus motorControllerStatus = value.f2;
      long imei = metadata.getImei();
      final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
      String imeiVehicleModelVariant =
              redisConnectionManager.getValue("IMEI_VEHICLE_MODEL_VARIANT_KEY::" + imei);
      float motorThrottlePercentageLowerLimit = 0F, motorThrottlePercentageUpperLimit=100F;
      if (imeiVehicleModelVariant != null && !imeiVehicleModelVariant.isEmpty()) {

        if (imeiVehicleModelVariant.startsWith("\"") && imeiVehicleModelVariant.endsWith("\"")) {
          imeiVehicleModelVariant =
                  imeiVehicleModelVariant.substring(1, imeiVehicleModelVariant.length() - 1);
        }

        String vehicleModelVariant = redisConnectionManager.getValue(imeiVehicleModelVariant);
        vehicleModelVariant = StringEscapeUtils.unescapeJava(vehicleModelVariant);

        if (vehicleModelVariant.startsWith("\"") && vehicleModelVariant.endsWith("\"")) {
          vehicleModelVariant = vehicleModelVariant.substring(1, vehicleModelVariant.length() - 1);
        }
        JsonNode vehicleData = objectMapper.readTree(vehicleModelVariant);

        // Initialize the attribute finder with JSON data
        VehicleAttributeFinder finder = new VehicleAttributeFinder(vehicleData);

        // Example usage for multiple attributes
        String partType = "MOTOR";
        String[] attributeNames = {
                "motorThrottlePercentageLowerLimit",
                "motorThrottlePercentageUpperLimit"
        };

        // Retrieve multiple specific attributes
        Map<String, String> specificAttributes =
                finder.getSpecificAttributesByPartType(partType, attributeNames);

        motorThrottlePercentageLowerLimit = Float.parseFloat(specificAttributes.get("motorThrottlePercentageLowerLimit"));
        motorThrottlePercentageUpperLimit = Float.parseFloat(specificAttributes.get("motorThrottlePercentageUpperLimit"));
      }

      return Row.of(
          Instant.ofEpochSecond(timestamp.getTimestamp()),
          timestamp.getTimestamp(),
          timestamp.getIngestionTime(),
          metadata.getCorrelationId(),
          metadata.getImei(),
          motorControllerStatus.getStatusFeedback() != null
                  && motorControllerStatus.getStatusFeedback().getDriveSelection() != null
              ? motorControllerStatus.getStatusFeedback().getDriveSelection().getName()
              : DriveSelection.NULL_DRIVE_SELECTION,
          motorControllerStatus.getStatusFeedback() != null
              && motorControllerStatus.getStatusFeedback().isRegeneration(),
          motorControllerStatus.getStatusFeedback() != null
              && motorControllerStatus.getStatusFeedback().isReadySign(),
          motorControllerStatus.getStatusFeedback() != null
              && motorControllerStatus.getStatusFeedback().isPLight(),
          motorControllerStatus.getStatusFeedback() != null
              && motorControllerStatus.getStatusFeedback().isReverse(),
          motorControllerStatus.getStatusFeedback() != null
              && motorControllerStatus.getStatusFeedback().isCruise(),
          motorControllerStatus.getStatusFeedback() != null
              && motorControllerStatus.getStatusFeedback().isVehicleBrake(),
          motorControllerStatus.getStatusFeedback() != null
              && motorControllerStatus.getStatusFeedback().isSideStand(),
          motorControllerStatus.getThrottlePercentage(),
          motorThrottlePercentageLowerLimit,
          motorThrottlePercentageUpperLimit);
    }
  }
}
