package com.nichesolv.nds.datacleaning.function.transformer.motorcontroller.status;

import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerStatus;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorStatus;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;

// motor faults.
@Component("motorFaultsTransformer")
public class MotorFaultsTransformer {

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/motor_faults.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/motor_faults_late_events.sql')}")
  private String lateEventsSql;

  @Autowired
  @Qualifier("motorFaultsWriter")
  private AbstractWriter<Row> motorFaultsWriter;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, MotorControllerStatus>> dataStream) {
    DataStream<Tuple3<Timestamp, Metadata, List<MotorStatus>>> stream =
        dataStream
            .process(new FetchMotorFaultsFromMotorControllerStatus())
            .name("motor_faults_from_motor_controller_status")
            .uid("motor_faults_from_motor_controller_status")
            .setParallelism(1);
    ;

    // Let's print how the stream records look like.
    stream
        .print("MotorFaultsPrint::>> ")
        .name("MotorFaultsPrint")
        .uid("MotorFaultsPrint")
        .setParallelism(1);

    // Convert it to map.
    final String[] fieldNames =
        new String[] {
          "event_time",
          "observed_timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "alarm",
          "protection",
          "alarm_name",
          "protection_name",
          "part_type"
        };

    // Let's make them into maps.
    DataStream<Row> motorFaults =
        stream
            .process(new MotorFaultsToRowMapper())
            .name("motor_faults_to_row")
            .uid("motor_faults_to_row")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.STRING,
                    Types.STRING,
                    Types.STRING));

    // Let's make it into rows.

    // Get a temporary view.
    tableEnv.createTemporaryView(
        "motor_faults",
            motorFaults,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `motor_faults_late`"));

    lateEvents
        .print("MotorFaultsLateEvents::>> ")
        .name("motor_faults_late_events")
        .uid("motor_faults_late_events")
        .setParallelism(1);
    this.motorFaultsWriter.save(lateEvents, true,false, false);

    return tableEnv.sqlQuery(sql);
  }

  // Maps faults to rows.
  public static class MotorFaultsToRowMapper
      extends ProcessFunction<
          Tuple3<Timestamp, Metadata, List<MotorStatus>>, Row> {

    @Override
    public void processElement(
        Tuple3<Timestamp, Metadata, List<MotorStatus>> value,
        ProcessFunction<Tuple3<Timestamp, Metadata, List<MotorStatus>>, Row>
                .Context
            ctx,
        Collector<Row> out)
        throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      List<MotorStatus> motorFaults = value.f2;
      int size =
          motorFaults != null ? motorFaults.size() : 0;
      for (int i = 0; i < size; i++) {
        out.collect(
            Row.of(
                Instant.ofEpochSecond(timestamp.getTimestamp()),
                timestamp.getTimestamp(),
                timestamp.getIngestionTime(),
                metadata.getCorrelationId(),
                metadata.getImei(),
                true,
                false,
                motorFaults.get(i) != null
                    ? MotorStatus.of(motorFaults.get(i).getMotorStatus()).name()
                    : MotorStatus.NO_STATUS.name(),
                "NO_STATUS",
                "MCU"));
      }
    }
  }

  private static class FetchMotorFaultsFromMotorControllerStatus
      extends ProcessFunction<
          Tuple3<Timestamp, Metadata, MotorControllerStatus>,
          Tuple3<Timestamp, Metadata, List<MotorStatus>>> {
    private static final long serialVersionUID = -8374182567028846872L;

    @Override
    public void processElement(
        Tuple3<Timestamp, Metadata, MotorControllerStatus> value,
        ProcessFunction<
                    Tuple3<Timestamp, Metadata, MotorControllerStatus>,
                    Tuple3<Timestamp, Metadata, List<MotorStatus>>>
                .Context
            ctx,
        Collector<Tuple3<Timestamp, Metadata, List<MotorStatus>>> out)
        throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      List<MotorStatus> motorFaults = value.f2.getMotorStatuses();
      out.collect(new Tuple3<>(timestamp, metadata, motorFaults));
    }
  }
}
