package com.nichesolv.nds.datacleaning.function.transformer.telemetry;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.nds.datacleaning.function.cache.VehicleAttributeFinder;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManager2;
import com.nichesolv.nds.datacleaning.function.config.RedisConnectionManagerFactory2;
import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.datacleaning.function.util.Corrector;
import com.nichesolv.nds.model.cache.GrvAggData;
import com.nichesolv.nds.model.cache.Vec3;
import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVector;
import com.nichesolv.nds.model.core.ajjas.event.gyroscope.Gyroscope;
import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInputParsed;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalInput;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalOutput;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import com.nichesolv.nds.model.domain.Telemetry;
import java.io.IOException;
import java.io.Serializable;
import java.time.Instant;
import java.util.Optional;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("imuTransformer")
public class ImuTransformer implements Serializable {

  private static final long serialVersionUID = 6503660257871091926L;
  private static final Logger log = LoggerFactory.getLogger(ImuTransformer.class);

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/imu.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:v2/imu_late_events.sql')}")
  private String lateEventsSql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:error/imu_error.sql')}")
  private String errorEventsSql;

  @Autowired
  @Qualifier("imuWriterV2")
  private AbstractWriter<Row> imuWriter;

  @Autowired private RedisConnectionManagerFactory2 redisConnectionManagerFactory;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, Telemetry>> dataStream) {

    dataStream.print("[telemetryTransformer]ImuStream::>> ").setParallelism(1);

    RedisConnectionManager2 redisConnectionManager =
        redisConnectionManagerFactory.createRedisConnectionManager();

    String[] fieldNames =
        new String[] {
          "event_time",
          "timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "ai_temp",
          "ai_vin",
          "ai_vsys",
          "ai_vbuck",
          "ai_vusr1",
          "ai_vusr2",
          "ai_lean_angle",
          "ai_temp_lower_limit",
          "ai_temp_upper_limit",
          "ai_vin_lower_limit",
          "ai_vin_upper_limit",
          "di_usr1",
          "di_usr2",
          "di_motion",
          "di_main_power",
          "di_ignition",
          "di_tamper",
          "do_usr1",
          "do_usr2",
          "accel_x",
          "accel_y",
          "accel_z",
          "gyro_x",
          "gyro_y",
          "gyro_z",
          "grv_x",
          "grv_y",
          "grv_z"
        };
    DataStream<Row> telemetryStream =
        dataStream
            .map(new TelemetryToRowMapper(redisConnectionManager))
            .name("imu_to_row_mapper")
            .uid("imu_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.BOOLEAN,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT));

    tableEnv.createTemporaryView(
        "imu",
        telemetryStream,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `imu_late`"));

    lateEvents
        .print("ImuLateEvents::>> ")
        .name("imu_late_events")
        .uid("imu_late_events")
        .setParallelism(1);

    this.imuWriter.save(lateEvents, true, false, false);

    DataStream<Row> errorEvents = tableEnv.toDataStream(tableEnv.sqlQuery(errorEventsSql));
    // error events
    this.imuWriter.save(errorEvents, false, true, false);

    return tableEnv.sqlQuery(sql);
  }

  public static class TelemetryToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, Telemetry>, Row> {

    private static final long serialVersionUID = 1626381734663657513L;

    private final RedisConnectionManager2 redisConnectionManager;

    public TelemetryToRowMapper(RedisConnectionManager2 redisConnectionManager) {
      this.redisConnectionManager = redisConnectionManager;
    }

    @Override
    public void open(Configuration parameters) {
      try {
        redisConnectionManager.init();
        log.info("RedisConnectionManager initialized successfully for TelemetryToRowMapper.");
      } catch (Exception e) {
        log.error("Error initializing RedisConnectionManager", e);
        throw new RuntimeException("Failed to initialize Redis connection.", e);
      }
    }

    @Override
    public Row map(Tuple3<Timestamp, Metadata, Telemetry> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      Telemetry telemetry = value.f2;
      Optional<AnalogInputParsed> analogInput = Optional.ofNullable(telemetry.getAnalogInput());
      Optional<DigitalInput> digitalInput = Optional.ofNullable(telemetry.getDigitalInput());
      Optional<DigitalOutput> digitalOutput = Optional.ofNullable(telemetry.getDigitalOutput());
      Optional<Accelerometer> accelerometer = Optional.ofNullable(telemetry.getAccelerometer());
      Optional<Gyroscope> gyroscope = Optional.ofNullable(telemetry.getGyroscope());
      Optional<GravitationalVector> gravitationalVector =
          Optional.ofNullable(telemetry.getGravitationalVector());

      String imei = String.valueOf(metadata.getImei());

      // Analog input limits
      final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
      String imeiVehicleModelVariant =
          redisConnectionManager.getValue("IMEI_VEHICLE_MODEL_VARIANT_KEY::" + imei);
      float aiTempLowerLimit = -25,
          aiVinLowerLimit = 0,
          aiTempUpperLimit = 80,
          aiVinUpperLimit = 100;
      if (imeiVehicleModelVariant != null && !imeiVehicleModelVariant.isEmpty()) {

        if (imeiVehicleModelVariant.startsWith("\"") && imeiVehicleModelVariant.endsWith("\"")) {
          imeiVehicleModelVariant =
              imeiVehicleModelVariant.substring(1, imeiVehicleModelVariant.length() - 1);
        }

        String vehicleModelVariant = redisConnectionManager.getValue(imeiVehicleModelVariant);
        vehicleModelVariant = StringEscapeUtils.unescapeJava(vehicleModelVariant);

        if (vehicleModelVariant.startsWith("\"") && vehicleModelVariant.endsWith("\"")) {
          vehicleModelVariant = vehicleModelVariant.substring(1, vehicleModelVariant.length() - 1);
        }
        JsonNode vehicleData = objectMapper.readTree(vehicleModelVariant);

        // Initialize the attribute finder with JSON data
        VehicleAttributeFinder finder = new VehicleAttributeFinder(vehicleData);

        // Example usage for multiple attributes
        String partType = "TCU";
        String[] attributeNames = {
          "aiTempLowerLimit", "aiVinLowerLimit", "aiTempUpperLimit", "aiVinUpperLimit"
        };

        // Retrieve multiple specific attributes
        //        Map<String, String> specificAttributes =
        //            finder.getSpecificAttributesByPartType(partType, attributeNames);
        //
        //        aiTempLowerLimit = Float.parseFloat(specificAttributes.get("aiTempLowerLimit"));
        //        aiVinLowerLimit = Float.parseFloat(specificAttributes.get("aiVinLowerLimit"));
        //        aiTempUpperLimit = Float.parseFloat(specificAttributes.get("aiTempUpperLimit"));
        //        aiVinUpperLimit = Float.parseFloat(specificAttributes.get("aiVinUpperLimit"));
      }

      // Accel, GRV, lean angle correction
      String redisKey = "IMEI_GRV_RAW_AGG::" + imei;
      Object redisValue = redisConnectionManager.getValue(redisKey);
      boolean isNullRedisValue =
          (redisValue == null || "NULL".equalsIgnoreCase(redisValue.toString()));
      String cleanedJson = null; // Initialize to null
      GrvAggData grvAggData = null; // Initialize to null
      if (!isNullRedisValue) {
        cleanedJson = preprocessJson(redisValue.toString());
        if (cleanedJson != null && !cleanedJson.equalsIgnoreCase("NULL")) {
          try {
            grvAggData = new ObjectMapper().readValue(cleanedJson, GrvAggData.class);
          } catch (JsonProcessingException e) {
            log.debug("NULL CACHE ENTRY: defaulting to null");
          }
        }
      }

      Vec3 correctedAccVector =
          (grvAggData == null || accelerometer.isEmpty())
                  || grvAggData.getXstill() == null
                  || grvAggData.getYstill() == null
                  || grvAggData.getZstill() == null
                  || grvAggData.getXrunning() == null
                  || grvAggData.getYrunning() == null
                  || grvAggData.getZrunning() == null
              ? null
              : Corrector.Correct(
                  new Vec3(
                      (float) accelerometer.get().getX(),
                      (float) accelerometer.get().getY(),
                      (float) accelerometer.get().getZ()),
                  new Vec3(
                      grvAggData.getXstill()[0],
                      grvAggData.getYstill()[0],
                      grvAggData.getZstill()[0]),
                  new Vec3(
                      grvAggData.getXrunning()[0],
                      grvAggData.getYrunning()[0],
                      grvAggData.getZrunning()[0]));

      Vec3 correctedGrvVector =
          (grvAggData == null
                  || gravitationalVector.isEmpty()
                  || grvAggData.getXstill() == null
                  || grvAggData.getYstill() == null
                  || grvAggData.getZstill() == null
                  || grvAggData.getXrunning() == null
                  || grvAggData.getYrunning() == null
                  || grvAggData.getZrunning() == null)
              ? null
              : Corrector.Correct(
                  new Vec3(
                      gravitationalVector.get().getX(),
                      gravitationalVector.get().getY(),
                      gravitationalVector.get().getZ()),
                  new Vec3(
                      grvAggData.getXstill()[0],
                      grvAggData.getYstill()[0],
                      grvAggData.getZstill()[0]),
                  new Vec3(
                      grvAggData.getXrunning()[0],
                      grvAggData.getYrunning()[0],
                      grvAggData.getZrunning()[0]));

      Float leanAngle =
          (isNullRedisValue || correctedGrvVector == null)
              ? null
              : (float)
                  Math.toDegrees(Math.atan2(correctedGrvVector.getY(), correctedGrvVector.getZ()));

      return Row.of(
          Instant.ofEpochSecond(timestamp.getTimestamp()),
          timestamp.getTimestamp(),
          timestamp.getIngestionTime(),
          metadata.getCorrelationId(),
          metadata.getImei(),
          analogInput.map(AnalogInputParsed::getTemp).orElse(null),
          analogInput.map(AnalogInputParsed::getVin).orElse(null),
          analogInput.map(AnalogInputParsed::getVsys).orElse(null),
          analogInput.map(AnalogInputParsed::getVbuck).orElse(null),
          analogInput.map(AnalogInputParsed::getVusr1).orElse(null),
          analogInput.map(AnalogInputParsed::getVusr2).orElse(null),
          (correctedGrvVector == null)
              ? null
              : (leanAngle == null ? null : (int) leanAngle.floatValue()),
          aiTempLowerLimit,
          aiTempUpperLimit,
          aiVinLowerLimit,
          aiVinUpperLimit,
          digitalInput.map(DigitalInput::isUsr1).orElse(null),
          digitalInput.map(DigitalInput::isUsr2).orElse(null),
          digitalInput.map(DigitalInput::isMotion).orElse(null),
          digitalInput.map(DigitalInput::isMainPower).orElse(null),
          digitalInput.map(DigitalInput::isIgnition).orElse(null),
          digitalInput.map(DigitalInput::isTamper).orElse(null),
          digitalOutput.map(DigitalOutput::isUsr1).orElse(null),
          digitalOutput.map(DigitalOutput::isUsr2).orElse(null),
          (correctedAccVector == null) ? null : (int) correctedAccVector.getX(),
          (correctedAccVector == null) ? null : (int) correctedAccVector.getY(),
          (correctedAccVector == null) ? null : (int) correctedAccVector.getZ(),
          gyroscope.map(Gyroscope::getX).orElse(null),
          gyroscope.map(Gyroscope::getY).orElse(null),
          gyroscope.map(Gyroscope::getZ).orElse(null),
          (correctedGrvVector == null) ? null : correctedGrvVector.getX(),
          (correctedGrvVector == null) ? null : correctedGrvVector.getY(),
          (correctedGrvVector == null) ? null : correctedGrvVector.getZ());
    }

    // Preprocess the JSON to remove type information
    private static String preprocessJson(String redisValue) {
      try {
        // Parse the JSON string into a JsonNode
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(redisValue);

        rootNode
            .fields()
            .forEachRemaining(
                entry -> {
                  JsonNode valueNode = entry.getValue();
                  if (valueNode.isArray()) {
                    // If the value is an array, replace it with the second element (the actual
                    // value)
                    ((com.fasterxml.jackson.databind.node.ArrayNode) valueNode).remove(0);
                  }
                });

        String s = objectMapper.writeValueAsString(rootNode);
        return s;

      } catch (IOException e) {
        e.printStackTrace();
        return redisValue; // Return the original value in case of error
      }
    }
  }
}
