package com.nichesolv.nds.datacleaning.function.transformer.telemetry;

import com.nichesolv.nds.datacleaning.function.sink.timescale.AbstractWriter;
import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVector;
import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInputParsed;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.io.Serializable;
import java.time.Instant;
import java.util.Optional;

import com.nichesolv.nds.model.domain.Telemetry;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("TelemetryRawTransformer")
public class TelemetryRawTransformer implements Serializable {

  private static final long serialVersionUID = -6147248547219458968L;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:raw/telemetry_raw.sql')}")
  private String sql;

  @Value(
      "#{T(com.nichesolv.nds.datacleaning.function.util.ResourceReader).readFileToString('classpath:raw/telemetry_raw_late_events.sql')}")
  private String lateEventsSql;

  @Autowired
  @Qualifier("telemetryWriterV2")
  private AbstractWriter<Row> telemetryCombinedWriter;

  public Table transform(
      StreamExecutionEnvironment env,
      StreamTableEnvironment tableEnv,
      DataStream<Tuple3<Timestamp, Metadata, Telemetry>> dataStream) {
    // Telemetry.
    // Let's print the Telemetry stream before we do any kind of imputation or anything else.
    dataStream.print("[TelemetryRawTransformer]TelemetryRawStream::>> ").setParallelism(1);

    // Only simple types are allowed in the table.
    // This is a necessary evil.
    String[] fieldNames =
        new String[] {
          "event_time",
          "timestamp",
          "ingestion_timestamp",
          "correlation_id",
          "imei",
          "accel_x",
          "accel_y",
          "accel_z",
          "grv_x",
          "grv_y",
          "grv_z",
          "ai_lean_angle"
        };
    DataStream<Row> gravitationalVectorStream =
        dataStream
            .map(new TelemetryRawToRowMapper())
            .name("telemetry_raw_to_row_mapper")
            .uid("telemetry_raw_to_row_mapper")
            .setParallelism(1)
            .returns(
                Types.ROW_NAMED(
                    fieldNames,
                    Types.INSTANT,
                    Types.INT,
                    Types.LONG,
                    Types.STRING,
                    Types.LONG,
                    Types.INT,
                    Types.INT,
                    Types.INT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.FLOAT,
                    Types.INT));

    tableEnv.createTemporaryView(
        "telemetry_raw",
        gravitationalVectorStream,
        Schema.newBuilder()
            .columnByExpression("rowtime", "CAST(event_time AS TIMESTAMP_LTZ(3))")
            .columnByExpression("proc_time", "PROCTIME()")
            .watermark("rowtime", "SOURCE_WATERMARK()")
            .build());

    tableEnv.executeSql(lateEventsSql);

    DataStream<Row> lateEvents =
        tableEnv.toDataStream(tableEnv.sqlQuery("SELECT * FROM `telemetry_raw_late`"));

    lateEvents
        .print("TelemetryRawLateEvents::>> ")
        .name("telemetry_raw_late_events")
        .uid("telemetry_raw_late_events")
        .setParallelism(1);

    this.telemetryCombinedWriter.save(lateEvents, true, false, true);

    return tableEnv.sqlQuery(sql);
  }

  public static class TelemetryRawToRowMapper
      extends RichMapFunction<Tuple3<Timestamp, Metadata, Telemetry>, Row> {

    private static final long serialVersionUID = 1203319227114264511L;

    @Override
    public Row map(Tuple3<Timestamp, Metadata, Telemetry> value) throws Exception {
      Timestamp timestamp = value.f0;
      Metadata metadata = value.f1;
      Telemetry telemetry = value.f2;
      Optional<Accelerometer> accelerometer = Optional.ofNullable(telemetry.getAccelerometer());
      Optional<GravitationalVector> gravitationalVector =
          Optional.ofNullable(telemetry.getGravitationalVector());
      Optional<AnalogInputParsed> analogInput = Optional.ofNullable(telemetry.getAnalogInput());
      // Table schema.
      return Row.of(
          Instant.ofEpochSecond(timestamp.getTimestamp()),
          timestamp.getTimestamp(),
          timestamp.getIngestionTime(),
          metadata.getCorrelationId(),
          metadata.getImei(),
          accelerometer.map(Accelerometer::getX).orElse(null),
          accelerometer.map(Accelerometer::getY).orElse(null),
          accelerometer.map(Accelerometer::getZ).orElse(null),
          gravitationalVector.map(GravitationalVector::getX).orElse(null),
          gravitationalVector.map(GravitationalVector::getY).orElse(null),
          gravitationalVector.map(GravitationalVector::getZ).orElse(null),
          analogInput.map(AnalogInputParsed::getLeanAngle).orElse(null));
    }
  }
}
