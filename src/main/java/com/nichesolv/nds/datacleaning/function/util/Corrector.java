package com.nichesolv.nds.datacleaning.function.util;
import com.nichesolv.nds.model.cache.Vec3;
public  class Corrector {

    public static Vec3 applyCorrections(Vec3 v, float[][] rPitch, float[][] rRoll, float[][] rRyaw) {
        Vec3 temp1 = matVecMultiply(rPitch, v);
        Vec3 temp2 = matVecMultiply(rRoll, temp1);
        return matVecMultiply(rRyaw, temp2);
    }

    public static Vec3 Correct(Vec3 currentGrv, Vec3 medianGrvStill, Vec3 medianGrvRunning) {

        // Calculate the correction factor based on current gravity
        float[][] rPitchRunning = {
                {(float) Math.cos(-Math.atan2(medianGrvRunning.getX(), medianGrvRunning.getZ())), 0, (float) Math.sin(-Math.atan2(medianGrvRunning.getX(), medianGrvRunning.getZ()))},
                {0, 1, 0},
                {(float) -Math.sin(-Math.atan2(medianGrvRunning.getX(), medianGrvRunning.getZ())), 0, (float) Math.cos(-Math.atan2(medianGrvRunning.getX(), medianGrvRunning.getZ()))}
        };

        // Apply pitch correction to the running gravity
        Vec3 medianTransformedPitch = matVecMultiply(rPitchRunning, medianGrvRunning);

        // Compute the roll angle from the transformed gravity
        float tRoll = (float) Math.atan2(medianTransformedPitch.getY(), medianTransformedPitch.getZ());
        float[][] rRoll = {
                {1, 0, 0},
                {0, (float) Math.cos(tRoll), -(float) Math.sin(tRoll)},
                {0, (float) Math.sin(tRoll), (float) Math.cos(tRoll)}
        };

        // Transform medianGrvStill by pitch and roll
        Vec3 transformedGrvStill = matVecMultiply(rPitchRunning, medianGrvStill);
        transformedGrvStill = matVecMultiply(rRoll, transformedGrvStill);

        // Compute the yaw angle based on the transformed medianGrvStill
        float tYaw = (float) Math.atan2(transformedGrvStill.getX(), transformedGrvStill.getY());
        float[][] rYawStill = {
                {(float) Math.cos(tYaw), (float) -Math.sin(tYaw), 0},
                {(float) Math.sin(tYaw), (float) Math.cos(tYaw), 0},
                {0, 0, 1}
        };

        // Apply the corrections based on the transformed gravity
        Vec3 correctedVec = applyCorrections(currentGrv, rPitchRunning, rRoll, rYawStill);
        float correctedLeanAngle = (float) Math.toDegrees(Math.atan2(correctedVec.getY(), correctedVec.getZ()));

        return correctedVec;
    }

    private static Vec3 matVecMultiply(float[][] matrix, Vec3 vector) {
        float x = matrix[0][0] * vector.getX() + matrix[0][1] * vector.getY() + matrix[0][2] * vector.getZ();
        float y = matrix[1][0] * vector.getX() + matrix[1][1] * vector.getY() + matrix[1][2] * vector.getZ();
        float z = matrix[2][0] * vector.getX() + matrix[2][1] * vector.getY() + matrix[2][2] * vector.getZ();
        return new Vec3(x, y, z);
    }
}