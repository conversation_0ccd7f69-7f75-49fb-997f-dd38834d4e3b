package com.nichesolv.nds.datacleaning.function.util;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;

public class PreparedStatementSetNull {
  public static void setFloatOrNull(PreparedStatement ps, int index, Float value) {
    try {
      if (value == null) {
        ps.setNull(index, Types.FLOAT);

      } else {
        ps.setFloat(index, value);
      }
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  public static void setDoubleOrNull(PreparedStatement ps, int index, Double value) {
    try {
      if (value == null) {
        ps.setNull(index, Types.DOUBLE);

      } else {
        ps.setDouble(index, value);
      }
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  public static void setIntOrNull(PreparedStatement ps, int index, Integer value) {
    try {
      if (value == null) {
        ps.setNull(index, Types.INTEGER);

      } else {
        ps.setInt(index, value);
      }
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  public static void setBoolOrNull(PreparedStatement ps, int index, Boolean value) {
    try {
      if (value == null) {
        ps.setNull(index, Types.BOOLEAN);

      } else {
        ps.setBoolean(index, value);
      }
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }
}
