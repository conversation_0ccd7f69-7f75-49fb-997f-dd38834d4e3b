package com.nichesolv.nds.model.core;

/** Implemented by all the TCUs */
public interface Tcu {
  TcuVendor getTcuVendor();

  void setTcuVendor(TcuVendor tcuVendor);

  com.nichesolv.nds.model.core.ajjas.metadata.Metadata getMetadata();

  com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp getTimestamp();

  com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer getAccelerometer();

  com.nichesolv.nds.model.core.ajjas.event.location.Location getLocation();

  com.nichesolv.nds.model.core.ajjas.event.io.IO getIo();

  com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorController getMotorController();

  com.nichesolv.nds.model.core.ajjas.event.can.bms.BatteryManagementSystem
      getBatteryManagementSystem();

  void setMetadata(com.nichesolv.nds.model.core.ajjas.metadata.Metadata metadata);

  void setTimestamp(com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp timestamp);

  void setAccelerometer(
      com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer accelerometer);

  void setLocation(com.nichesolv.nds.model.core.ajjas.event.location.Location location);

  void setIo(com.nichesolv.nds.model.core.ajjas.event.io.IO io);

  void setMotorController(
      com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorController motorController);

  void setBatteryManagementSystem(
      com.nichesolv.nds.model.core.ajjas.event.can.bms.BatteryManagementSystem
          batteryManagementSystem);
}
