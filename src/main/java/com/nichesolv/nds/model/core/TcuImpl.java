package com.nichesolv.nds.model.core;

import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.BatteryManagementSystem;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorController;
import com.nichesolv.nds.model.core.ajjas.event.io.IO;
import com.nichesolv.nds.model.core.ajjas.event.location.Location;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class TcuImpl implements Tcu {

  private TcuVendor tcuVendor;

  private Metadata metadata;

  private Timestamp timestamp;

  private Accelerometer accelerometer;

  private Location location;

  private IO io;

  private MotorController motorController;

  private BatteryManagementSystem batteryManagementSystem;
}
