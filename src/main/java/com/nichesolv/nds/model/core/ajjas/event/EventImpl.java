package com.nichesolv.nds.model.core.ajjas.event;

import java.io.Serial;
import java.io.Serializable;
import lombok.*;

/**
 * This class represents an event. Events have an opcode and length that specifies the length of the
 * MotorControllerData in the event buffer.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class EventImpl implements Event, Serializable {
  @Serial private static final long serialVersionUID = 5283952482905222574L;

  private OpCode opCode;

  private short length;
}
