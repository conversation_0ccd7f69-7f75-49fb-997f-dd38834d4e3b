package com.nichesolv.nds.model.core.ajjas.event;

import lombok.Getter;

@Getter
public enum OpCode {

  /** Pseudo event containing unix time stamp for all the events following it. */
  TS("TS"),

  /** All TCU related i/o states are available in this event. */
  IO("IO"),

  /** Raw 3 axis accelerometer values (resolution depends on configured full scale range). */
  AC("AC"),

  /** Location info reported by GNSS receiver (only available in case of valid fix). */
  LO("LO"),

  /** Raw CAN frames with CAN id. */
  CA("CA"),

  /** NO CAN */
  NO_OP("NO_OP");

  private final String opCode;

  OpCode(String opCode) {
    this.opCode = opCode;
  }

  public static OpCode of(String identifier) {
    switch (identifier) {
      case "TS":
        {
          return OpCode.TS;
        }
      case "IO":
        {
          return OpCode.IO;
        }
      case "AC":
        {
          return OpCode.AC;
        }
      case "LO":
        {
          return OpCode.LO;
        }
      case "CA":
        {
          return OpCode.CA;
        }
      default:
        {
          return OpCode.NO_OP;
        }
    }
  }
}
