package com.nichesolv.nds.model.core.ajjas.event.accelerometer;

import com.nichesolv.nds.model.core.ajjas.event.Event;
import com.nichesolv.nds.model.core.ajjas.event.accelerometer.typeinfo.AccelerometerTypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@TypeInfo(AccelerometerTypeInfoFactory.class)
public interface Accelerometer extends Event {

  void setX(int x);

  int getX();

  void setY(int y);

  int getY();

  void setZ(int z);

  int getZ();
}
