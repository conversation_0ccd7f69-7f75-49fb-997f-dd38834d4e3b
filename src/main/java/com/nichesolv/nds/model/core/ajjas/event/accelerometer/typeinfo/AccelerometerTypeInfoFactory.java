package com.nichesolv.nds.model.core.ajjas.event.accelerometer.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;

public class AccelerometerTypeInfoFactory extends TypeInfoFactory<Accelerometer> {
  @Override
  public TypeInformation<Accelerometer> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return AccelerometerTypeInfo.getTypeInfo();
  }
}
