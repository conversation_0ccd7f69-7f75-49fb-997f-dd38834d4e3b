package com.nichesolv.nds.model.core.ajjas.event.can.bms;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BatteryStatus;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.BatteryCellTemperature;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.BatteryManagementSystemTypeInfo;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltage;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/** Common interface to all battery management system. */
@TypeInfo(BatteryManagementSystemTypeInfo.class)
public interface BatteryManagementSystem {
  Metadata getMetadata();

  BatteryCellTemperature getBatteryCellTemperature();

  BatteryCellVoltage getBatteryCellVoltage();

  BatteryStatus getBatteryStatus();

  void setMetadata(Metadata metadata);

  void setBatteryCellTemperature(BatteryCellTemperature batteryCellTemperature);

  void setBatteryCellVoltage(BatteryCellVoltage batteryCellVoltage);

  void setBatteryStatus(BatteryStatus batteryStatus);

  BmsVendor getBmsVendor();

  void setBmsVendor(BmsVendor bmsVendor);
}
