package com.nichesolv.nds.model.core.ajjas.event.can.bms;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BatteryStatus;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.BatteryCellTemperature;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.BatteryManagementSystemImplTypeInfo;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltage;
import java.io.Serial;
import java.io.Serializable;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

import javax.annotation.concurrent.Immutable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(BatteryManagementSystemImplTypeInfo.class)
public class BatteryManagementSystemImpl implements Serializable, BatteryManagementSystem {

  @Serial private static final long serialVersionUID = 5239443465168390437L;

  private BmsVendor bmsVendor;

  private Metadata metadata;

  private BatteryCellTemperature batteryCellTemperature;

  private BatteryCellVoltage batteryCellVoltage;

  private BatteryStatus batteryStatus;
}
