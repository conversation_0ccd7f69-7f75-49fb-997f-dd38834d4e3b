package com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.ChemistryTypeInfo;
import lombok.Getter;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/** This class represents the chemistry of the cell. 0 : NCM 1 : LFP */
@Getter
@TypeInfo(ChemistryTypeInfo.class)
public enum Chemistry {
  NCM(0),

  LFP(1),

  NO_CHEMISTRY(Integer.MAX_VALUE);

  private final int chemistry;

  Chemistry(int chemistry) {
    this.chemistry = chemistry;
  }

  public static Chemistry of(int chemistry) {
    switch (chemistry) {
      case 0:
        return Chemistry.NCM;
      case 1:
        return Chemistry.LFP;
      default:
        return Chemistry.NO_CHEMISTRY;
    }
  }
}
