package com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.MetadataTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@TypeInfo(MetadataTypeInfo.class)
public interface Metadata {
  String getBatterySerialNumber();

  String getBmsSerialNumber();

  String getBmsFirmwareVersion();

  String getProtocolVersion();

  long getRatedCapacity();

  int getActiveCell();

  int getActiveTemp();

  int getDod();

  Chemistry getChemistry();

  void setBatterySerialNumber(String batterySerialNumber);

  void setBmsSerialNumber(String bmsSerialNumber);

  void setBmsFirmwareVersion(String bmsFirmwareVersion);

  void setProtocolVersion(String protocolVersion);

  void setRatedCapacity(long ratedCapacity);

  void setActiveCell(int activeCell);

  void setActiveTemp(int activeTemp);

  void setDod(int dod);

  void setChemistry(Chemistry chemistry);
}
