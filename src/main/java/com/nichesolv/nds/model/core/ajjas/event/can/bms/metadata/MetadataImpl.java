package com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata;

import java.io.Serial;
import java.io.Serializable;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.MetadataImplTypeInfo;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/**
 * This class represents the TronTek metadata. The information contained in this class is static per
 * vehicle.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
@TypeInfo(MetadataImplTypeInfo.class)
public class MetadataImpl implements Metadata, Serializable {

  @Serial private static final long serialVersionUID = -5626806213607111514L;

  /** Battery Serial Number(ASCII)-- 16 CHAR */
  private String batterySerialNumber;

  /** BMS serial No.(ASCII) ---16 CHAR */
  private String bmsSerialNumber;

  /** BMS Firmware Version.-- 8 CHAR (FIXED--TK020323) */
  private String bmsFirmwareVersion;

  /** Protocol Version--8 CHAR */
  private String protocolVersion;

  /** Rated Capacity (uint32_t) (mAh) */
  private long ratedCapacity;

  /** Active_Cell (uint8_t) */
  private int activeCell;

  /** Active_Temp (uint8_t) */
  private int activeTemp;

  /** DOD (uint8_t) */
  private int dod;

  /** Chemistry */
  private Chemistry chemistry;
}
