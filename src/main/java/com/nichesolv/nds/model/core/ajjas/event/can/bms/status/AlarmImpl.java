package com.nichesolv.nds.model.core.ajjas.event.can.bms.status;

import java.io.Serial;
import java.io.Serializable;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.AlarmImplTypeInfo;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(AlarmImplTypeInfo.class)
public class AlarmImpl implements Alarm, Serializable {

  @Serial private static final long serialVersionUID = -1432517681760304057L;

  private AlarmType alarmType;

  private boolean set;
}
