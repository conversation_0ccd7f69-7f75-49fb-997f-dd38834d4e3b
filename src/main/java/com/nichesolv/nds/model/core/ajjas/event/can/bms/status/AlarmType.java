package com.nichesolv.nds.model.core.ajjas.event.can.bms.status;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.AlarmTypeTypeInfo;
import lombok.Getter;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/** This class represents the various ALARMS we expect. */
@Getter
@TypeInfo(AlarmTypeTypeInfo.class)
public enum AlarmType {
  CELL_UNDER_VOLTAGE(0),

  CELL_OVER_VOLTAGE(1),

  PACK_UNDER_VOLTAGE(2),

  PACK_OVER_VOLTAGE(3),

  CELL_UNDER_TEMPERATURE(4),

  CELL_OVER_TEMPERATURE(5),

  AMBIENT_UNDER_TEMPERATURE(6),

  AMBIENT_OVER_TEMPERATURE(7),

  MOSFET_UNDER_TEMPERATURE(8),

  MOSFET_OVER_TEMPERATURE(9),

  B<PERSON><PERSON><PERSON>ER_OR_LED(10),

  CGH_OVER_CURRENT(11),

  DSG_OVER_CURRENT(12),

  NO_ALARM(-1);

  private final int alarm;

  AlarmType(int alarm) {
    this.alarm = alarm;
  }

  public static AlarmType of(int alarm) {
    switch (alarm) {
      case 0:
        return AlarmType.CELL_UNDER_VOLTAGE;
      case 1:
        return AlarmType.CELL_OVER_VOLTAGE;
      case 2:
        return AlarmType.PACK_UNDER_VOLTAGE;
      case 3:
        return AlarmType.PACK_OVER_VOLTAGE;
      case 4:
        return AlarmType.CELL_UNDER_TEMPERATURE;
      case 5:
        return AlarmType.CELL_OVER_TEMPERATURE;
      case 6:
        return AlarmType.AMBIENT_UNDER_TEMPERATURE;
      case 7:
        return AlarmType.AMBIENT_OVER_TEMPERATURE;
      case 8:
        return AlarmType.MOSFET_UNDER_TEMPERATURE;
      case 9:
        return AlarmType.MOSFET_OVER_TEMPERATURE;
      case 10:
        return AlarmType.BUZZER_OR_LED;
      case 11:
        return AlarmType.CGH_OVER_CURRENT;
      case 12:
        return AlarmType.DSG_OVER_CURRENT;
      default:
        return AlarmType.NO_ALARM;
    }
  }
}
