package com.nichesolv.nds.model.core.ajjas.event.can.bms.status;

import java.io.Serial;
import java.io.Serializable;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.BalancingStatusImplTypeInfo;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(BalancingStatusImplTypeInfo.class)
public class BalancingStatusImpl implements BalancingStatus, Serializable {
  @Serial private static final long serialVersionUID = -3183996892866838920L;

  private int cellId;

  private boolean status;
}
