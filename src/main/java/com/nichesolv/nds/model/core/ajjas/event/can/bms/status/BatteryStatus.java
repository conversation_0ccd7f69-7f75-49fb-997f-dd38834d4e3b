package com.nichesolv.nds.model.core.ajjas.event.can.bms.status;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.BatteryStatusTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@TypeInfo(BatteryStatusTypeInfo.class)
public interface BatteryStatus {
  Integer getChgCycleCount();

  Integer getDsgCycleCount();

  java.util.List<Alarm> getAlarms();

  java.util.List<Protection> getProtections();

  Float getCellVoltMin();

  Float getCellVoltMax();

  Float getTemperatureMin();

  Float getTemperatureMax();

  Float getBatteryVolt();

  Float getSoc();

  Float getSoh();

  Float getCurrent();

  java.util.List<BalancingStatus> getBalancingStatuses();

  void setChgCycleCount(Integer chgCycleCount);

  void setDsgCycleCount(Integer dsgCycleCount);

  void setAlarms(java.util.List<Alarm> alarms);

  void setProtections(java.util.List<Protection> protections);

  void setCellVoltMin(Float cellVoltMin);

  void setCellVoltMax(Float cellVoltMax);

  void setTemperatureMin(Float temperatureMin);

  void setTemperatureMax(Float temperatureMax);

  void setBatteryVolt(Float batteryVolt);

  void setSoc(Float soc);

  void setSoh(Float soh);

  void setCurrent(Float current);

  void setBalancingStatuses(java.util.List<BalancingStatus> balancingStatuses);

  Integer getRemainingCapacity();

  void setRemainingCapacity(Integer remainingCapacity);

  Float getMosfetTemperature();

  void setMosfetTemperature(Float mosfetTemperature);
}
