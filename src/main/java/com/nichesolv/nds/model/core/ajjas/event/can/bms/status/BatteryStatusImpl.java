package com.nichesolv.nds.model.core.ajjas.event.can.bms.status;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.BatteryStatusImplTypeInfo;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(BatteryStatusImplTypeInfo.class)
public class BatteryStatusImpl implements BatteryStatus, Serializable {

  @Serial private static final long serialVersionUID = 8603442321438071717L;

  // CHG CYCLE COUNT
  private Integer chgCycleCount;

  // DSG CYCLE COUNT
  private Integer dsgCycleCount;

  // Alarms
  @Singular private List<Alarm> alarms;

  // Protections
  @Singular private List<Protection> protections;

  // Cell Volt Min (V)
  private Float cellVoltMin;

  // Cell Volt Maximum (V)
  private Float cellVoltMax;

  // Temp minimum (in celsius)	.
  private Float temperatureMin;

  // Temp maximum (in celsius)
  private Float temperatureMax;

  /** Batt Volt (V) */
  private Float batteryVolt;

  /** SOC  */
  private Float soc;

  /** SOH */
  private Float soh;

  // Current (float) (A).
  private Float current;

  // Balancing Status(uint32_t) Need to do bit wise operation.
  @Singular private List<BalancingStatus> balancingStatuses;

  private Integer remainingCapacity;

  // MOSFET temperature (in celsius)
  private Float mosfetTemperature;
}
