package com.nichesolv.nds.model.core.ajjas.event.can.bms.status;

import java.io.Serializable;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.ProtectionImplTypeInfo;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(ProtectionImplTypeInfo.class)
public class ProtectionImpl implements Protection, Serializable {

  private static final long serialVersionUID = -937708074885068397L;

  private ProtectionType protectionType;

  private boolean set;
}
