package com.nichesolv.nds.model.core.ajjas.event.can.bms.status;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.ProtectionTypeTypeInfo;
import lombok.Getter;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/** Represents the battery protection fields. */
@Getter
@TypeInfo(ProtectionTypeTypeInfo.class)
public enum ProtectionType {
  CELL_UNDER_VOLTAGE(0),

  CELL_OVER_VOLTAGE(1),

  PACK_UNDER_VOLTAGE(2),

  PACK_OVER_VOLTAGE(3),

  CELL_UNDER_TEMPERATURE(4),

  CELL_OVER_TEMPERATURE(5),

  AMBIENT_UNDER_TEMPERATURE(6),

  AMBIENT_OVER_TEMPERATURE(7),

  MOSFET_UNDER_TEMPERATURE(8),

  MOSFET_OVER_TEMPERATURE(9),

  THERMAL_RUNWAY(10),

  <PERSON><PERSON><PERSON><PERSON><PERSON>_OR_LED(11),

  CG<PERSON>_OVER_CURRENT(12),

  DSG_OVER_CURRENT(13),

  SHORT_CIRCUIT(14),

  SINGLE_OVERVOLTAGE_PROTECTION(15),

  SINGLE_UNDERVOLTAGE_PROTECTION(16),

  PACK_OVERVOLTAGE_PROTECTION(17),

  PACK_UNDERVOLTAGE_PROTECTION(18),

  CHARGE_OVER_TEMPERATURE_PROTECTION(19),

  CHARGE_LOW_TEMPERATURE_PROTECTION(20),

  DISCHARGE_OVER_TEMPERATURE_PROTECTION(21),

  DISCHARGE_LOW_TEMPERATURE_PROTECTION(22),

  CHARGE_OVERCURRENT_PROTECTION(23),

  DISCHARGE_OVERCURRENT_PROTECTION(24),

  SHORT_CIRCUIT_PROTECTION(25),

  FRONT_DETECTION_IC_ERROR(26),

  SOFTWARE_LOCK_MOS(27),

  CHARGE_MOS_OFF(28),

  DISCHARGE_MOS_OFF(29),

  NO_PROTECTION(-1);

  private final int protection;

  ProtectionType(int protection) {
    this.protection = protection;
  }

  public static ProtectionType of(int protection) {
    switch (protection) {
      case 0:
        return ProtectionType.CELL_UNDER_VOLTAGE;
      case 1:
        return ProtectionType.CELL_OVER_VOLTAGE;
      case 2:
        return ProtectionType.PACK_UNDER_VOLTAGE;
      case 3:
        return ProtectionType.PACK_OVER_VOLTAGE;
      case 4:
        return ProtectionType.CELL_UNDER_TEMPERATURE;
      case 5:
        return ProtectionType.CELL_OVER_TEMPERATURE;
      case 6:
        return ProtectionType.AMBIENT_UNDER_TEMPERATURE;
      case 7:
        return ProtectionType.AMBIENT_OVER_TEMPERATURE;
      case 8:
        return ProtectionType.MOSFET_UNDER_TEMPERATURE;
      case 9:
        return ProtectionType.MOSFET_OVER_TEMPERATURE;
      case 10:
        return ProtectionType.BUZZER_OR_LED;
      case 11:
        return ProtectionType.CGH_OVER_CURRENT;
      case 12:
        return ProtectionType.DSG_OVER_CURRENT;
      case 15:
        return ProtectionType.SINGLE_OVERVOLTAGE_PROTECTION;
      case 16:
        return ProtectionType.SINGLE_UNDERVOLTAGE_PROTECTION;
      case 17:
        return ProtectionType.PACK_OVERVOLTAGE_PROTECTION;
      case 18:
        return ProtectionType.PACK_UNDERVOLTAGE_PROTECTION;
      case 19:
        return ProtectionType.CHARGE_OVER_TEMPERATURE_PROTECTION;
      case 20:
        return ProtectionType.CHARGE_LOW_TEMPERATURE_PROTECTION;
      case 21:
        return ProtectionType.DISCHARGE_OVER_TEMPERATURE_PROTECTION;
      case 22:
        return ProtectionType.DISCHARGE_LOW_TEMPERATURE_PROTECTION;
      case 23:
        return ProtectionType.CHARGE_OVERCURRENT_PROTECTION;
      case 24:
        return ProtectionType.DISCHARGE_OVERCURRENT_PROTECTION;
      case 25:
        return ProtectionType.SHORT_CIRCUIT_PROTECTION;
      case 26:
        return ProtectionType.FRONT_DETECTION_IC_ERROR;
      case 27:
        return ProtectionType.SOFTWARE_LOCK_MOS;
      case 28:
        return ProtectionType.CHARGE_MOS_OFF;
      case 29:
        return ProtectionType.DISCHARGE_MOS_OFF;
      default:
        return ProtectionType.NO_PROTECTION;
    }
  }
}
