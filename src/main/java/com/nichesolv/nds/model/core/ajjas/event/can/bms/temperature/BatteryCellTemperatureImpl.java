package com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.BatteryCellTemperatureImplTypeInfo;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/** This class represents the battery cell temperature. */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(BatteryCellTemperatureImplTypeInfo.class)
public class BatteryCellTemperatureImpl implements BatteryCellTemperature, Serializable {

  @Serial private static final long serialVersionUID = -1607176485062886454L;

  /** List of CellTemperatures for this stack. */
  @Singular private List<CellTemperature> cellTemperatures;
}
