package com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.CellTemperatureImplTypeInfo;
import java.io.Serial;
import java.io.Serializable;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/** This class represents a cell temperature. */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(CellTemperatureImplTypeInfo.class)
public class CellTemperatureImpl implements CellTemperature, Serializable {

  @Serial private static final long serialVersionUID = 3714540985835176455L;

  /** Cell identifier */
  private int id;

  /** Cell or stack temperature value. */
  private Float temperature;
}
