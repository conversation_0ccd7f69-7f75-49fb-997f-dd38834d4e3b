package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.google.common.collect.ImmutableMap;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.AlarmImpl;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.AlarmType;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class AlarmImplTypeInfo extends TypeInfoFactory<AlarmImpl> {
  @Override
  public TypeInformation<AlarmImpl> createTypeInfo(Type t, Map<String, TypeInformation<?>> g) {
    return Types.POJO(
        AlarmImpl.class,
        ImmutableMap.of("alarmType", Types.ENUM(AlarmType.class), "set", Types.BOOLEAN));
  }
}
