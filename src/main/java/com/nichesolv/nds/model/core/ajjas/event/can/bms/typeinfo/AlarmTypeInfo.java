package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.Alarm;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class AlarmTypeInfo extends TypeInfoFactory<Alarm> {
  @Override
  public TypeInformation<Alarm> createTypeInfo(Type t, Map<String, TypeInformation<?>> g) {
    return Types.POJO(Alarm.class, new HashMap<>());
  }
}
