package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.AlarmType;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class AlarmTypeTypeInfo extends TypeInfoFactory<AlarmType> {
  @Override
  public TypeInformation<AlarmType> createTypeInfo(Type t, Map<String, TypeInformation<?>> g) {
    return Types.ENUM(AlarmType.class);
  }
}
