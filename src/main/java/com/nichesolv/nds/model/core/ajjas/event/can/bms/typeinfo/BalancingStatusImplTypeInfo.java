package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BalancingStatusImpl;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.shaded.guava30.com.google.common.collect.ImmutableMap;

public class BalancingStatusImplTypeInfo extends TypeInfoFactory<BalancingStatusImpl> {

  @Override
  public TypeInformation<BalancingStatusImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return Types.POJO(
        BalancingStatusImpl.class, ImmutableMap.of("cellId", Types.INT, "status", Types.BOOLEAN));
  }
}
