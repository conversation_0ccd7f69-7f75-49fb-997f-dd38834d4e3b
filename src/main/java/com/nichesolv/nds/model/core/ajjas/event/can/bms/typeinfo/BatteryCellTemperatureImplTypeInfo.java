package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.BatteryCellTemperatureImpl;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.CellTemperature;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.shaded.guava30.com.google.common.collect.ImmutableMap;

public class BatteryCellTemperatureImplTypeInfo
    extends TypeInfoFactory<BatteryCellTemperatureImpl> {

  @Override
  public TypeInformation<BatteryCellTemperatureImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return Types.POJO(
        BatteryCellTemperatureImpl.class,
        ImmutableMap.of(
            "cellTemperatures", Types.LIST(Types.POJO(CellTemperature.class, new HashMap<>()))));
  }
}
