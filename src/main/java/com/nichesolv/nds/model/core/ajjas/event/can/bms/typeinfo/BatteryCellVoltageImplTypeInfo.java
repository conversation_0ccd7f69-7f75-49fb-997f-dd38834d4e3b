package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltageImpl;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.CellVoltage;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.shaded.guava30.com.google.common.collect.ImmutableMap;

public class BatteryCellVoltageImplTypeInfo extends TypeInfoFactory<BatteryCellVoltageImpl> {
  @Override
  public TypeInformation<BatteryCellVoltageImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return Types.POJO(
        BatteryCellVoltageImpl.class,
        ImmutableMap.of(
            "cellVoltages", Types.LIST(Types.POJO(CellVoltage.class, new HashMap<>()))));
  }
}
