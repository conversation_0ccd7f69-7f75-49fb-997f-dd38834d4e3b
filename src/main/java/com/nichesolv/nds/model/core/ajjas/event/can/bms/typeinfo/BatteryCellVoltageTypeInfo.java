package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltage;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class BatteryCellVoltageTypeInfo extends TypeInfoFactory<BatteryCellVoltage> {
  @Override
  public TypeInformation<BatteryCellVoltage> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return Types.POJO(BatteryCellVoltage.class, new HashMap<>());
  }
}
