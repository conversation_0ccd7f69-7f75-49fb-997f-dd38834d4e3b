package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.google.common.collect.ImmutableMap;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.BatteryManagementSystemImpl;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.BmsVendor;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BatteryStatus;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.BatteryCellTemperature;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.BatteryCellVoltage;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class BatteryManagementSystemImplTypeInfo
    extends TypeInfoFactory<BatteryManagementSystemImpl> {
  @Override
  public TypeInformation<BatteryManagementSystemImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(
        BatteryManagementSystemImpl.class,
        ImmutableMap.of(
            "bmsVendor",
            Types.ENUM(BmsVendor.class),
            "metadata",
            Types.POJO(Metadata.class, new HashMap<>()),
            "batteryCellTemperature",
            Types.POJO(BatteryCellTemperature.class, new HashMap<>()),
            "batteryCellVoltage",
            Types.POJO(BatteryCellVoltage.class, new HashMap<>()),
            "batteryStatus",
            Types.POJO(BatteryStatus.class, new HashMap<>())));
  }
}
