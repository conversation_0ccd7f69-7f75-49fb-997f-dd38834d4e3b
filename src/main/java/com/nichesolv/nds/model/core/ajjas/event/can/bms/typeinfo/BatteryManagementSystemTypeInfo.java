package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.BatteryManagementSystem;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class BatteryManagementSystemTypeInfo extends TypeInfoFactory<BatteryManagementSystem> {
  @Override
  public TypeInformation<BatteryManagementSystem> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(BatteryManagementSystem.class, new HashMap<>());
  }
}
