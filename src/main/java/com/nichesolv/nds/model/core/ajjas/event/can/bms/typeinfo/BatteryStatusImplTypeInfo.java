package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.Alarm;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BalancingStatus;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BatteryStatusImpl;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.Protection;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class BatteryStatusImplTypeInfo extends TypeInfoFactory<BatteryStatusImpl> {
  @Override
  public TypeInformation<BatteryStatusImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    HashMap<String, TypeInformation<?>> hash = new HashMap<>();
    hash.put("chgCycleCount", Types.INT);
    hash.put("dsgCycleCount", Types.INT);
    hash.put("alarms", Types.LIST(Types.POJO(Alarm.class, new HashMap<>())));
    hash.put("protections", Types.LIST(Types.POJO(Protection.class, new HashMap<>())));
    hash.put("cellVoltMin", Types.FLOAT);
    hash.put("cellVoltMax", Types.FLOAT);
    hash.put("temperatureMin", Types.FLOAT);
    hash.put("temperatureMax", Types.FLOAT);
    hash.put("batteryVolt", Types.FLOAT);
    hash.put("soc", Types.FLOAT);
    hash.put("soh", Types.FLOAT);
    hash.put("current", Types.FLOAT);
    hash.put("balancingStatuses", Types.LIST(Types.POJO(BalancingStatus.class, new HashMap<>())));
    hash.put("remainingCapacity", Types.INT);
    hash.put("mosfetTemperature", Types.FLOAT);

    return Types.POJO(BatteryStatusImpl.class, hash);
  }
}
