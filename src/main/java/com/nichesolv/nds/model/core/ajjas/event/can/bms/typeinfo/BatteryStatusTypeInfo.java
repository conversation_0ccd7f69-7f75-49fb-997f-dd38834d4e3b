package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.BatteryStatus;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class BatteryStatusTypeInfo extends TypeInfoFactory<BatteryStatus> {
  @Override
  public TypeInformation<BatteryStatus> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(BatteryStatus.class, new HashMap<>());
  }
}
