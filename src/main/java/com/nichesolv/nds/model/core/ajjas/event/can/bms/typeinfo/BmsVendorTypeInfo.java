package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.BmsVendor;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class BmsVendorTypeInfo extends TypeInfoFactory<BmsVendor> {
  @Override
  public TypeInformation<BmsVendor> createTypeInfo(Type t, Map<String, TypeInformation<?>> g) {
    return Types.ENUM(BmsVendor.class);
  }
}
