package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.google.common.collect.ImmutableMap;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.temperature.CellTemperatureImpl;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class CellTemperatureImplTypeInfo extends TypeInfoFactory<CellTemperatureImpl> {
  @Override
  public TypeInformation<CellTemperatureImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(
        CellTemperatureImpl.class, ImmutableMap.of("id", Types.INT, "temperature", Types.FLOAT));
  }
}
