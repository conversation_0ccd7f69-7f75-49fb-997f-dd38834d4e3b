package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.google.common.collect.ImmutableMap;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.CellVoltageImpl;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class CellVoltageImplTypeInfo extends TypeInfoFactory<CellVoltageImpl> {
  @Override
  public TypeInformation<CellVoltageImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(CellVoltageImpl.class, ImmutableMap.of("id", Types.INT, "volts", Types.FLOAT));
  }
}
