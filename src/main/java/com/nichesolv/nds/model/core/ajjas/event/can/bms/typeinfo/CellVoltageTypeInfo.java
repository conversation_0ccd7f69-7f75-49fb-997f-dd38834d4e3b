package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage.CellVoltage;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class CellVoltageTypeInfo extends TypeInfoFactory<CellVoltage> {
  @Override
  public TypeInformation<CellVoltage> createTypeInfo(Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(CellVoltage.class, new HashMap<>());
  }
}
