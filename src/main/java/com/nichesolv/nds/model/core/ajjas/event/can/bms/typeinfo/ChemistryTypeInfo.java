package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Chemistry;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class ChemistryTypeInfo extends TypeInfoFactory<Chemistry> {
  @Override
  public TypeInformation<Chemistry> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return Types.ENUM(Chemistry.class);
  }
}
