package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.google.common.collect.ImmutableMap;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Chemistry;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.MetadataImpl;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class MetadataImplTypeInfo extends TypeInfoFactory<MetadataImpl> {
  @Override
  public TypeInformation<MetadataImpl> createTypeInfo(Type t, Map<String, TypeInformation<?>> map) {

    return Types.POJO(
        MetadataImpl.class,
        ImmutableMap.of(
            "batterySerialNumber",
            Types.STRING,
            "bmsSerialNumber",
            Types.STRING,
            "bmsFirmwareVersion",
            Types.STRING,
            "protocolVersion",
            Types.STRING,
            "ratedCapacity",
            Types.LONG,
            "activeCell",
            Types.INT,
            "activeTemp",
            Types.INT,
            "dod",
            Types.INT,
            "chemistry",
            Types.ENUM(Chemistry.class)));
  }
}
