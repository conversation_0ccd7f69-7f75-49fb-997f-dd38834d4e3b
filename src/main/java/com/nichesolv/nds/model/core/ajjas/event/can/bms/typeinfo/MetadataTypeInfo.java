package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.metadata.Metadata;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class MetadataTypeInfo extends TypeInfoFactory<Metadata> {
  @Override
  public TypeInformation<Metadata> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return Types.POJO(Metadata.class, new HashMap<>());
  }
}
