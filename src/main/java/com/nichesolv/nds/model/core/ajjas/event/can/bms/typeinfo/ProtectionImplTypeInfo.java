package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.google.common.collect.ImmutableMap;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.ProtectionImpl;
import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.ProtectionType;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class ProtectionImplTypeInfo extends TypeInfoFactory<ProtectionImpl> {
  @Override
  public TypeInformation<ProtectionImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(
        ProtectionImpl.class,
        ImmutableMap.of("set", Types.BOOLEAN, "protectionType", Types.ENUM(ProtectionType.class)));
  }
}
