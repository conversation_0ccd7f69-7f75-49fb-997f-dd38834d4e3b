package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.Protection;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class ProtectionTypeInfo extends TypeInfoFactory<Protection> {
  @Override
  public TypeInformation<Protection> createTypeInfo(Type t, Map<String, TypeInformation<?>> g) {
    return Types.POJO(Protection.class, new HashMap<>());
  }
}
