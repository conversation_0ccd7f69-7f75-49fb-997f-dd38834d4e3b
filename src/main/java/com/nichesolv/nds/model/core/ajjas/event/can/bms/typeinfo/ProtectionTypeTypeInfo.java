package com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.status.ProtectionType;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class ProtectionTypeTypeInfo extends TypeInfoFactory<ProtectionType> {
  @Override
  public TypeInformation<ProtectionType> createTypeInfo(Type t, Map<String, TypeInformation<?>> g) {
    return Types.ENUM(ProtectionType.class);
  }
}
