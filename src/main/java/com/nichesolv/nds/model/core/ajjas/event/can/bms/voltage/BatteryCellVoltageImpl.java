package com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.BatteryCellVoltageImplTypeInfo;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/**
 * This class represents the battery cell voltages. Might optionally have information about the
 * stack as well.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(BatteryCellVoltageImplTypeInfo.class)
public class BatteryCellVoltageImpl implements BatteryCellVoltage, Serializable {

  @Serial private static final long serialVersionUID = 3990226216354895707L;

  /** List of cell voltage objects. */
  @Singular private List<CellVoltage> cellVoltages;
}
