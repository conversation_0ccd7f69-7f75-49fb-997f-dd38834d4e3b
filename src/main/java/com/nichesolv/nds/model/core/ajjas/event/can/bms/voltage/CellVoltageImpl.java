package com.nichesolv.nds.model.core.ajjas.event.can.bms.voltage;

import com.nichesolv.nds.model.core.ajjas.event.can.bms.typeinfo.CellVoltageImplTypeInfo;
import java.io.Serial;
import java.io.Serializable;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/**
 * This class represents a cell voltage with identifier and the actual voltage value.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
@TypeInfo(CellVoltageImplTypeInfo.class)
public class CellVoltageImpl implements CellVoltage, Serializable {
  @Serial private static final long serialVersionUID = 9200795800974812580L;

  /**
   * Identification for the cell. see:
   * https://docs.google.com/spreadsheets/d/1kOEDP64CFmsZGsqF8ALMY6AycBTE8miVRC1di3F6hEY/edit#gid=105162269&range=B13:K19
   */
  private Integer id;

  /** Battery voltage. */
  private Float volts;
}
