package com.nichesolv.nds.model.core.ajjas.event.can.mc;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo.ActiveStatusTypeInfo;
import lombok.Getter;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Getter
@TypeInfo(ActiveStatusTypeInfo.class)
public enum ActiveStatus {
  ON(1),

  OFF(0),

  NO_STATUS(2),

  NOT_SUPPORTED(3);

  private final int activeStatus;

  ActiveStatus(int activeStatus) {
    this.activeStatus = activeStatus;
  }

  public static ActiveStatus of(int status) {
    status = status > 0 ? 1 : status;
    switch (status) {
      case 1:
        {
          return ActiveStatus.ON;
        }
      case 0:
        {
          return ActiveStatus.OFF;
        }
      case -2:
        {
          return ActiveStatus.NOT_SUPPORTED;
        }
      default:
        {
          return ActiveStatus.NO_STATUS;
        }
    }
  }
}
