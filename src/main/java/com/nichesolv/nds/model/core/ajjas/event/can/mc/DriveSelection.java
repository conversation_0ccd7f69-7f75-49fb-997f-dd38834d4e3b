package com.nichesolv.nds.model.core.ajjas.event.can.mc;

import lombok.Getter;

@Getter
// @TypeInfo(DriveSelectionTypeInfo.class)
public enum DriveSelection {
  ECO(1, "ECO"),
  CITY(2, "CITY"),
  <PERSON>OW<PERSON>(3, "POWER"),
  R<PERSON><PERSON><PERSON><PERSON>(4, "REVERS<PERSON>"),
  NULL_DRIVE_SELECTION(0, "NULL_DRIVE_SELECTION");

  private final int driveSelection;
  private final String name;

  DriveSelection(int driveSelection, String name) {
    this.driveSelection = driveSelection;
    this.name = name;
  }

  public static DriveSelection of(int identifier) {
    switch (identifier) {
      case 1:
        {
          return DriveSelection.ECO;
        }
      case 2:
        {
          return DriveSelection.CITY;
        }
      case 3:
        {
          return DriveSelection.POWER;
        }
      case 4:
        {
          return DriveSelection.REVERSE;
        }
      default:
        {
          return DriveSelection.NULL_DRIVE_SELECTION;
        }
    }
  }

  public static DriveSelection of(String name) {
    switch (name) {
      case "CITY":
        {
          return DriveSelection.CITY;
        }
      case "POWER":
        {
          return DriveSelection.POWER;
        }
      case "REVERSE":
        {
          return DriveSelection.REVERSE;
        }
      default:
        {
          return DriveSelection.ECO;
        }
    }
  }
}
