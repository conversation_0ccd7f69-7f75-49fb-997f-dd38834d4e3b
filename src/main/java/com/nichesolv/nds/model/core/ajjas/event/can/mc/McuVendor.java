package com.nichesolv.nds.model.core.ajjas.event.can.mc;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo.McuVendorTypeInfo;
import lombok.Getter;
import org.apache.flink.api.common.typeinfo.TypeInfo;

// todo: This needs to be removed and picked up from the database.
@Getter
@TypeInfo(McuVendorTypeInfo.class)
public enum McuVendor {
  STARS(0),
  MATEL(1);

  private final int mcuVendor;

  McuVendor(int mcuVendor) {
    this.mcuVendor = mcuVendor;
  }
}
