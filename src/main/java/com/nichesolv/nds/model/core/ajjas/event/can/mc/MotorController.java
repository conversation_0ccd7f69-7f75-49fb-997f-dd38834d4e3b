package com.nichesolv.nds.model.core.ajjas.event.can.mc;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo.MotorControllerTypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@TypeInfo(MotorControllerTypeInfoFactory.class)
public interface MotorController {

  MotorControllerStatus getMotorControllerStatus();

  MotorControllerData getMotorControllerData();

  void setMotorControllerStatus(MotorControllerStatus motorControllerStatus);

  void setMotorControllerData(MotorControllerData motorControllerData);

  McuVendor getMcuVendor();

  void setMcuVendor(McuVendor mcuVendor);
}
