package com.nichesolv.nds.model.core.ajjas.event.can.mc;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo.MotorControllerDataTypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@TypeInfo(MotorControllerDataTypeInfoFactory.class)
public interface MotorControllerData {
  Float getDcVoltage();

  Integer getMotorSpeed();

  Float getDcCurrent();

  Float getMotorTemperature();

  Float getMcsTemperature();

  void setDcVoltage(Float dcVoltage);

  void setMotorSpeed(Integer motorSpeed);

  void setDcCurrent(Float dcCurrent);

  void setMotorTemperature(Float motorTemperature);

  void setMcsTemperature(Float mcsTemperature);
}
