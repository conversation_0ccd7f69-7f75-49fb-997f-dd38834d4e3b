package com.nichesolv.nds.model.core.ajjas.event.can.mc;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo.MotorControllerDataImplTypeInfo;
import java.io.Serial;
import java.io.Serializable;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/** This class represents a specific type of motor controller CAN record with 621 as CAN id. */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(MotorControllerDataImplTypeInfo.class)
public class MotorControllerDataImpl implements MotorControllerData, Serializable {

  @Serial private static final long serialVersionUID = 2296866048150895305L;

  private Float dcVoltage;

  private Integer motorSpeed;

  private Float dcCurrent;

  private Float motorTemperature;

  private Float mcsTemperature;
}
