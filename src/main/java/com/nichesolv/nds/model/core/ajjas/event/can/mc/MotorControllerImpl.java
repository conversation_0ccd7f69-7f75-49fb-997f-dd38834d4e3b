package com.nichesolv.nds.model.core.ajjas.event.can.mc;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo.MotorControllerImplTypeInfo;
import java.io.Serial;
import java.io.Serializable;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/**
 * A class that represents CAN record from CAN id: 620 and 621, which happens to be the CAN ids of
 * Stars motor controller.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(MotorControllerImplTypeInfo.class)
public class MotorControllerImpl implements MotorController, Serializable {

  @Serial private static final long serialVersionUID = -3331381838113853103L;

  private McuVendor mcuVendor;

  private MotorControllerStatus motorControllerStatus;

  private MotorControllerData motorControllerData;
}
