package com.nichesolv.nds.model.core.ajjas.event.can.mc;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import lombok.*;

/** This class represents motor controller CAN record with id: 620. */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
// @TypeInfo(MotorControllerStatusImplTypeInfo.class)
public class MotorControllerStatusImpl implements MotorControllerStatus, Serializable {

  @Serial private static final long serialVersionUID = -5563985758135398994L;

  private List<MotorStatus> motorStatuses;

  private StatusFeedback statusFeedback;

  private Integer throttlePercentage;
}
