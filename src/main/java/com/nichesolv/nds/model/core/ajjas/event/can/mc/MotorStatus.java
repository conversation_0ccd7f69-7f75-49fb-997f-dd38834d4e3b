package com.nichesolv.nds.model.core.ajjas.event.can.mc;

import lombok.Getter;

@Getter
// @TypeInfo(MotorStatusTypeInfo.class)
public enum MotorStatus {
  WORKING_FINE(0, "WORKING_FINE"),
  MOSFET_FAULT(1, "MOSFET_FAULT"),
  DRIVE_FAULT(2, "DRIVE_FAULT"),
  OVERLOAD_FAULT(4, "OVERLOAD_FAULT"),
  OVER_VOLTAGE_FAULT(8, "OVERVOLTAGE_FAULT"),
  OVER_CURRENT_FAULT(10, "OVER_CURRENT_FAULT"),
  UNDER_VOLTAGE_FAULT(12, "UNDER_VOLTAGE_FAULT"),
  SPEED_OVERSHOOT_FAULT(14, "SPEED_OVERSHOOT_FAULT"),
  OVERHEAT_FAULT(16, "OVERHEAT_FAULT"),
  LOW_VOLTAGE_FAULT(32, "L<PERSON>_VOLTAGE_FAULT"),
  MOTOR_LOST_HALL_FAULT(64, "MOTOR_LOST_HALL_FAULT"),
  HALL_SENSOR_FAULT(128, "HALL_SENSOR_FAULT"),
  MOTOR_OVERHEAT_FAULT(256, "MOTOR_OVERHEAT_FAULT"),
  MOTOR_STUCK_FAULT(512, "MOTOR_STUCK_FAULT"),
  THROTTLE_FAULT(1024, "THROTTLE_FAULT"),
  NO_STATUS(1025, "NO_STATUS");

  private final int motorStatus;
  private final String name;

  MotorStatus(int motorStatus, String name) {
    this.motorStatus = motorStatus;
    this.name = name;
  }

  public static MotorStatus of(int value) {
    switch (value) {
      case 0:
        {
          return MotorStatus.WORKING_FINE;
        }
      case 1:
        {
          return MotorStatus.MOSFET_FAULT;
        }
      case 2:
        {
          return MotorStatus.DRIVE_FAULT;
        }
      case 4:
        {
          return MotorStatus.OVERLOAD_FAULT;
        }
      case 8:
        {
          return MotorStatus.OVER_VOLTAGE_FAULT;
        }
      case 10: {
        return MotorStatus.OVER_CURRENT_FAULT;
      }
      case 12: {
        return MotorStatus.UNDER_VOLTAGE_FAULT;
      }
      case 14: {
        return MotorStatus.SPEED_OVERSHOOT_FAULT;
      }
      case 16:
        {
          return MotorStatus.OVERHEAT_FAULT;
        }
      case 32:
        {
          return MotorStatus.LOW_VOLTAGE_FAULT;
        }
      case 64:
        {
          return MotorStatus.MOTOR_LOST_HALL_FAULT;
        }
      case 128:
        {
          return MotorStatus.HALL_SENSOR_FAULT;
        }
      case 256:
        {
          return MotorStatus.MOTOR_OVERHEAT_FAULT;
        }
      case 512:
        {
          return MotorStatus.MOTOR_STUCK_FAULT;
        }
      case 1024:
        {
          return MotorStatus.THROTTLE_FAULT;
        }
      default:
        {
          return MotorStatus.NO_STATUS;
        }
    }
  }

  public static MotorStatus of(String value) {
    switch (value) {
      case "WORKING_FINE":
        {
          return MotorStatus.WORKING_FINE;
        }
      case "MOSFET_FAULT":
        {
          return MotorStatus.MOSFET_FAULT;
        }
      case "DRIVE_FAULT":
        {
          return MotorStatus.DRIVE_FAULT;
        }
      case "OVERLOAD_FAULT":
        {
          return MotorStatus.OVERLOAD_FAULT;
        }
      case "OVERVOLTAGE_FAULT":
        {
          return MotorStatus.OVER_VOLTAGE_FAULT;
        }
      case "OVERHEAT_FAULT":
        {
          return MotorStatus.OVERHEAT_FAULT;
        }
      case "LOW_VOLTAGE_FAULT":
        {
          return MotorStatus.LOW_VOLTAGE_FAULT;
        }
      case "MOTOR_LOST_HALL_FAULT":
        {
          return MotorStatus.MOTOR_LOST_HALL_FAULT;
        }
      case "HALL_SENSOR_FAULT":
        {
          return MotorStatus.HALL_SENSOR_FAULT;
        }
      case "MOTOR_OVERHEAT_FAULT":
        {
          return MotorStatus.MOTOR_OVERHEAT_FAULT;
        }
      case "MOTOR_STUCK_FAULT":
        {
          return MotorStatus.MOTOR_STUCK_FAULT;
        }
      case "THROTTLE_FAULT":
        {
          return MotorStatus.THROTTLE_FAULT;
        }
      default:
        {
          return MotorStatus.NO_STATUS;
        }
    }
  }
}
