package com.nichesolv.nds.model.core.ajjas.event.can.mc;

import java.io.Serial;
import java.io.Serializable;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
// @TypeInfo(StatusFeedbackTypeInfo.class)
public class StatusFeedback implements Serializable {
  @Serial private static final long serialVersionUID = -5463374654098055003L;

  private DriveSelection driveSelection;

  private boolean regeneration;

  private boolean readySign;

  private boolean pLight;

  private boolean reverse;

  private boolean cruise;

  private boolean vehicleBrake;

  private boolean sideStand;
}
