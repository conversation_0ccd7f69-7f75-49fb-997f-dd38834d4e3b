package com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.ActiveStatus;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class ActiveStatusTypeInfo extends TypeInfoFactory<ActiveStatus> {
  @Override
  public TypeInformation<ActiveStatus> createTypeInfo(Type t, Map<String, TypeInformation<?>> map) {
    return Types.ENUM(ActiveStatus.class);
  }
}
