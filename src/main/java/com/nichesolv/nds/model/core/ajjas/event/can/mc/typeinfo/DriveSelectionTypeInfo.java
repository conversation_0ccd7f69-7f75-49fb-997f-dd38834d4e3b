package com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.DriveSelection;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class DriveSelectionTypeInfo extends TypeInfoFactory<DriveSelection> {
  @Override
  public TypeInformation<DriveSelection> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.ENUM(DriveSelection.class);
  }
}
