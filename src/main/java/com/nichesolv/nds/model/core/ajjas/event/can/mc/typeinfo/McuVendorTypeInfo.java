package com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.McuVendor;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class McuVendorTypeInfo extends TypeInfoFactory<McuVendor> {
  @Override
  public TypeInformation<McuVendor> createTypeInfo(Type t, Map<String, TypeInformation<?>> map) {
    return Types.ENUM(McuVendor.class);
  }
}
