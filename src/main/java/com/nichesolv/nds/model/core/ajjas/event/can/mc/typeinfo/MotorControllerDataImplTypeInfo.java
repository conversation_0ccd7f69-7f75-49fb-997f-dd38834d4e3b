package com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo;

import com.google.common.collect.ImmutableMap;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerDataImpl;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class MotorControllerDataImplTypeInfo extends TypeInfoFactory<MotorControllerDataImpl> {
  @Override
  public TypeInformation<MotorControllerDataImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(
        MotorControllerDataImpl.class,
        ImmutableMap.of(
            "dcVoltage",
            Types.FLOAT,
            "motorSpeed",
            Types.INT,
            "dcCurrent",
            Types.FLOAT,
            "motorTemperature",
            Types.FLOAT,
            "mcsTemperature",
            Types.FLOAT));
  }
}
