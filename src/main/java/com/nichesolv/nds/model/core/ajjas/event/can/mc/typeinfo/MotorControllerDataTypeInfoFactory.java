package com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerData;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;

/** Motor controller type information factory. */
public class MotorControllerDataTypeInfoFactory extends TypeInfoFactory<MotorControllerData> {
  @Override
  public TypeInformation<MotorControllerData> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return MotorControllerDataTypeInfo.getTypeInfo();
  }
}
