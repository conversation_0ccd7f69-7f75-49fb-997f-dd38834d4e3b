package com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo;

import com.google.common.collect.ImmutableMap;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.McuVendor;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerData;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerImpl;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerStatus;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class MotorControllerImplTypeInfo extends TypeInfoFactory<MotorControllerImpl> {
  @Override
  public TypeInformation<MotorControllerImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(
        MotorControllerImpl.class,
        ImmutableMap.of(
            "mcuVendor",
            Types.ENUM(McuVendor.class),
            "motorControllerStatus",
            Types.POJO(MotorControllerStatus.class, new HashMap<>()),
            "motorControllerData",
            Types.POJO(MotorControllerData.class, new HashMap<>())));
  }
}
