package com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo;

import com.google.common.collect.ImmutableMap;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.*;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class MotorControllerStatusImplTypeInfo extends TypeInfoFactory<MotorControllerStatusImpl> {
  @Override
  public TypeInformation<MotorControllerStatusImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(
        MotorControllerStatusImpl.class,
        ImmutableMap.of(
            "motorStatus",
            Types.ENUM(MotorStatus.class),
            "statusFeedback",
            Types.POJO(StatusFeedback.class, new HashMap<>()),
            "throttlePercentage",
            Types.INT));
  }
}
