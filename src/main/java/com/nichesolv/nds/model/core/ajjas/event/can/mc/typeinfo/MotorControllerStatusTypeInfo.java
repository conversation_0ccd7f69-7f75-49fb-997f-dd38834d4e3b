package com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.*;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class MotorControllerStatusTypeInfo extends TypeInfoFactory<MotorControllerStatus> {
  @Override
  public TypeInformation<MotorControllerStatus> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(MotorControllerStatus.class, new HashMap<>());
  }
}
