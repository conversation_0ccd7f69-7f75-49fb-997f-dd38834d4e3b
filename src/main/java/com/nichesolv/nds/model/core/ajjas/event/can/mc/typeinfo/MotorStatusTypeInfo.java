package com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorStatus;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class MotorStatusTypeInfo extends TypeInfoFactory<MotorStatus> {
  @Override
  public TypeInformation<MotorStatus> createTypeInfo(Type t, Map<String, TypeInformation<?>> map) {
    return Types.ENUM(MotorStatus.class);
  }
}
