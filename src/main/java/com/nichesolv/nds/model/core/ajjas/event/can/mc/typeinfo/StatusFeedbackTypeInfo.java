package com.nichesolv.nds.model.core.ajjas.event.can.mc.typeinfo;

import com.google.common.collect.ImmutableMap;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.DriveSelection;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.StatusFeedback;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class StatusFeedbackTypeInfo extends TypeInfoFactory<StatusFeedback> {
  @Override
  public TypeInformation<StatusFeedback> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(
        StatusFeedback.class,
        ImmutableMap.of(
            "driveSelection",
            Types.ENUM(DriveSelection.class),
            "regeneration",
            Types.BOOLEAN,
            "readySign",
            Types.BOOLEAN,
            "pLight",
            Types.BOOLEAN,
            "reverse",
            Types.BOOLEAN,
            "cruise",
            Types.BOOLEAN,
            "vehicleBrake",
            Types.BOOLEAN,
            "sideStand",
            Types.BOOLEAN));
  }
}
