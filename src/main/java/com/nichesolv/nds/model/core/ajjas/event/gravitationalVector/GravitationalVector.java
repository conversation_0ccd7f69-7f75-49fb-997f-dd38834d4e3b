package com.nichesolv.nds.model.core.ajjas.event.gravitationalVector;

import com.nichesolv.nds.model.core.ajjas.event.Event;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.typeinfo.GravitationalVectorTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@TypeInfo(GravitationalVectorTypeInfo.class)
public interface GravitationalVector extends Event {
  float getX();

  float getY();

  float getZ();

  void setX(float x);

  void setY(float y);

  void setZ(float z);
}
