package com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVector;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class GravitationalVectorTypeInfo extends TypeInfoFactory<GravitationalVector> {

  @Override
  public TypeInformation<GravitationalVector> createTypeInfo(Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(GravitationalVector.class, new HashMap<>());
  }
}
