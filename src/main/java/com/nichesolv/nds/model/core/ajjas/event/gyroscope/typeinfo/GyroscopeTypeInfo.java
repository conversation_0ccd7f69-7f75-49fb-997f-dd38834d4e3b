package com.nichesolv.nds.model.core.ajjas.event.gyroscope.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.gyroscope.Gyroscope;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

public class GyroscopeTypeInfo extends TypeInfoFactory<Gyroscope> {
  @Override
  public TypeInformation<Gyroscope> createTypeInfo(Type t, Map<String, TypeInformation<?>> map) {
    return Types.POJO(Gyroscope.class, new HashMap<>());
  }
}
