package com.nichesolv.nds.model.core.ajjas.event.io;

import lombok.Getter;

@Getter
public enum AnalogInputIdentifier {
  TEMP(0, "temperature"),

  VIN(1, "voltageInput"),

  VSYS(2, "SystemVoltage"),

  VBUCK(3, "vbuck"),

  VUSR_1(4, "vusr1"),

  VUSR_2(5, "vusr2"),

  LEAN_ANG(6, "leanAngle"),

  NO_ANALOG_INPUT_IDENTIFIER(Integer.MAX_VALUE, "noAnalogInput");

  private final int analogInputIdentifier;

  // todo: I don't want this but have to do this because the REST api uses this names for field
  // names.
  private final String fieldName;

  AnalogInputIdentifier(int analogInputIdentifier, String fieldName) {
    this.analogInputIdentifier = analogInputIdentifier;
    this.fieldName = fieldName;
  }

  public static AnalogInputIdentifier of(int analogInputIdentifier) {
    switch (analogInputIdentifier) {
      case 0:
        return AnalogInputIdentifier.TEMP;
      case 1:
        return AnalogInputIdentifier.VIN;
      case 2:
        return AnalogInputIdentifier.VSYS;
      case 3:
        return AnalogInputIdentifier.VBUCK;
      case 4:
        return AnalogInputIdentifier.VUSR_1;
      case 5:
        return AnalogInputIdentifier.VUSR_2;
      case 6:
        return AnalogInputIdentifier.LEAN_ANG;
      default:
        return AnalogInputIdentifier.NO_ANALOG_INPUT_IDENTIFIER;
    }
  }
}
