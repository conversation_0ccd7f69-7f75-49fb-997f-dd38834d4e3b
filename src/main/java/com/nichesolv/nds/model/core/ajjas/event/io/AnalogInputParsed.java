package com.nichesolv.nds.model.core.ajjas.event.io;

public interface AnalogInputParsed {
    Integer getTemp();
    void setTemp(Integer temp);

    Integer getVin();
    void setVin(Integer vin);

    Integer getVsys();
    void setVsys(Integer vsys);

    Integer getVbuck();
    void setVbuck(Integer vbuck);

    Integer getVusr1();
    void setVusr1(Integer vusr1);

    Integer getVusr2();
    void setVusr2(Integer vusr2);

    Integer getLeanAngle();
    void setLeanAngle(Integer leanAngle);
}
