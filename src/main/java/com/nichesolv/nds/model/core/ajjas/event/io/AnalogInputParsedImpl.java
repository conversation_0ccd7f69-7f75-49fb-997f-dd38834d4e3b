package com.nichesolv.nds.model.core.ajjas.event.io;

import java.io.Serial;
import java.io.Serializable;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class AnalogInputParsedImpl implements AnalogInputParsed, Serializable {
  @Serial private static final long serialVersionUID = 3530587690553092760L;

  private Integer temp;
  private Integer vin;
  private Integer vsys;
  private Integer vbuck;
  private Integer vusr1;
  private Integer vusr2;
  private Integer leanAngle;
}
