package com.nichesolv.nds.model.core.ajjas.event.io;

import com.nichesolv.nds.model.core.ajjas.event.Event;
import com.nichesolv.nds.model.core.ajjas.event.io.typeinfo.IOTypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@TypeInfo(IOTypeInfoFactory.class)
public interface IO extends Event {
  DigitalInput getDigitalInput();

  DigitalOutput getDigitalOutput();

  java.util.List<AnalogInput> getAnalogInputs();

  void setDigitalInput(DigitalInput digitalInput);

  void setDigitalOutput(DigitalOutput digitalOutput);

  void setAnalogInputs(java.util.List<AnalogInput> analogInputs);
}
