package com.nichesolv.nds.model.core.ajjas.event.io;

import com.nichesolv.nds.model.core.ajjas.event.EventImpl;
import com.nichesolv.nds.model.core.ajjas.event.io.typeinfo.IOImplTypeInfoFactory;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TypeInfo(IOImplTypeInfoFactory.class)
public class IOImpl extends EventImpl implements Serializable, IO {

  @Serial private static final long serialVersionUID = -7593892595386032628L;

  private DigitalInput digitalInput;

  private DigitalOutput digitalOutput;

  private List<AnalogInput> analogInputs;
}
