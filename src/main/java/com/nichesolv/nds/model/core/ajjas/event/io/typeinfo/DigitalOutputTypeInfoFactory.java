package com.nichesolv.nds.model.core.ajjas.event.io.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.io.DigitalOutput;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class DigitalOutputTypeInfoFactory extends TypeInfoFactory<DigitalOutput> {
  @Override
  public TypeInformation<DigitalOutput> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return Types.POJO(DigitalOutput.class, new HashMap<>());
  }
}
