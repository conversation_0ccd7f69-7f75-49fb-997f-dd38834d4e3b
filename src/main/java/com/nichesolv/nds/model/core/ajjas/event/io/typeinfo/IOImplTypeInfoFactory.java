package com.nichesolv.nds.model.core.ajjas.event.io.typeinfo;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInput;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalInput;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalOutput;
import com.nichesolv.nds.model.core.ajjas.event.io.IOImpl;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.shaded.guava30.com.google.common.collect.ImmutableMap;

/**
 * private DigitalInput digitalInput;
 *
 * <p>private DigitalOutput digitalOutput;
 *
 * <p>private List<AnalogInput> analogInputs;
 */
public class IOImplTypeInfoFactory extends TypeInfoFactory<IOImpl> {
  @Override
  public TypeInformation<IOImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return Types.POJO(
        IOImpl.class,
        ImmutableMap.of(
            "digitalInput",
            Types.POJO(DigitalInput.class, new HashMap<>()),
            "digitalOutput",
            Types.POJO(DigitalOutput.class, new HashMap<>()),
            "analogInputs",
            Types.LIST(Types.POJO(AnalogInput.class, new HashMap<>()))));
  }
}
