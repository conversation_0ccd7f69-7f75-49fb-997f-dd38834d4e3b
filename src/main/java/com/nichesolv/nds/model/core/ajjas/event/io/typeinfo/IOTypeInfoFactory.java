package com.nichesolv.nds.model.core.ajjas.event.io.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.io.IO;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;

public class IOTypeInfoFactory extends TypeInfoFactory<IO> {
  @Override
  public TypeInformation<IO> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return IOTypeInfo.getTypeInfo();
  }
}
