package com.nichesolv.nds.model.core.ajjas.event.location;

import com.nichesolv.nds.model.core.ajjas.event.Event;

public interface Location extends Event {
  double getLatitude();

  double getLongitude();

  float getAltitude();

  float getSpeed();

  float getBearing();

  int getPdop();

  int getHdop();

  int getVdop();

  int getViewSats();

  int getTrackSats();

  void setLatitude(double latitude);

  void setLongitude(double longitude);

  void setAltitude(float altitude);

  void setSpeed(float speed);

  void setBearing(float bearing);

  void setPdop(int pdop);

  void setHdop(int hdop);

  void setVdop(int vdop);

  void setViewSats(int viewSats);

  void setTrackSats(int trackSats);
}
