package com.nichesolv.nds.model.core.ajjas.event.location;

import com.nichesolv.nds.model.core.ajjas.event.EventImpl;
import java.io.Serial;
import java.io.Serializable;
import lombok.*;

/** This class represents a Location event. */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class LocationImpl extends EventImpl implements Location, Serializable {

  @Serial private static final long serialVersionUID = 9051989712512364460L;
  private double latitude;

  private double longitude;

  private float altitude;

  private float speed;

  private float bearing;

  private int pdop;

  private int hdop;

  private int vdop;

  private int viewSats;

  private int trackSats;
}
