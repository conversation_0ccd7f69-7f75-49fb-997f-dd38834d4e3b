package com.nichesolv.nds.model.core.ajjas.event.location.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.location.Location;
import java.util.HashMap;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

/** Location type info. */
public class LocationTypeInfo {

  public static TypeInformation<Location> getTypeInfo() {
    return Types.POJO(Location.class, new HashMap<>());
  }
}
