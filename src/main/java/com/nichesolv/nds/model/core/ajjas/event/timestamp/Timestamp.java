package com.nichesolv.nds.model.core.ajjas.event.timestamp;

import com.nichesolv.nds.model.core.ajjas.event.Event;
import com.nichesolv.nds.model.core.ajjas.event.timestamp.typeinfo.TimestampTypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@TypeInfo(TimestampTypeInfoFactory.class)
public interface Timestamp extends Event {

  void setTimestamp(int timestamp);

  int getTimestamp();

  long getIngestionTime();

  void setIngestionTime(long ingestionTime);
}
