package com.nichesolv.nds.model.core.ajjas.event.timestamp;

import com.nichesolv.nds.model.core.ajjas.event.EventImpl;
import java.io.Serial;
import java.io.Serializable;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.typeinfo.TimestampImplTypeInfoFactory;
import lombok.*;
import org.apache.flink.api.common.typeinfo.TypeInfo;

/**
 * This class represents the primary timestamp object within the system. Timestamp is one of the
 * events received as part of the event buffer. The timestamp event within the event buffer has a
 * length of 4 bytes.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@TypeInfo(TimestampImplTypeInfoFactory.class)
public class TimestampImpl extends EventImpl implements Timestamp, Serializable {
  @Serial private static final long serialVersionUID = -720031349517064223L;

  private int timestamp;

  private long ingestionTime;
}
