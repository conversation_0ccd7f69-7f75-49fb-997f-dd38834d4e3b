package com.nichesolv.nds.model.core.ajjas.event.timestamp.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.shaded.guava30.com.google.common.collect.ImmutableMap;

public class TimestampImplTypeInfo {

  public static TypeInformation<TimestampImpl> getTypeInfo() {
    return Types.POJO(
        TimestampImpl.class,
        ImmutableMap.of(
            "timestamp", Types.INT,
            "ingestionTime", Types.LONG));
  }
}
