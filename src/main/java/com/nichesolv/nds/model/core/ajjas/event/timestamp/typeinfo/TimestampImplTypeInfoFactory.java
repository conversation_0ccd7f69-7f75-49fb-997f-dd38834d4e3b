package com.nichesolv.nds.model.core.ajjas.event.timestamp.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.TimestampImpl;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;

public class TimestampImplTypeInfoFactory extends TypeInfoFactory<TimestampImpl> {
  @Override
  public TypeInformation<TimestampImpl> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return TimestampImplTypeInfo.getTypeInfo();
  }
}
