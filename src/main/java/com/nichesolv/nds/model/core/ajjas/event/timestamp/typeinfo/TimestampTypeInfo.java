package com.nichesolv.nds.model.core.ajjas.event.timestamp.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import java.util.HashMap;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

public class TimestampTypeInfo {
  public static TypeInformation<Timestamp> getTypeInfo() {
    return Types.POJO(Timestamp.class, new HashMap<>());
  }
}
