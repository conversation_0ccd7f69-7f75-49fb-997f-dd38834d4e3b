package com.nichesolv.nds.model.core.ajjas.event.timestamp.typeinfo;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;

public class TimestampTypeInfoFactory extends TypeInfoFactory<Timestamp> {
  @Override
  public TypeInformation<Timestamp> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return TimestampTypeInfo.getTypeInfo();
  }
}
