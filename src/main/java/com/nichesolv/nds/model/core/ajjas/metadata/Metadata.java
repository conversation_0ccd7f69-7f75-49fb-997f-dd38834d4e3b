package com.nichesolv.nds.model.core.ajjas.metadata;

import com.nichesolv.nds.model.core.ajjas.metadata.typeinfo.MetadataTypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@TypeInfo(MetadataTypeInfoFactory.class)
public interface Metadata {

  void setImei(long imei);

  long getImei();

  void setMagic(String magic);

  String getMagic();

  void setSqn(short sqn);

  short getSqn();

  void setCrc16(short crc16);

  short getCrc16();

  String getCorrelationId();

  void setCorrelationId(String correlationId);
}
