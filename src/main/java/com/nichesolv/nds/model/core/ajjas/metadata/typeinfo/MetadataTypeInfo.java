package com.nichesolv.nds.model.core.ajjas.metadata.typeinfo;

import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.util.HashMap;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

/** Metadata type information. */
public class MetadataTypeInfo {

  public static TypeInformation<Metadata> getTypeInfo() {
    return Types.POJO(Metadata.class, new HashMap<>());
  }
}
