package com.nichesolv.nds.model.core.ajjas.metadata.typeinfo;

import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import java.lang.reflect.Type;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;

/** Type information factory. */
public class MetadataTypeInfoFactory extends TypeInfoFactory<Metadata> {
  @Override
  public TypeInformation<Metadata> createTypeInfo(
      Type t, Map<String, TypeInformation<?>> genericParameters) {
    return MetadataTypeInfo.getTypeInfo();
  }
}
