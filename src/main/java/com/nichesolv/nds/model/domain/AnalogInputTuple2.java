package com.nichesolv.nds.model.domain;

import org.apache.flink.api.java.tuple.Tuple2;

// This is just a duplicate of other tuple2 extensions just to follow a better naming convention we
// are doing this.
public class AnalogInputTuple2 extends Tuple2<Float, Float> {

  private static final long serialVersionUID = 252897282969195610L;

  public AnalogInputTuple2() {}

  public AnalogInputTuple2(Float temperature, Float voltage) {
    super(temperature, voltage);
  }
}
