package com.nichesolv.nds.model.domain;

import com.nichesolv.nds.model.core.ajjas.event.timestamp.Timestamp;
import com.nichesolv.nds.model.core.ajjas.metadata.Metadata;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.tuple.Tuple4;
import org.apache.flink.types.Row;

public class CompositeKey extends Tuple2<Long, String> {

  private static final long serialVersionUID = -5409721394436022068L;

  private static final CompositeKey defaultKey = new CompositeKey(Long.MAX_VALUE, "NO_KEY");

  public CompositeKey() {}

  public CompositeKey(Long f0, String f1) {
    super(f0, f1);
  }

  public static CompositeKey of(Long f0, String f1) {
    return new CompositeKey(f0, f1);
  }

  public static TypeInformation<CompositeKey> getType() {
    return Types.TUPLE(Types.LONG, Types.STRING);
  }

  public static class FromTuple4<E extends Tuple4<Timestamp, Metadata, Window, ?>>
      implements KeySelector<E, CompositeKey> {

    private static final long serialVersionUID = 6465304078112370936L;

    @Override
    public CompositeKey getKey(E value) throws Exception {
      return new CompositeKey(value.f1.getImei(), value.f1.getCorrelationId());
    }
  }

  public static class FromTuple3<E extends Tuple3<Timestamp, Metadata, ?>>
      implements KeySelector<E, CompositeKey> {

    private static final long serialVersionUID = -9192061048474728428L;

    @Override
    public CompositeKey getKey(E value) throws Exception {
      return new CompositeKey(value.f1.getImei(), value.f1.getCorrelationId());
    }
  }

  public static class FromRow implements KeySelector<Row, CompositeKey> {

    private static final long serialVersionUID = -4562920890842704332L;

    @Override
    public CompositeKey getKey(Row row) throws Exception {
      Long imei = row.getFieldAs("imei");
      String correlationId = row.getFieldAs("correlation_id");
      return new CompositeKey(imei, correlationId);
    }
  }
}
