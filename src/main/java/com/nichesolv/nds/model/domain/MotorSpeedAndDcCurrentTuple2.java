package com.nichesolv.nds.model.domain;

import org.apache.flink.api.java.tuple.Tuple2;

// This is just a duplicate of other tuple2 extensions just to follow a better naming convention we
// are doing this.
public class MotorSpeedAndDcCurrentTuple2 extends Tuple2<Float, Float> {

  private static final long serialVersionUID = 6635256346158745240L;

  public MotorSpeedAndDcCurrentTuple2() {}

  public MotorSpeedAndDcCurrentTuple2(Float motorSpeed, Float dcCurrent) {
    super(motorSpeed, dcCurrent);
  }
}
