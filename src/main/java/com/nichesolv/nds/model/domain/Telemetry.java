package com.nichesolv.nds.model.domain;

import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerData;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerStatus;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVector;
import com.nichesolv.nds.model.core.ajjas.event.gyroscope.Gyroscope;
import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInputParsed;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalInput;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalOutput;
import com.nichesolv.nds.model.core.ajjas.event.location.Location;

public interface Telemetry {
    AnalogInputParsed getAnalogInput();
    void setAnalogInput(AnalogInputParsed analogInput);

    DigitalInput getDigitalInput();
    void setDigitalInput(DigitalInput digitalInput);

    DigitalOutput getDigitalOutput();
    void setDigitalOutput(DigitalOutput digitalOutput);

    Location getLocation();
    void setLocation(Location location);

    Accelerometer getAccelerometer();
    void setAccelerometer(Accelerometer accelerometer);

    MotorControllerData getMotorControllerData();
    void setMotorControllerData(MotorControllerData motorControllerData);

    MotorControllerStatus getMotorControllerStatus();
    void setMotorControllerStatus(MotorControllerStatus motorControllerStatus);

    Gyroscope getGyroscope();
    void setGyroscope(Gyroscope gyroscope);

    GravitationalVector getGravitationalVector();
    void setGravitationalVector(GravitationalVector gravitationalVector);

}
