package com.nichesolv.nds.model.domain;

import com.nichesolv.nds.model.core.ajjas.event.accelerometer.Accelerometer;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerData;
import com.nichesolv.nds.model.core.ajjas.event.can.mc.MotorControllerStatus;
import com.nichesolv.nds.model.core.ajjas.event.gravitationalVector.GravitationalVector;
import com.nichesolv.nds.model.core.ajjas.event.gyroscope.Gyroscope;
import com.nichesolv.nds.model.core.ajjas.event.io.AnalogInputParsed;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalInput;
import com.nichesolv.nds.model.core.ajjas.event.io.DigitalOutput;
import com.nichesolv.nds.model.core.ajjas.event.location.Location;

import java.io.Serializable;

import lombok.*;

// A simple telemetry class.
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class TelemetryImpl implements Telemetry, Serializable {

  private static final long serialVersionUID = 2316202201118830802L;

  private AnalogInputParsed analogInput;

  private DigitalInput digitalInput;

  private DigitalOutput digitalOutput;

  private Location location;

  private Accelerometer accelerometer;

  private MotorControllerData motorControllerData;

  private MotorControllerStatus motorControllerStatus;

  private Gyroscope gyroscope;

  private GravitationalVector gravitationalVector;
}
