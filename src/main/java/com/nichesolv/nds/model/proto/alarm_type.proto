syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "AlarmTypeProto";


enum AlarmType {

  CELL_UNDER_VOLTAGE_ALARM = 0;

  CELL_OVER_VOLTAGE_ALARM = 1;

  PACK_UNDER_VOLTAGE_ALARM = 2;

  PACK_OVER_VOLTAGE_ALARM = 3;

  CELL_UNDER_TEMPERATURE_ALARM = 4;

  CELL_OVER_TEMPERATURE_ALARM = 5;

  AMBIENT_UNDER_TEMPERATURE_ALARM = 6;

  AMBIENT_OVER_TEMPERATURE_ALARM = 7;

  MOSFET_UNDER_TEMPERATURE_ALARM = 8;

  MOSFET_OVER_TEMPERATURE_ALARM = 9;

  BUZZER_OR_LED_ALARM = 10;

  CGH_OVER_CURRENT_ALARM = 11;

  DSG_OVER_CURRENT_ALARM = 12;

  NO_ALARM = -1;

}