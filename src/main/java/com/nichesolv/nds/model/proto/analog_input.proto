syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "AnalogInputProto";

import "com/nichesolv/nds/model/proto/timestamp.proto";
import  "com/nichesolv/nds/model/proto/metadata.proto";

// Represents the analog input message.
message AnalogInput {

  optional sint32 temp = 1;

  optional uint32 vin = 2;

  optional uint32 vsys = 3;

  optional uint32 vbuck = 5;

  optional uint32 vusr_1 = 7;

  optional uint32 vusr_2 = 9;

  optional uint32 lean_angle = 11;

  // Timestamp.
  Timestamp timestamp = 12;

  // Metadata.
  Metadata metadata = 13;

}