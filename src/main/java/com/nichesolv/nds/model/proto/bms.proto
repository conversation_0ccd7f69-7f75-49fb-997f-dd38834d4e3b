syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "BatteryManagementSystemProto";

import "com/nichesolv/nds/model/proto/alarm_type.proto";
import "com/nichesolv/nds/model/proto/protection.proto";

import "com/nichesolv/nds/model/proto/timestamp.proto";
import  "com/nichesolv/nds/model/proto/metadata.proto";

message BmsMetadata {

  optional string battery_serial_number = 1;

  optional string bms_serial_number = 3;

  optional string protocol_version = 5;

  optional sint64 rated_capacity = 7;

  optional sint32 active_cell = 9;

  optional sint32 active_temp = 11;

  optional sint32 dod = 13;

  optional Chemistry chemistry = 15;

  // Timestamp.
  Timestamp timestamp = 16;

  // Metadata.
  Metadata metadata = 17;

}

message Status {

  optional sint32 chgCycleCount = 1;

  // DSG CYCLE COUNT
  optional sint32 dsgCycleCount = 3;

  // Alarms
  repeated Alarm alarms = 5;

  // Protections
  repeated  Protection protections = 7;

  // Cell Volt Min (uint16_t) (V)
  optional float cellVoltMin = 9;

  // Cell Volt Maximum  (V)
  optional float cellVoltMax = 11;

  // Temp minimum (in celsius)	.
  optional float temperatureMin = 13;

  // Temp maximum  (in Celsius)
  optional float temperatureMax = 15;

  /** Batt Volt (float) (V) */
  optional float batteryVolt = 17;

  /** SOC  */
  optional float soc = 19;

  /** SOH  */
  optional float soh = 21;

  // Current (float) (A).
  optional float current = 23;

  // Balancing Status(uint32_t) Need to do bit wise operation.
  repeated BalancingStatus balancingStatuses = 25;

  // Remaining Capacity
  optional sint32 remainingCapacity = 26;

  // Timestamp.
  Timestamp timestamp = 27;

  // Metadata.
  Metadata metadata = 28;

  // Temp maximum  (in Celsius)
  optional float mosfetTemperature = 29;
}

message Temperature {

  repeated CellTemperature cell_temperatures = 1;

  // Timestamp.
  Timestamp timestamp = 2;

  // Metadata.
  Metadata metadata = 3;

}

message CellTemperature {

  optional sint32 cell_id = 1;

  optional float temperature = 3;

}

message Voltage {

  repeated CellVoltage cell_voltages = 1;

  // Timestamp.
  Timestamp timestamp = 2;

  // Metadata.
  Metadata metadata = 3;

}

message CellVoltage {

  optional uint32 cell_id = 1;

  optional float volts = 3;

}

enum Chemistry {

  NCM = 0;

  LFP = 1;

  NO_CHEMISTRY = 9999;
}

message BalancingStatus {

  optional uint32 cell_id = 1;

  optional bool isSet = 3;

}

message Alarm {

  optional AlarmType alarm_type = 1;

  optional bool isSet = 3;

}