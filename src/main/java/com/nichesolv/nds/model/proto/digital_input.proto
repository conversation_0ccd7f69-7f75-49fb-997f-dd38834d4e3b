syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "DigitalInputProto";

import "com/nichesolv/nds/model/proto/timestamp.proto";
import  "com/nichesolv/nds/model/proto/metadata.proto";

// Represents digital input.
message DigitalInput {

  optional bool usr1 = 1;

  optional bool usr2 = 3;

  optional bool motion = 5;

  optional bool tamper = 7;

  optional bool mainPower = 9;

  optional bool ignition = 11;

  Timestamp timestamp = 12;

  Metadata metadata = 13;

}