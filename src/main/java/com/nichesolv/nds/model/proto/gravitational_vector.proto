syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "GravitationalVectorProto";

import "com/nichesolv/nds/model/proto/timestamp.proto";
import  "com/nichesolv/nds/model/proto/metadata.proto";

// Represents the GravitationalVector protocol. GravitationalVector data is part of the TCU.
message GravitationalVector {

  // x axis.
  optional float x = 1;

  // y axis.
  optional float y = 2;

  // z axis.
  optional float z = 3;

  // Timestamp.
  Timestamp timestamp = 6;

  // Metadata.
  Metadata metadata = 7;

}