syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "GyroscopeProto";

import "com/nichesolv/nds/model/proto/timestamp.proto";
import  "com/nichesolv/nds/model/proto/metadata.proto";

// Represents the Gyroscope protocol. Gyroscope data is part of the TCU.
message Gyroscope {

  // x axis.
  optional sint32 x = 1;

  // y axis.
  optional sint32 y = 2;

  // z axis.
  optional sint32 z = 3;

  // Timestamp.
  Timestamp timestamp = 6;

  // Metadata.
  Metadata metadata = 7;

}