syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "ImuProto";

import "com/nichesolv/nds/model/proto/timestamp.proto";
import "com/nichesolv/nds/model/proto/metadata.proto";

message Imu {

  // Metadata
  Metadata metadata = 1;

  // Timestamp
  Timestamp timestamp = 2;

  // Accelerometer Data
  message Accelerometer {
    optional sint32 x = 1;
    optional sint32 y = 2;
    optional sint32 z = 3;
  }
  optional Accelerometer accelerometer = 3;

  // Gyroscope Data
  message Gyroscope {
    optional sint32 x = 1;
    optional sint32 y = 2;
    optional sint32 z = 3;
  }
  optional Gyroscope gyroscope = 4;

  // Gravitational Vector Data
  message GravitationalVector {
    optional float x = 1;
    optional float y = 2;
    optional float z = 3;
  }
  optional GravitationalVector gravitational_vector = 5;

  // Digital Input Data
  message DigitalInput {
    optional bool motion = 1;
    optional bool ignition = 2;
  }
  optional DigitalInput digital_input = 6;


}