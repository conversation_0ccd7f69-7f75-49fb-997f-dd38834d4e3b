syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "LocationProto";

import "com/nichesolv/nds/model/proto/timestamp.proto";
import  "com/nichesolv/nds/model/proto/metadata.proto";

// This message represents the location event.
message Location {

  optional double latitude = 1;

  optional double longitude = 3;

  optional float altitude = 5;

  optional float speed = 7;

  optional float bearing = 9;

  optional sint32 pdop = 11;

  optional sint32 hdop = 13;

  optional sint32 vdop = 15;

  optional sint32 view_sats = 17;

  optional sint32 track_sats = 19;

  Timestamp timestamp = 20;

  Metadata metadata = 21;

}