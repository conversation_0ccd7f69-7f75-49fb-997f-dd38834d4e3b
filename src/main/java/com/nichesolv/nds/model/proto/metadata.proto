syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "MetadataProto";

// This class represents the metadata associated with each payload.
message Metadata {

  // Imei is the main identifier of the vehicle. This field is required.
  uint64 imei = 1;

  // Correlation id is a unique identifier given to each payload.
  string correlation_id = 2;

  // This fields tells if the data is old or new.
  optional string magic = 3;

  // Has no use.
  optional sint32 sqn = 4;

  // Has no use.
  optional int32 crc16 = 5;

}