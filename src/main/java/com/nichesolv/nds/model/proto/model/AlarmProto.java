// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/alarm_type.proto

package com.nichesolv.nds.model.proto.model;

public final class AlarmProto {
  private AlarmProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code Alarm}
   */
  public enum Alarm
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>CELL_UNDER_VOLTAGE_ALARM = 0;</code>
     */
    CELL_UNDER_VOLTAGE_ALARM(0),
    /**
     * <code>CELL_OVER_VOLTAGE_ALARM = 1;</code>
     */
    CELL_OVER_VOLTAGE_ALARM(1),
    /**
     * <code>PACK_UNDER_VOLTAGE_ALARM = 2;</code>
     */
    PACK_UNDER_VOLTAGE_ALARM(2),
    /**
     * <code>PACK_OVER_VOLTAGE_ALARM = 3;</code>
     */
    PACK_OVER_VOLTAGE_ALARM(3),
    /**
     * <code>CELL_UNDER_TEMPERATURE_ALARM = 4;</code>
     */
    CELL_UNDER_TEMPERATURE_ALARM(4),
    /**
     * <code>CELL_OVER_TEMPERATURE_ALARM = 5;</code>
     */
    CELL_OVER_TEMPERATURE_ALARM(5),
    /**
     * <code>AMBIENT_UNDER_TEMPERATURE_ALARM = 6;</code>
     */
    AMBIENT_UNDER_TEMPERATURE_ALARM(6),
    /**
     * <code>AMBIENT_OVER_TEMPERATURE_ALARM = 7;</code>
     */
    AMBIENT_OVER_TEMPERATURE_ALARM(7),
    /**
     * <code>MOSFET_UNDER_TEMPERATURE_ALARM = 8;</code>
     */
    MOSFET_UNDER_TEMPERATURE_ALARM(8),
    /**
     * <code>MOSFET_OVER_TEMPERATURE_ALARM = 9;</code>
     */
    MOSFET_OVER_TEMPERATURE_ALARM(9),
    /**
     * <code>BUZZER_OR_LED_ALARM = 10;</code>
     */
    BUZZER_OR_LED_ALARM(10),
    /**
     * <code>CGH_OVER_CURRENT_ALARM = 11;</code>
     */
    CGH_OVER_CURRENT_ALARM(11),
    /**
     * <code>DSG_OVER_CURRENT_ALARM = 12;</code>
     */
    DSG_OVER_CURRENT_ALARM(12),
    /**
     * <code>NO_ALARM = -1;</code>
     */
    NO_ALARM(-1),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>CELL_UNDER_VOLTAGE_ALARM = 0;</code>
     */
    public static final int CELL_UNDER_VOLTAGE_ALARM_VALUE = 0;
    /**
     * <code>CELL_OVER_VOLTAGE_ALARM = 1;</code>
     */
    public static final int CELL_OVER_VOLTAGE_ALARM_VALUE = 1;
    /**
     * <code>PACK_UNDER_VOLTAGE_ALARM = 2;</code>
     */
    public static final int PACK_UNDER_VOLTAGE_ALARM_VALUE = 2;
    /**
     * <code>PACK_OVER_VOLTAGE_ALARM = 3;</code>
     */
    public static final int PACK_OVER_VOLTAGE_ALARM_VALUE = 3;
    /**
     * <code>CELL_UNDER_TEMPERATURE_ALARM = 4;</code>
     */
    public static final int CELL_UNDER_TEMPERATURE_ALARM_VALUE = 4;
    /**
     * <code>CELL_OVER_TEMPERATURE_ALARM = 5;</code>
     */
    public static final int CELL_OVER_TEMPERATURE_ALARM_VALUE = 5;
    /**
     * <code>AMBIENT_UNDER_TEMPERATURE_ALARM = 6;</code>
     */
    public static final int AMBIENT_UNDER_TEMPERATURE_ALARM_VALUE = 6;
    /**
     * <code>AMBIENT_OVER_TEMPERATURE_ALARM = 7;</code>
     */
    public static final int AMBIENT_OVER_TEMPERATURE_ALARM_VALUE = 7;
    /**
     * <code>MOSFET_UNDER_TEMPERATURE_ALARM = 8;</code>
     */
    public static final int MOSFET_UNDER_TEMPERATURE_ALARM_VALUE = 8;
    /**
     * <code>MOSFET_OVER_TEMPERATURE_ALARM = 9;</code>
     */
    public static final int MOSFET_OVER_TEMPERATURE_ALARM_VALUE = 9;
    /**
     * <code>BUZZER_OR_LED_ALARM = 10;</code>
     */
    public static final int BUZZER_OR_LED_ALARM_VALUE = 10;
    /**
     * <code>CGH_OVER_CURRENT_ALARM = 11;</code>
     */
    public static final int CGH_OVER_CURRENT_ALARM_VALUE = 11;
    /**
     * <code>DSG_OVER_CURRENT_ALARM = 12;</code>
     */
    public static final int DSG_OVER_CURRENT_ALARM_VALUE = 12;
    /**
     * <code>NO_ALARM = -1;</code>
     */
    public static final int NO_ALARM_VALUE = -1;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @Deprecated
    public static Alarm valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static Alarm forNumber(int value) {
      switch (value) {
        case 0: return CELL_UNDER_VOLTAGE_ALARM;
        case 1: return CELL_OVER_VOLTAGE_ALARM;
        case 2: return PACK_UNDER_VOLTAGE_ALARM;
        case 3: return PACK_OVER_VOLTAGE_ALARM;
        case 4: return CELL_UNDER_TEMPERATURE_ALARM;
        case 5: return CELL_OVER_TEMPERATURE_ALARM;
        case 6: return AMBIENT_UNDER_TEMPERATURE_ALARM;
        case 7: return AMBIENT_OVER_TEMPERATURE_ALARM;
        case 8: return MOSFET_UNDER_TEMPERATURE_ALARM;
        case 9: return MOSFET_OVER_TEMPERATURE_ALARM;
        case 10: return BUZZER_OR_LED_ALARM;
        case 11: return CGH_OVER_CURRENT_ALARM;
        case 12: return DSG_OVER_CURRENT_ALARM;
        case -1: return NO_ALARM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<Alarm>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        Alarm> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<Alarm>() {
            public Alarm findValueByNumber(int number) {
              return Alarm.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return AlarmProto.getDescriptor().getEnumTypes().get(0);
    }

    private static final Alarm[] VALUES = values();

    public static Alarm valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private Alarm(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:Alarm)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n)com/nichesolv/nds/model/proto/alarm.pr" +
      "oto*\270\003\n\005Alarm\022\034\n\030CELL_UNDER_VOLTAGE_ALAR" +
      "M\020\000\022\033\n\027CELL_OVER_VOLTAGE_ALARM\020\001\022\034\n\030PACK" +
      "_UNDER_VOLTAGE_ALARM\020\002\022\033\n\027PACK_OVER_VOLT" +
      "AGE_ALARM\020\003\022 \n\034CELL_UNDER_TEMPERATURE_AL" +
      "ARM\020\004\022\037\n\033CELL_OVER_TEMPERATURE_ALARM\020\005\022#" +
      "\n\037AMBIENT_UNDER_TEMPERATURE_ALARM\020\006\022\"\n\036A" +
      "MBIENT_OVER_TEMPERATURE_ALARM\020\007\022\"\n\036MOSFE" +
      "T_UNDER_TEMPERATURE_ALARM\020\010\022!\n\035MOSFET_OV" +
      "ER_TEMPERATURE_ALARM\020\t\022\027\n\023BUZZER_OR_LED_" +
      "ALARM\020\n\022\032\n\026CGH_OVER_CURRENT_ALARM\020\013\022\032\n\026D" +
      "SG_OVER_CURRENT_ALARM\020\014\022\025\n\010NO_ALARM\020\377\377\377\377" +
      "\377\377\377\377\377\001B1\n#com.nichesolv.nds.model.proto." +
      "modelB\nAlarmProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
