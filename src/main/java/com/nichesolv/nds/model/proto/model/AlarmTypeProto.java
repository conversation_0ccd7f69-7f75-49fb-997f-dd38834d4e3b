// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/alarm_type.proto

package com.nichesolv.nds.model.proto.model;

public final class AlarmTypeProto {
  private AlarmTypeProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code AlarmType}
   */
  public enum AlarmType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>CELL_UNDER_VOLTAGE_ALARM = 0;</code>
     */
    CELL_UNDER_VOLTAGE_ALARM(0),
    /**
     * <code>CELL_OVER_VOLTAGE_ALARM = 1;</code>
     */
    CELL_OVER_VOLTAGE_ALARM(1),
    /**
     * <code>PACK_UNDER_VOLTAGE_ALARM = 2;</code>
     */
    PACK_UNDER_VOLTAGE_ALARM(2),
    /**
     * <code>PACK_OVER_VOLTAGE_ALARM = 3;</code>
     */
    PACK_OVER_VOLTAGE_ALARM(3),
    /**
     * <code>CELL_UNDER_TEMPERATURE_ALARM = 4;</code>
     */
    CELL_UNDER_TEMPERATURE_ALARM(4),
    /**
     * <code>CELL_OVER_TEMPERATURE_ALARM = 5;</code>
     */
    CELL_OVER_TEMPERATURE_ALARM(5),
    /**
     * <code>AMBIENT_UNDER_TEMPERATURE_ALARM = 6;</code>
     */
    AMBIENT_UNDER_TEMPERATURE_ALARM(6),
    /**
     * <code>AMBIENT_OVER_TEMPERATURE_ALARM = 7;</code>
     */
    AMBIENT_OVER_TEMPERATURE_ALARM(7),
    /**
     * <code>MOSFET_UNDER_TEMPERATURE_ALARM = 8;</code>
     */
    MOSFET_UNDER_TEMPERATURE_ALARM(8),
    /**
     * <code>MOSFET_OVER_TEMPERATURE_ALARM = 9;</code>
     */
    MOSFET_OVER_TEMPERATURE_ALARM(9),
    /**
     * <code>BUZZER_OR_LED_ALARM = 10;</code>
     */
    BUZZER_OR_LED_ALARM(10),
    /**
     * <code>CGH_OVER_CURRENT_ALARM = 11;</code>
     */
    CGH_OVER_CURRENT_ALARM(11),
    /**
     * <code>DSG_OVER_CURRENT_ALARM = 12;</code>
     */
    DSG_OVER_CURRENT_ALARM(12),
    /**
     * <code>NO_ALARM = -1;</code>
     */
    NO_ALARM(-1),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>CELL_UNDER_VOLTAGE_ALARM = 0;</code>
     */
    public static final int CELL_UNDER_VOLTAGE_ALARM_VALUE = 0;
    /**
     * <code>CELL_OVER_VOLTAGE_ALARM = 1;</code>
     */
    public static final int CELL_OVER_VOLTAGE_ALARM_VALUE = 1;
    /**
     * <code>PACK_UNDER_VOLTAGE_ALARM = 2;</code>
     */
    public static final int PACK_UNDER_VOLTAGE_ALARM_VALUE = 2;
    /**
     * <code>PACK_OVER_VOLTAGE_ALARM = 3;</code>
     */
    public static final int PACK_OVER_VOLTAGE_ALARM_VALUE = 3;
    /**
     * <code>CELL_UNDER_TEMPERATURE_ALARM = 4;</code>
     */
    public static final int CELL_UNDER_TEMPERATURE_ALARM_VALUE = 4;
    /**
     * <code>CELL_OVER_TEMPERATURE_ALARM = 5;</code>
     */
    public static final int CELL_OVER_TEMPERATURE_ALARM_VALUE = 5;
    /**
     * <code>AMBIENT_UNDER_TEMPERATURE_ALARM = 6;</code>
     */
    public static final int AMBIENT_UNDER_TEMPERATURE_ALARM_VALUE = 6;
    /**
     * <code>AMBIENT_OVER_TEMPERATURE_ALARM = 7;</code>
     */
    public static final int AMBIENT_OVER_TEMPERATURE_ALARM_VALUE = 7;
    /**
     * <code>MOSFET_UNDER_TEMPERATURE_ALARM = 8;</code>
     */
    public static final int MOSFET_UNDER_TEMPERATURE_ALARM_VALUE = 8;
    /**
     * <code>MOSFET_OVER_TEMPERATURE_ALARM = 9;</code>
     */
    public static final int MOSFET_OVER_TEMPERATURE_ALARM_VALUE = 9;
    /**
     * <code>BUZZER_OR_LED_ALARM = 10;</code>
     */
    public static final int BUZZER_OR_LED_ALARM_VALUE = 10;
    /**
     * <code>CGH_OVER_CURRENT_ALARM = 11;</code>
     */
    public static final int CGH_OVER_CURRENT_ALARM_VALUE = 11;
    /**
     * <code>DSG_OVER_CURRENT_ALARM = 12;</code>
     */
    public static final int DSG_OVER_CURRENT_ALARM_VALUE = 12;
    /**
     * <code>NO_ALARM = -1;</code>
     */
    public static final int NO_ALARM_VALUE = -1;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static AlarmType valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static AlarmType forNumber(int value) {
      switch (value) {
        case 0: return CELL_UNDER_VOLTAGE_ALARM;
        case 1: return CELL_OVER_VOLTAGE_ALARM;
        case 2: return PACK_UNDER_VOLTAGE_ALARM;
        case 3: return PACK_OVER_VOLTAGE_ALARM;
        case 4: return CELL_UNDER_TEMPERATURE_ALARM;
        case 5: return CELL_OVER_TEMPERATURE_ALARM;
        case 6: return AMBIENT_UNDER_TEMPERATURE_ALARM;
        case 7: return AMBIENT_OVER_TEMPERATURE_ALARM;
        case 8: return MOSFET_UNDER_TEMPERATURE_ALARM;
        case 9: return MOSFET_OVER_TEMPERATURE_ALARM;
        case 10: return BUZZER_OR_LED_ALARM;
        case 11: return CGH_OVER_CURRENT_ALARM;
        case 12: return DSG_OVER_CURRENT_ALARM;
        case -1: return NO_ALARM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<AlarmType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        AlarmType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<AlarmType>() {
            public AlarmType findValueByNumber(int number) {
              return AlarmType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.AlarmTypeProto.getDescriptor().getEnumTypes().get(0);
    }

    private static final AlarmType[] VALUES = values();

    public static AlarmType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private AlarmType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:AlarmType)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.com/nichesolv/nds/model/proto/alarm_ty" +
      "pe.proto*\274\003\n\tAlarmType\022\034\n\030CELL_UNDER_VOL" +
      "TAGE_ALARM\020\000\022\033\n\027CELL_OVER_VOLTAGE_ALARM\020" +
      "\001\022\034\n\030PACK_UNDER_VOLTAGE_ALARM\020\002\022\033\n\027PACK_" +
      "OVER_VOLTAGE_ALARM\020\003\022 \n\034CELL_UNDER_TEMPE" +
      "RATURE_ALARM\020\004\022\037\n\033CELL_OVER_TEMPERATURE_" +
      "ALARM\020\005\022#\n\037AMBIENT_UNDER_TEMPERATURE_ALA" +
      "RM\020\006\022\"\n\036AMBIENT_OVER_TEMPERATURE_ALARM\020\007" +
      "\022\"\n\036MOSFET_UNDER_TEMPERATURE_ALARM\020\010\022!\n\035" +
      "MOSFET_OVER_TEMPERATURE_ALARM\020\t\022\027\n\023BUZZE" +
      "R_OR_LED_ALARM\020\n\022\032\n\026CGH_OVER_CURRENT_ALA" +
      "RM\020\013\022\032\n\026DSG_OVER_CURRENT_ALARM\020\014\022\025\n\010NO_A" +
      "LARM\020\377\377\377\377\377\377\377\377\377\001B5\n#com.nichesolv.nds.mod" +
      "el.proto.modelB\016AlarmTypeProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
