// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/analog_input.proto

package com.nichesolv.nds.model.proto.model;

public final class AnalogInputProto {
  private AnalogInputProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface AnalogInputOrBuilder extends
      // @@protoc_insertion_point(interface_extends:AnalogInput)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint32 temp = 1;</code>
     * @return Whether the temp field is set.
     */
    boolean hasTemp();
    /**
     * <code>optional uint32 temp = 1;</code>
     * @return The temp.
     */
    int getTemp();

    /**
     * <code>optional sint32 vin = 2;</code>
     * @return Whether the vin field is set.
     */
    boolean hasVin();
    /**
     * <code>optional sint32 vin = 2;</code>
     * @return The vin.
     */
    int getVin();

    /**
     * <code>optional sint32 vsys = 3;</code>
     * @return Whether the vsys field is set.
     */
    boolean hasVsys();
    /**
     * <code>optional sint32 vsys = 3;</code>
     * @return The vsys.
     */
    int getVsys();

    /**
     * <code>optional sint32 vbuck = 5;</code>
     * @return Whether the vbuck field is set.
     */
    boolean hasVbuck();
    /**
     * <code>optional sint32 vbuck = 5;</code>
     * @return The vbuck.
     */
    int getVbuck();

    /**
     * <code>optional sint32 vusr_1 = 7;</code>
     * @return Whether the vusr1 field is set.
     */
    boolean hasVusr1();
    /**
     * <code>optional sint32 vusr_1 = 7;</code>
     * @return The vusr1.
     */
    int getVusr1();

    /**
     * <code>optional sint32 vusr_2 = 9;</code>
     * @return Whether the vusr2 field is set.
     */
    boolean hasVusr2();
    /**
     * <code>optional sint32 vusr_2 = 9;</code>
     * @return The vusr2.
     */
    int getVusr2();

    /**
     * <code>optional sint32 lean_angle = 11;</code>
     * @return Whether the leanAngle field is set.
     */
    boolean hasLeanAngle();
    /**
     * <code>optional sint32 lean_angle = 11;</code>
     * @return The leanAngle.
     */
    int getLeanAngle();

    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 12;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 12;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 12;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 13;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 13;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 13;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();
  }
  /**
   * <pre>
   * Represents the analog input message.
   * </pre>
   *
   * Protobuf type {@code AnalogInput}
   */
  public static final class AnalogInput extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:AnalogInput)
      AnalogInputOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AnalogInput.newBuilder() to construct.
    private AnalogInput(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AnalogInput() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AnalogInput();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.AnalogInputProto.internal_static_AnalogInput_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.AnalogInputProto.internal_static_AnalogInput_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput.class, com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput.Builder.class);
    }

    private int bitField0_;
    public static final int TEMP_FIELD_NUMBER = 1;
    private int temp_ = 0;
    /**
     * <code>optional uint32 temp = 1;</code>
     * @return Whether the temp field is set.
     */
    @java.lang.Override
    public boolean hasTemp() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint32 temp = 1;</code>
     * @return The temp.
     */
    @java.lang.Override
    public int getTemp() {
      return temp_;
    }

    public static final int VIN_FIELD_NUMBER = 2;
    private int vin_ = 0;
    /**
     * <code>optional sint32 vin = 2;</code>
     * @return Whether the vin field is set.
     */
    @java.lang.Override
    public boolean hasVin() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional sint32 vin = 2;</code>
     * @return The vin.
     */
    @java.lang.Override
    public int getVin() {
      return vin_;
    }

    public static final int VSYS_FIELD_NUMBER = 3;
    private int vsys_ = 0;
    /**
     * <code>optional sint32 vsys = 3;</code>
     * @return Whether the vsys field is set.
     */
    @java.lang.Override
    public boolean hasVsys() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional sint32 vsys = 3;</code>
     * @return The vsys.
     */
    @java.lang.Override
    public int getVsys() {
      return vsys_;
    }

    public static final int VBUCK_FIELD_NUMBER = 5;
    private int vbuck_ = 0;
    /**
     * <code>optional sint32 vbuck = 5;</code>
     * @return Whether the vbuck field is set.
     */
    @java.lang.Override
    public boolean hasVbuck() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional sint32 vbuck = 5;</code>
     * @return The vbuck.
     */
    @java.lang.Override
    public int getVbuck() {
      return vbuck_;
    }

    public static final int VUSR_1_FIELD_NUMBER = 7;
    private int vusr1_ = 0;
    /**
     * <code>optional sint32 vusr_1 = 7;</code>
     * @return Whether the vusr1 field is set.
     */
    @java.lang.Override
    public boolean hasVusr1() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional sint32 vusr_1 = 7;</code>
     * @return The vusr1.
     */
    @java.lang.Override
    public int getVusr1() {
      return vusr1_;
    }

    public static final int VUSR_2_FIELD_NUMBER = 9;
    private int vusr2_ = 0;
    /**
     * <code>optional sint32 vusr_2 = 9;</code>
     * @return Whether the vusr2 field is set.
     */
    @java.lang.Override
    public boolean hasVusr2() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional sint32 vusr_2 = 9;</code>
     * @return The vusr2.
     */
    @java.lang.Override
    public int getVusr2() {
      return vusr2_;
    }

    public static final int LEAN_ANGLE_FIELD_NUMBER = 11;
    private int leanAngle_ = 0;
    /**
     * <code>optional sint32 lean_angle = 11;</code>
     * @return Whether the leanAngle field is set.
     */
    @java.lang.Override
    public boolean hasLeanAngle() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional sint32 lean_angle = 11;</code>
     * @return The leanAngle.
     */
    @java.lang.Override
    public int getLeanAngle() {
      return leanAngle_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 12;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 12;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 12;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 12;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }

    public static final int METADATA_FIELD_NUMBER = 13;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 13;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 13;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 13;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(1, temp_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeSInt32(2, vin_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeSInt32(3, vsys_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeSInt32(5, vbuck_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeSInt32(7, vusr1_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeSInt32(9, vusr2_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeSInt32(11, leanAngle_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeMessage(12, getTimestamp());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeMessage(13, getMetadata());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, temp_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(2, vin_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(3, vsys_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(5, vbuck_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(7, vusr1_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(9, vusr2_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(11, leanAngle_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, getTimestamp());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, getMetadata());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput other = (com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput) obj;

      if (hasTemp() != other.hasTemp()) return false;
      if (hasTemp()) {
        if (getTemp()
            != other.getTemp()) return false;
      }
      if (hasVin() != other.hasVin()) return false;
      if (hasVin()) {
        if (getVin()
            != other.getVin()) return false;
      }
      if (hasVsys() != other.hasVsys()) return false;
      if (hasVsys()) {
        if (getVsys()
            != other.getVsys()) return false;
      }
      if (hasVbuck() != other.hasVbuck()) return false;
      if (hasVbuck()) {
        if (getVbuck()
            != other.getVbuck()) return false;
      }
      if (hasVusr1() != other.hasVusr1()) return false;
      if (hasVusr1()) {
        if (getVusr1()
            != other.getVusr1()) return false;
      }
      if (hasVusr2() != other.hasVusr2()) return false;
      if (hasVusr2()) {
        if (getVusr2()
            != other.getVusr2()) return false;
      }
      if (hasLeanAngle() != other.hasLeanAngle()) return false;
      if (hasLeanAngle()) {
        if (getLeanAngle()
            != other.getLeanAngle()) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTemp()) {
        hash = (37 * hash) + TEMP_FIELD_NUMBER;
        hash = (53 * hash) + getTemp();
      }
      if (hasVin()) {
        hash = (37 * hash) + VIN_FIELD_NUMBER;
        hash = (53 * hash) + getVin();
      }
      if (hasVsys()) {
        hash = (37 * hash) + VSYS_FIELD_NUMBER;
        hash = (53 * hash) + getVsys();
      }
      if (hasVbuck()) {
        hash = (37 * hash) + VBUCK_FIELD_NUMBER;
        hash = (53 * hash) + getVbuck();
      }
      if (hasVusr1()) {
        hash = (37 * hash) + VUSR_1_FIELD_NUMBER;
        hash = (53 * hash) + getVusr1();
      }
      if (hasVusr2()) {
        hash = (37 * hash) + VUSR_2_FIELD_NUMBER;
        hash = (53 * hash) + getVusr2();
      }
      if (hasLeanAngle()) {
        hash = (37 * hash) + LEAN_ANGLE_FIELD_NUMBER;
        hash = (53 * hash) + getLeanAngle();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Represents the analog input message.
     * </pre>
     *
     * Protobuf type {@code AnalogInput}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:AnalogInput)
        com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInputOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.AnalogInputProto.internal_static_AnalogInput_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.AnalogInputProto.internal_static_AnalogInput_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput.class, com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTimestampFieldBuilder();
          getMetadataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        temp_ = 0;
        vin_ = 0;
        vsys_ = 0;
        vbuck_ = 0;
        vusr1_ = 0;
        vusr2_ = 0;
        leanAngle_ = 0;
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.AnalogInputProto.internal_static_AnalogInput_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput build() {
        com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput buildPartial() {
        com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput result = new com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.temp_ = temp_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.vin_ = vin_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.vsys_ = vsys_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.vbuck_ = vbuck_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.vusr1_ = vusr1_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.vusr2_ = vusr2_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.leanAngle_ = leanAngle_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.timestamp_ = timestampBuilder_ == null
              ? timestamp_
              : timestampBuilder_.build();
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.metadata_ = metadataBuilder_ == null
              ? metadata_
              : metadataBuilder_.build();
          to_bitField0_ |= 0x00000100;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput other) {
        if (other == com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput.getDefaultInstance()) return this;
        if (other.hasTemp()) {
          setTemp(other.getTemp());
        }
        if (other.hasVin()) {
          setVin(other.getVin());
        }
        if (other.hasVsys()) {
          setVsys(other.getVsys());
        }
        if (other.hasVbuck()) {
          setVbuck(other.getVbuck());
        }
        if (other.hasVusr1()) {
          setVusr1(other.getVusr1());
        }
        if (other.hasVusr2()) {
          setVusr2(other.getVusr2());
        }
        if (other.hasLeanAngle()) {
          setLeanAngle(other.getLeanAngle());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                temp_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                vin_ = input.readSInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                vsys_ = input.readSInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 40: {
                vbuck_ = input.readSInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 40
              case 56: {
                vusr1_ = input.readSInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 56
              case 72: {
                vusr2_ = input.readSInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 72
              case 88: {
                leanAngle_ = input.readSInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 88
              case 98: {
                input.readMessage(
                    getTimestampFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000080;
                break;
              } // case 98
              case 106: {
                input.readMessage(
                    getMetadataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000100;
                break;
              } // case 106
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int temp_ ;
      /**
       * <code>optional uint32 temp = 1;</code>
       * @return Whether the temp field is set.
       */
      @java.lang.Override
      public boolean hasTemp() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint32 temp = 1;</code>
       * @return The temp.
       */
      @java.lang.Override
      public int getTemp() {
        return temp_;
      }
      /**
       * <code>optional uint32 temp = 1;</code>
       * @param value The temp to set.
       * @return This builder for chaining.
       */
      public Builder setTemp(int value) {

        temp_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 temp = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        temp_ = 0;
        onChanged();
        return this;
      }

      private int vin_ ;
      /**
       * <code>optional sint32 vin = 2;</code>
       * @return Whether the vin field is set.
       */
      @java.lang.Override
      public boolean hasVin() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional sint32 vin = 2;</code>
       * @return The vin.
       */
      @java.lang.Override
      public int getVin() {
        return vin_;
      }
      /**
       * <code>optional sint32 vin = 2;</code>
       * @param value The vin to set.
       * @return This builder for chaining.
       */
      public Builder setVin(int value) {

        vin_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 vin = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearVin() {
        bitField0_ = (bitField0_ & ~0x00000002);
        vin_ = 0;
        onChanged();
        return this;
      }

      private int vsys_ ;
      /**
       * <code>optional sint32 vsys = 3;</code>
       * @return Whether the vsys field is set.
       */
      @java.lang.Override
      public boolean hasVsys() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional sint32 vsys = 3;</code>
       * @return The vsys.
       */
      @java.lang.Override
      public int getVsys() {
        return vsys_;
      }
      /**
       * <code>optional sint32 vsys = 3;</code>
       * @param value The vsys to set.
       * @return This builder for chaining.
       */
      public Builder setVsys(int value) {

        vsys_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 vsys = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearVsys() {
        bitField0_ = (bitField0_ & ~0x00000004);
        vsys_ = 0;
        onChanged();
        return this;
      }

      private int vbuck_ ;
      /**
       * <code>optional sint32 vbuck = 5;</code>
       * @return Whether the vbuck field is set.
       */
      @java.lang.Override
      public boolean hasVbuck() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional sint32 vbuck = 5;</code>
       * @return The vbuck.
       */
      @java.lang.Override
      public int getVbuck() {
        return vbuck_;
      }
      /**
       * <code>optional sint32 vbuck = 5;</code>
       * @param value The vbuck to set.
       * @return This builder for chaining.
       */
      public Builder setVbuck(int value) {

        vbuck_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 vbuck = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearVbuck() {
        bitField0_ = (bitField0_ & ~0x00000008);
        vbuck_ = 0;
        onChanged();
        return this;
      }

      private int vusr1_ ;
      /**
       * <code>optional sint32 vusr_1 = 7;</code>
       * @return Whether the vusr1 field is set.
       */
      @java.lang.Override
      public boolean hasVusr1() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional sint32 vusr_1 = 7;</code>
       * @return The vusr1.
       */
      @java.lang.Override
      public int getVusr1() {
        return vusr1_;
      }
      /**
       * <code>optional sint32 vusr_1 = 7;</code>
       * @param value The vusr1 to set.
       * @return This builder for chaining.
       */
      public Builder setVusr1(int value) {

        vusr1_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 vusr_1 = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearVusr1() {
        bitField0_ = (bitField0_ & ~0x00000010);
        vusr1_ = 0;
        onChanged();
        return this;
      }

      private int vusr2_ ;
      /**
       * <code>optional sint32 vusr_2 = 9;</code>
       * @return Whether the vusr2 field is set.
       */
      @java.lang.Override
      public boolean hasVusr2() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional sint32 vusr_2 = 9;</code>
       * @return The vusr2.
       */
      @java.lang.Override
      public int getVusr2() {
        return vusr2_;
      }
      /**
       * <code>optional sint32 vusr_2 = 9;</code>
       * @param value The vusr2 to set.
       * @return This builder for chaining.
       */
      public Builder setVusr2(int value) {

        vusr2_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 vusr_2 = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearVusr2() {
        bitField0_ = (bitField0_ & ~0x00000020);
        vusr2_ = 0;
        onChanged();
        return this;
      }

      private int leanAngle_ ;
      /**
       * <code>optional sint32 lean_angle = 11;</code>
       * @return Whether the leanAngle field is set.
       */
      @java.lang.Override
      public boolean hasLeanAngle() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional sint32 lean_angle = 11;</code>
       * @return The leanAngle.
       */
      @java.lang.Override
      public int getLeanAngle() {
        return leanAngle_;
      }
      /**
       * <code>optional sint32 lean_angle = 11;</code>
       * @param value The leanAngle to set.
       * @return This builder for chaining.
       */
      public Builder setLeanAngle(int value) {

        leanAngle_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 lean_angle = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearLeanAngle() {
        bitField0_ = (bitField0_ & ~0x00000040);
        leanAngle_ = 0;
        onChanged();
        return this;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 12;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 12;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 12;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
        } else {
          timestampBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 12;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 12;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0) &&
            timestamp_ != null &&
            timestamp_ != com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance()) {
            getTimestampBuilder().mergeFrom(value);
          } else {
            timestamp_ = value;
          }
        } else {
          timestampBuilder_.mergeFrom(value);
        }
        if (timestamp_ != null) {
          bitField0_ |= 0x00000080;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 12;</code>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000080);
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 12;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        bitField0_ |= 0x00000080;
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 12;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 13;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 13;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 13;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
        } else {
          metadataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 13;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 13;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0) &&
            metadata_ != null &&
            metadata_ != com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance()) {
            getMetadataBuilder().mergeFrom(value);
          } else {
            metadata_ = value;
          }
        } else {
          metadataBuilder_.mergeFrom(value);
        }
        if (metadata_ != null) {
          bitField0_ |= 0x00000100;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 13;</code>
       */
      public Builder clearMetadata() {
        bitField0_ = (bitField0_ & ~0x00000100);
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 13;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        bitField0_ |= 0x00000100;
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 13;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:AnalogInput)
    }

    // @@protoc_insertion_point(class_scope:AnalogInput)
    private static final com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput();
    }

    public static com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<AnalogInput>
        PARSER = new com.google.protobuf.AbstractParser<AnalogInput>() {
      @java.lang.Override
      public AnalogInput parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<AnalogInput> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AnalogInput> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.AnalogInputProto.AnalogInput getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_AnalogInput_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_AnalogInput_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n0com/nichesolv/nds/model/proto/analog_i" +
      "nput.proto\032-com/nichesolv/nds/model/prot" +
      "o/timestamp.proto\032,com/nichesolv/nds/mod" +
      "el/proto/metadata.proto\"\241\002\n\013AnalogInput\022" +
      "\021\n\004temp\030\001 \001(\rH\000\210\001\001\022\020\n\003vin\030\002 \001(\021H\001\210\001\001\022\021\n\004" +
      "vsys\030\003 \001(\021H\002\210\001\001\022\022\n\005vbuck\030\005 \001(\021H\003\210\001\001\022\023\n\006v" +
      "usr_1\030\007 \001(\021H\004\210\001\001\022\023\n\006vusr_2\030\t \001(\021H\005\210\001\001\022\027\n" +
      "\nlean_angle\030\013 \001(\021H\006\210\001\001\022\035\n\ttimestamp\030\014 \001(" +
      "\0132\n.Timestamp\022\033\n\010metadata\030\r \001(\0132\t.Metada" +
      "taB\007\n\005_tempB\006\n\004_vinB\007\n\005_vsysB\010\n\006_vbuckB\t" +
      "\n\007_vusr_1B\t\n\007_vusr_2B\r\n\013_lean_angleB7\n#c" +
      "om.nichesolv.nds.model.proto.modelB\020Anal" +
      "ogInputProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor(),
        });
    internal_static_AnalogInput_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_AnalogInput_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_AnalogInput_descriptor,
        new java.lang.String[] { "Temp", "Vin", "Vsys", "Vbuck", "Vusr1", "Vusr2", "LeanAngle", "Timestamp", "Metadata", "Temp", "Vin", "Vsys", "Vbuck", "Vusr1", "Vusr2", "LeanAngle", });
    com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
