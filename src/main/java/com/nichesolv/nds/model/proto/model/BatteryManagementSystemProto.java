// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/bms.proto

package com.nichesolv.nds.model.proto.model;

public final class BatteryManagementSystemProto {
  private BatteryManagementSystemProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code Chemistry}
   */
  public enum Chemistry
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>NCM = 0;</code>
     */
    NCM(0),
    /**
     * <code>LFP = 1;</code>
     */
    LFP(1),
    /**
     * <code>NO_CHEMISTRY = 9999;</code>
     */
    NO_CHEMISTRY(9999),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>NCM = 0;</code>
     */
    public static final int NCM_VALUE = 0;
    /**
     * <code>LFP = 1;</code>
     */
    public static final int LFP_VALUE = 1;
    /**
     * <code>NO_CHEMISTRY = 9999;</code>
     */
    public static final int NO_CHEMISTRY_VALUE = 9999;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static Chemistry valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static Chemistry forNumber(int value) {
      switch (value) {
        case 0: return NCM;
        case 1: return LFP;
        case 9999: return NO_CHEMISTRY;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<Chemistry>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        Chemistry> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<Chemistry>() {
            public Chemistry findValueByNumber(int number) {
              return Chemistry.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.getDescriptor().getEnumTypes().get(0);
    }

    private static final Chemistry[] VALUES = values();

    public static Chemistry valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private Chemistry(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:Chemistry)
  }

  public interface BmsMetadataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BmsMetadata)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string battery_serial_number = 1;</code>
     * @return Whether the batterySerialNumber field is set.
     */
    boolean hasBatterySerialNumber();
    /**
     * <code>string battery_serial_number = 1;</code>
     * @return The batterySerialNumber.
     */
    java.lang.String getBatterySerialNumber();
    /**
     * <code>string battery_serial_number = 1;</code>
     * @return The bytes for batterySerialNumber.
     */
    com.google.protobuf.ByteString
        getBatterySerialNumberBytes();

    /**
     * <code>string bms_serial_number = 3;</code>
     * @return Whether the bmsSerialNumber field is set.
     */
    boolean hasBmsSerialNumber();
    /**
     * <code>string bms_serial_number = 3;</code>
     * @return The bmsSerialNumber.
     */
    java.lang.String getBmsSerialNumber();
    /**
     * <code>string bms_serial_number = 3;</code>
     * @return The bytes for bmsSerialNumber.
     */
    com.google.protobuf.ByteString
        getBmsSerialNumberBytes();

    /**
     * <code>string protocol_version = 5;</code>
     * @return Whether the protocolVersion field is set.
     */
    boolean hasProtocolVersion();
    /**
     * <code>string protocol_version = 5;</code>
     * @return The protocolVersion.
     */
    java.lang.String getProtocolVersion();
    /**
     * <code>string protocol_version = 5;</code>
     * @return The bytes for protocolVersion.
     */
    com.google.protobuf.ByteString
        getProtocolVersionBytes();

    /**
     * <code>sint64 rated_capacity = 7;</code>
     * @return Whether the ratedCapacity field is set.
     */
    boolean hasRatedCapacity();
    /**
     * <code>sint64 rated_capacity = 7;</code>
     * @return The ratedCapacity.
     */
    long getRatedCapacity();

    /**
     * <code>sint32 active_cell = 9;</code>
     * @return Whether the activeCell field is set.
     */
    boolean hasActiveCell();
    /**
     * <code>sint32 active_cell = 9;</code>
     * @return The activeCell.
     */
    int getActiveCell();

    /**
     * <code>sint32 active_temp = 11;</code>
     * @return Whether the activeTemp field is set.
     */
    boolean hasActiveTemp();
    /**
     * <code>sint32 active_temp = 11;</code>
     * @return The activeTemp.
     */
    int getActiveTemp();

    /**
     * <code>sint32 dod = 13;</code>
     * @return Whether the dod field is set.
     */
    boolean hasDod();
    /**
     * <code>sint32 dod = 13;</code>
     * @return The dod.
     */
    int getDod();

    /**
     * <code>.Chemistry chemistry = 15;</code>
     * @return Whether the chemistry field is set.
     */
    boolean hasChemistry();
    /**
     * <code>.Chemistry chemistry = 15;</code>
     * @return The enum numeric value on the wire for chemistry.
     */
    int getChemistryValue();
    /**
     * <code>.Chemistry chemistry = 15;</code>
     * @return The chemistry.
     */
    com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Chemistry getChemistry();

    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 16;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 16;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 16;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 17;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 17;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 17;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();
  }
  /**
   * Protobuf type {@code BmsMetadata}
   */
  public static final class BmsMetadata extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BmsMetadata)
      BmsMetadataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BmsMetadata.newBuilder() to construct.
    private BmsMetadata(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BmsMetadata() {
      batterySerialNumber_ = "";
      bmsSerialNumber_ = "";
      protocolVersion_ = "";
      chemistry_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BmsMetadata();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BmsMetadata(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              batterySerialNumber_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              bmsSerialNumber_ = s;
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              protocolVersion_ = s;
              break;
            }
            case 56: {
              bitField0_ |= 0x00000008;
              ratedCapacity_ = input.readSInt64();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000010;
              activeCell_ = input.readSInt32();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000020;
              activeTemp_ = input.readSInt32();
              break;
            }
            case 104: {
              bitField0_ |= 0x00000040;
              dod_ = input.readSInt32();
              break;
            }
            case 120: {
              int rawValue = input.readEnum();
              bitField0_ |= 0x00000080;
              chemistry_ = rawValue;
              break;
            }
            case 130: {
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder subBuilder = null;
              if (timestamp_ != null) {
                subBuilder = timestamp_.toBuilder();
              }
              timestamp_ = input.readMessage(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(timestamp_);
                timestamp_ = subBuilder.buildPartial();
              }

              break;
            }
            case 138: {
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder subBuilder = null;
              if (metadata_ != null) {
                subBuilder = metadata_.toBuilder();
              }
              metadata_ = input.readMessage(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(metadata_);
                metadata_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_BmsMetadata_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_BmsMetadata_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata.Builder.class);
    }

    private int bitField0_;
    public static final int BATTERY_SERIAL_NUMBER_FIELD_NUMBER = 1;
    private volatile java.lang.Object batterySerialNumber_;
    /**
     * <code>string battery_serial_number = 1;</code>
     * @return Whether the batterySerialNumber field is set.
     */
    @java.lang.Override
    public boolean hasBatterySerialNumber() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>string battery_serial_number = 1;</code>
     * @return The batterySerialNumber.
     */
    @java.lang.Override
    public java.lang.String getBatterySerialNumber() {
      java.lang.Object ref = batterySerialNumber_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        batterySerialNumber_ = s;
        return s;
      }
    }
    /**
     * <code>string battery_serial_number = 1;</code>
     * @return The bytes for batterySerialNumber.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBatterySerialNumberBytes() {
      java.lang.Object ref = batterySerialNumber_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        batterySerialNumber_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BMS_SERIAL_NUMBER_FIELD_NUMBER = 3;
    private volatile java.lang.Object bmsSerialNumber_;
    /**
     * <code>string bms_serial_number = 3;</code>
     * @return Whether the bmsSerialNumber field is set.
     */
    @java.lang.Override
    public boolean hasBmsSerialNumber() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>string bms_serial_number = 3;</code>
     * @return The bmsSerialNumber.
     */
    @java.lang.Override
    public java.lang.String getBmsSerialNumber() {
      java.lang.Object ref = bmsSerialNumber_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bmsSerialNumber_ = s;
        return s;
      }
    }
    /**
     * <code>string bms_serial_number = 3;</code>
     * @return The bytes for bmsSerialNumber.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBmsSerialNumberBytes() {
      java.lang.Object ref = bmsSerialNumber_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bmsSerialNumber_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTOCOL_VERSION_FIELD_NUMBER = 5;
    private volatile java.lang.Object protocolVersion_;
    /**
     * <code>string protocol_version = 5;</code>
     * @return Whether the protocolVersion field is set.
     */
    @java.lang.Override
    public boolean hasProtocolVersion() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>string protocol_version = 5;</code>
     * @return The protocolVersion.
     */
    @java.lang.Override
    public java.lang.String getProtocolVersion() {
      java.lang.Object ref = protocolVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        protocolVersion_ = s;
        return s;
      }
    }
    /**
     * <code>string protocol_version = 5;</code>
     * @return The bytes for protocolVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getProtocolVersionBytes() {
      java.lang.Object ref = protocolVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        protocolVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RATED_CAPACITY_FIELD_NUMBER = 7;
    private long ratedCapacity_;
    /**
     * <code>sint64 rated_capacity = 7;</code>
     * @return Whether the ratedCapacity field is set.
     */
    @java.lang.Override
    public boolean hasRatedCapacity() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>sint64 rated_capacity = 7;</code>
     * @return The ratedCapacity.
     */
    @java.lang.Override
    public long getRatedCapacity() {
      return ratedCapacity_;
    }

    public static final int ACTIVE_CELL_FIELD_NUMBER = 9;
    private int activeCell_;
    /**
     * <code>sint32 active_cell = 9;</code>
     * @return Whether the activeCell field is set.
     */
    @java.lang.Override
    public boolean hasActiveCell() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>sint32 active_cell = 9;</code>
     * @return The activeCell.
     */
    @java.lang.Override
    public int getActiveCell() {
      return activeCell_;
    }

    public static final int ACTIVE_TEMP_FIELD_NUMBER = 11;
    private int activeTemp_;
    /**
     * <code>sint32 active_temp = 11;</code>
     * @return Whether the activeTemp field is set.
     */
    @java.lang.Override
    public boolean hasActiveTemp() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>sint32 active_temp = 11;</code>
     * @return The activeTemp.
     */
    @java.lang.Override
    public int getActiveTemp() {
      return activeTemp_;
    }

    public static final int DOD_FIELD_NUMBER = 13;
    private int dod_;
    /**
     * <code>sint32 dod = 13;</code>
     * @return Whether the dod field is set.
     */
    @java.lang.Override
    public boolean hasDod() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>sint32 dod = 13;</code>
     * @return The dod.
     */
    @java.lang.Override
    public int getDod() {
      return dod_;
    }

    public static final int CHEMISTRY_FIELD_NUMBER = 15;
    private int chemistry_;
    /**
     * <code>.Chemistry chemistry = 15;</code>
     * @return Whether the chemistry field is set.
     */
    @java.lang.Override public boolean hasChemistry() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>.Chemistry chemistry = 15;</code>
     * @return The enum numeric value on the wire for chemistry.
     */
    @java.lang.Override public int getChemistryValue() {
      return chemistry_;
    }
    /**
     * <code>.Chemistry chemistry = 15;</code>
     * @return The chemistry.
     */
    @java.lang.Override public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Chemistry getChemistry() {
      @SuppressWarnings("deprecation")
      com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Chemistry result = com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Chemistry.valueOf(chemistry_);
      return result == null ? com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Chemistry.UNRECOGNIZED : result;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 16;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 16;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return timestamp_ != null;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 16;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 16;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return getTimestamp();
    }

    public static final int METADATA_FIELD_NUMBER = 17;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 17;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return metadata_ != null;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 17;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 17;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return getMetadata();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, batterySerialNumber_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, bmsSerialNumber_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, protocolVersion_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeSInt64(7, ratedCapacity_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeSInt32(9, activeCell_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeSInt32(11, activeTemp_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeSInt32(13, dod_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeEnum(15, chemistry_);
      }
      if (timestamp_ != null) {
        output.writeMessage(16, getTimestamp());
      }
      if (metadata_ != null) {
        output.writeMessage(17, getMetadata());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, batterySerialNumber_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, bmsSerialNumber_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, protocolVersion_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt64Size(7, ratedCapacity_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(9, activeCell_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(11, activeTemp_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(13, dod_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(15, chemistry_);
      }
      if (timestamp_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(16, getTimestamp());
      }
      if (metadata_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(17, getMetadata());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata other = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata) obj;

      if (hasBatterySerialNumber() != other.hasBatterySerialNumber()) return false;
      if (hasBatterySerialNumber()) {
        if (!getBatterySerialNumber()
            .equals(other.getBatterySerialNumber())) return false;
      }
      if (hasBmsSerialNumber() != other.hasBmsSerialNumber()) return false;
      if (hasBmsSerialNumber()) {
        if (!getBmsSerialNumber()
            .equals(other.getBmsSerialNumber())) return false;
      }
      if (hasProtocolVersion() != other.hasProtocolVersion()) return false;
      if (hasProtocolVersion()) {
        if (!getProtocolVersion()
            .equals(other.getProtocolVersion())) return false;
      }
      if (hasRatedCapacity() != other.hasRatedCapacity()) return false;
      if (hasRatedCapacity()) {
        if (getRatedCapacity()
            != other.getRatedCapacity()) return false;
      }
      if (hasActiveCell() != other.hasActiveCell()) return false;
      if (hasActiveCell()) {
        if (getActiveCell()
            != other.getActiveCell()) return false;
      }
      if (hasActiveTemp() != other.hasActiveTemp()) return false;
      if (hasActiveTemp()) {
        if (getActiveTemp()
            != other.getActiveTemp()) return false;
      }
      if (hasDod() != other.hasDod()) return false;
      if (hasDod()) {
        if (getDod()
            != other.getDod()) return false;
      }
      if (hasChemistry() != other.hasChemistry()) return false;
      if (hasChemistry()) {
        if (chemistry_ != other.chemistry_) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBatterySerialNumber()) {
        hash = (37 * hash) + BATTERY_SERIAL_NUMBER_FIELD_NUMBER;
        hash = (53 * hash) + getBatterySerialNumber().hashCode();
      }
      if (hasBmsSerialNumber()) {
        hash = (37 * hash) + BMS_SERIAL_NUMBER_FIELD_NUMBER;
        hash = (53 * hash) + getBmsSerialNumber().hashCode();
      }
      if (hasProtocolVersion()) {
        hash = (37 * hash) + PROTOCOL_VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getProtocolVersion().hashCode();
      }
      if (hasRatedCapacity()) {
        hash = (37 * hash) + RATED_CAPACITY_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRatedCapacity());
      }
      if (hasActiveCell()) {
        hash = (37 * hash) + ACTIVE_CELL_FIELD_NUMBER;
        hash = (53 * hash) + getActiveCell();
      }
      if (hasActiveTemp()) {
        hash = (37 * hash) + ACTIVE_TEMP_FIELD_NUMBER;
        hash = (53 * hash) + getActiveTemp();
      }
      if (hasDod()) {
        hash = (37 * hash) + DOD_FIELD_NUMBER;
        hash = (53 * hash) + getDod();
      }
      if (hasChemistry()) {
        hash = (37 * hash) + CHEMISTRY_FIELD_NUMBER;
        hash = (53 * hash) + chemistry_;
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code BmsMetadata}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BmsMetadata)
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_BmsMetadata_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_BmsMetadata_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        batterySerialNumber_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        bmsSerialNumber_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        protocolVersion_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        ratedCapacity_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        activeCell_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        activeTemp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        dod_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        chemistry_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        if (timestampBuilder_ == null) {
          timestamp_ = null;
        } else {
          timestamp_ = null;
          timestampBuilder_ = null;
        }
        if (metadataBuilder_ == null) {
          metadata_ = null;
        } else {
          metadata_ = null;
          metadataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_BmsMetadata_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata build() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata buildPartial() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata result = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.batterySerialNumber_ = batterySerialNumber_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.bmsSerialNumber_ = bmsSerialNumber_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.protocolVersion_ = protocolVersion_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.ratedCapacity_ = ratedCapacity_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.activeCell_ = activeCell_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.activeTemp_ = activeTemp_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.dod_ = dod_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          to_bitField0_ |= 0x00000080;
        }
        result.chemistry_ = chemistry_;
        if (timestampBuilder_ == null) {
          result.timestamp_ = timestamp_;
        } else {
          result.timestamp_ = timestampBuilder_.build();
        }
        if (metadataBuilder_ == null) {
          result.metadata_ = metadata_;
        } else {
          result.metadata_ = metadataBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata other) {
        if (other == com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata.getDefaultInstance()) return this;
        if (other.hasBatterySerialNumber()) {
          bitField0_ |= 0x00000001;
          batterySerialNumber_ = other.batterySerialNumber_;
          onChanged();
        }
        if (other.hasBmsSerialNumber()) {
          bitField0_ |= 0x00000002;
          bmsSerialNumber_ = other.bmsSerialNumber_;
          onChanged();
        }
        if (other.hasProtocolVersion()) {
          bitField0_ |= 0x00000004;
          protocolVersion_ = other.protocolVersion_;
          onChanged();
        }
        if (other.hasRatedCapacity()) {
          setRatedCapacity(other.getRatedCapacity());
        }
        if (other.hasActiveCell()) {
          setActiveCell(other.getActiveCell());
        }
        if (other.hasActiveTemp()) {
          setActiveTemp(other.getActiveTemp());
        }
        if (other.hasDod()) {
          setDod(other.getDod());
        }
        if (other.hasChemistry()) {
          setChemistry(other.getChemistry());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object batterySerialNumber_ = "";
      /**
       * <code>string battery_serial_number = 1;</code>
       * @return Whether the batterySerialNumber field is set.
       */
      public boolean hasBatterySerialNumber() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>string battery_serial_number = 1;</code>
       * @return The batterySerialNumber.
       */
      public java.lang.String getBatterySerialNumber() {
        java.lang.Object ref = batterySerialNumber_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          batterySerialNumber_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string battery_serial_number = 1;</code>
       * @return The bytes for batterySerialNumber.
       */
      public com.google.protobuf.ByteString
          getBatterySerialNumberBytes() {
        java.lang.Object ref = batterySerialNumber_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          batterySerialNumber_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string battery_serial_number = 1;</code>
       * @param value The batterySerialNumber to set.
       * @return This builder for chaining.
       */
      public Builder setBatterySerialNumber(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        batterySerialNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string battery_serial_number = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBatterySerialNumber() {
        bitField0_ = (bitField0_ & ~0x00000001);
        batterySerialNumber_ = getDefaultInstance().getBatterySerialNumber();
        onChanged();
        return this;
      }
      /**
       * <code>string battery_serial_number = 1;</code>
       * @param value The bytes for batterySerialNumber to set.
       * @return This builder for chaining.
       */
      public Builder setBatterySerialNumberBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        bitField0_ |= 0x00000001;
        batterySerialNumber_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object bmsSerialNumber_ = "";
      /**
       * <code>string bms_serial_number = 3;</code>
       * @return Whether the bmsSerialNumber field is set.
       */
      public boolean hasBmsSerialNumber() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>string bms_serial_number = 3;</code>
       * @return The bmsSerialNumber.
       */
      public java.lang.String getBmsSerialNumber() {
        java.lang.Object ref = bmsSerialNumber_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          bmsSerialNumber_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string bms_serial_number = 3;</code>
       * @return The bytes for bmsSerialNumber.
       */
      public com.google.protobuf.ByteString
          getBmsSerialNumberBytes() {
        java.lang.Object ref = bmsSerialNumber_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          bmsSerialNumber_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string bms_serial_number = 3;</code>
       * @param value The bmsSerialNumber to set.
       * @return This builder for chaining.
       */
      public Builder setBmsSerialNumber(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        bmsSerialNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string bms_serial_number = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearBmsSerialNumber() {
        bitField0_ = (bitField0_ & ~0x00000002);
        bmsSerialNumber_ = getDefaultInstance().getBmsSerialNumber();
        onChanged();
        return this;
      }
      /**
       * <code>string bms_serial_number = 3;</code>
       * @param value The bytes for bmsSerialNumber to set.
       * @return This builder for chaining.
       */
      public Builder setBmsSerialNumberBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        bitField0_ |= 0x00000002;
        bmsSerialNumber_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object protocolVersion_ = "";
      /**
       * <code>string protocol_version = 5;</code>
       * @return Whether the protocolVersion field is set.
       */
      public boolean hasProtocolVersion() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>string protocol_version = 5;</code>
       * @return The protocolVersion.
       */
      public java.lang.String getProtocolVersion() {
        java.lang.Object ref = protocolVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          protocolVersion_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string protocol_version = 5;</code>
       * @return The bytes for protocolVersion.
       */
      public com.google.protobuf.ByteString
          getProtocolVersionBytes() {
        java.lang.Object ref = protocolVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protocolVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string protocol_version = 5;</code>
       * @param value The protocolVersion to set.
       * @return This builder for chaining.
       */
      public Builder setProtocolVersion(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        protocolVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string protocol_version = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtocolVersion() {
        bitField0_ = (bitField0_ & ~0x00000004);
        protocolVersion_ = getDefaultInstance().getProtocolVersion();
        onChanged();
        return this;
      }
      /**
       * <code>string protocol_version = 5;</code>
       * @param value The bytes for protocolVersion to set.
       * @return This builder for chaining.
       */
      public Builder setProtocolVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        bitField0_ |= 0x00000004;
        protocolVersion_ = value;
        onChanged();
        return this;
      }

      private long ratedCapacity_ ;
      /**
       * <code>sint64 rated_capacity = 7;</code>
       * @return Whether the ratedCapacity field is set.
       */
      @java.lang.Override
      public boolean hasRatedCapacity() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>sint64 rated_capacity = 7;</code>
       * @return The ratedCapacity.
       */
      @java.lang.Override
      public long getRatedCapacity() {
        return ratedCapacity_;
      }
      /**
       * <code>sint64 rated_capacity = 7;</code>
       * @param value The ratedCapacity to set.
       * @return This builder for chaining.
       */
      public Builder setRatedCapacity(long value) {
        bitField0_ |= 0x00000008;
        ratedCapacity_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>sint64 rated_capacity = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearRatedCapacity() {
        bitField0_ = (bitField0_ & ~0x00000008);
        ratedCapacity_ = 0L;
        onChanged();
        return this;
      }

      private int activeCell_ ;
      /**
       * <code>sint32 active_cell = 9;</code>
       * @return Whether the activeCell field is set.
       */
      @java.lang.Override
      public boolean hasActiveCell() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>sint32 active_cell = 9;</code>
       * @return The activeCell.
       */
      @java.lang.Override
      public int getActiveCell() {
        return activeCell_;
      }
      /**
       * <code>sint32 active_cell = 9;</code>
       * @param value The activeCell to set.
       * @return This builder for chaining.
       */
      public Builder setActiveCell(int value) {
        bitField0_ |= 0x00000010;
        activeCell_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>sint32 active_cell = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearActiveCell() {
        bitField0_ = (bitField0_ & ~0x00000010);
        activeCell_ = 0;
        onChanged();
        return this;
      }

      private int activeTemp_ ;
      /**
       * <code>sint32 active_temp = 11;</code>
       * @return Whether the activeTemp field is set.
       */
      @java.lang.Override
      public boolean hasActiveTemp() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>sint32 active_temp = 11;</code>
       * @return The activeTemp.
       */
      @java.lang.Override
      public int getActiveTemp() {
        return activeTemp_;
      }
      /**
       * <code>sint32 active_temp = 11;</code>
       * @param value The activeTemp to set.
       * @return This builder for chaining.
       */
      public Builder setActiveTemp(int value) {
        bitField0_ |= 0x00000020;
        activeTemp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>sint32 active_temp = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearActiveTemp() {
        bitField0_ = (bitField0_ & ~0x00000020);
        activeTemp_ = 0;
        onChanged();
        return this;
      }

      private int dod_ ;
      /**
       * <code>sint32 dod = 13;</code>
       * @return Whether the dod field is set.
       */
      @java.lang.Override
      public boolean hasDod() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>sint32 dod = 13;</code>
       * @return The dod.
       */
      @java.lang.Override
      public int getDod() {
        return dod_;
      }
      /**
       * <code>sint32 dod = 13;</code>
       * @param value The dod to set.
       * @return This builder for chaining.
       */
      public Builder setDod(int value) {
        bitField0_ |= 0x00000040;
        dod_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>sint32 dod = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearDod() {
        bitField0_ = (bitField0_ & ~0x00000040);
        dod_ = 0;
        onChanged();
        return this;
      }

      private int chemistry_ = 0;
      /**
       * <code>.Chemistry chemistry = 15;</code>
       * @return Whether the chemistry field is set.
       */
      @java.lang.Override public boolean hasChemistry() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>.Chemistry chemistry = 15;</code>
       * @return The enum numeric value on the wire for chemistry.
       */
      @java.lang.Override public int getChemistryValue() {
        return chemistry_;
      }
      /**
       * <code>.Chemistry chemistry = 15;</code>
       * @param value The enum numeric value on the wire for chemistry to set.
       * @return This builder for chaining.
       */
      public Builder setChemistryValue(int value) {
        bitField0_ |= 0x00000080;
        chemistry_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.Chemistry chemistry = 15;</code>
       * @return The chemistry.
       */
      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Chemistry getChemistry() {
        @SuppressWarnings("deprecation")
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Chemistry result = com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Chemistry.valueOf(chemistry_);
        return result == null ? com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Chemistry.UNRECOGNIZED : result;
      }
      /**
       * <code>.Chemistry chemistry = 15;</code>
       * @param value The chemistry to set.
       * @return This builder for chaining.
       */
      public Builder setChemistry(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Chemistry value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000080;
        chemistry_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.Chemistry chemistry = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearChemistry() {
        bitField0_ = (bitField0_ & ~0x00000080);
        chemistry_ = 0;
        onChanged();
        return this;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 16;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return timestampBuilder_ != null || timestamp_ != null;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 16;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 16;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
          onChanged();
        } else {
          timestampBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 16;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
          onChanged();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 16;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (timestamp_ != null) {
            timestamp_ =
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.newBuilder(timestamp_).mergeFrom(value).buildPartial();
          } else {
            timestamp_ = value;
          }
          onChanged();
        } else {
          timestampBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 16;</code>
       */
      public Builder clearTimestamp() {
        if (timestampBuilder_ == null) {
          timestamp_ = null;
          onChanged();
        } else {
          timestamp_ = null;
          timestampBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 16;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 16;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 16;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 17;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return metadataBuilder_ != null || metadata_ != null;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 17;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 17;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
          onChanged();
        } else {
          metadataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 17;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
          onChanged();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 17;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (metadata_ != null) {
            metadata_ =
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.newBuilder(metadata_).mergeFrom(value).buildPartial();
          } else {
            metadata_ = value;
          }
          onChanged();
        } else {
          metadataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 17;</code>
       */
      public Builder clearMetadata() {
        if (metadataBuilder_ == null) {
          metadata_ = null;
          onChanged();
        } else {
          metadata_ = null;
          metadataBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 17;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 17;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 17;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BmsMetadata)
    }

    // @@protoc_insertion_point(class_scope:BmsMetadata)
    private static final com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata();
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BmsMetadata>
        PARSER = new com.google.protobuf.AbstractParser<BmsMetadata>() {
      @java.lang.Override
      public BmsMetadata parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BmsMetadata(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BmsMetadata> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BmsMetadata> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BmsMetadata getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface StatusOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Status)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>sint32 chgCycleCount = 1;</code>
     * @return Whether the chgCycleCount field is set.
     */
    boolean hasChgCycleCount();
    /**
     * <code>sint32 chgCycleCount = 1;</code>
     * @return The chgCycleCount.
     */
    int getChgCycleCount();

    /**
     * <pre>
     * DSG CYCLE COUNT
     * </pre>
     *
     * <code>sint32 dsgCycleCount = 3;</code>
     * @return Whether the dsgCycleCount field is set.
     */
    boolean hasDsgCycleCount();
    /**
     * <pre>
     * DSG CYCLE COUNT
     * </pre>
     *
     * <code>sint32 dsgCycleCount = 3;</code>
     * @return The dsgCycleCount.
     */
    int getDsgCycleCount();

    /**
     * <pre>
     * Alarms
     * </pre>
     *
     * <code>repeated .Alarm alarms = 5;</code>
     */
    java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm> 
        getAlarmsList();
    /**
     * <pre>
     * Alarms
     * </pre>
     *
     * <code>repeated .Alarm alarms = 5;</code>
     */
    com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm getAlarms(int index);
    /**
     * <pre>
     * Alarms
     * </pre>
     *
     * <code>repeated .Alarm alarms = 5;</code>
     */
    int getAlarmsCount();
    /**
     * <pre>
     * Alarms
     * </pre>
     *
     * <code>repeated .Alarm alarms = 5;</code>
     */
    java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.AlarmOrBuilder> 
        getAlarmsOrBuilderList();
    /**
     * <pre>
     * Alarms
     * </pre>
     *
     * <code>repeated .Alarm alarms = 5;</code>
     */
    com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.AlarmOrBuilder getAlarmsOrBuilder(
        int index);

    /**
     * <pre>
     * Protections
     * </pre>
     *
     * <code>repeated .Protection protections = 7;</code>
     * @return A list containing the protections.
     */
    java.util.List<com.nichesolv.nds.model.proto.model.ProtectionProto.Protection> getProtectionsList();
    /**
     * <pre>
     * Protections
     * </pre>
     *
     * <code>repeated .Protection protections = 7;</code>
     * @return The count of protections.
     */
    int getProtectionsCount();
    /**
     * <pre>
     * Protections
     * </pre>
     *
     * <code>repeated .Protection protections = 7;</code>
     * @param index The index of the element to return.
     * @return The protections at the given index.
     */
    com.nichesolv.nds.model.proto.model.ProtectionProto.Protection getProtections(int index);
    /**
     * <pre>
     * Protections
     * </pre>
     *
     * <code>repeated .Protection protections = 7;</code>
     * @return A list containing the enum numeric values on the wire for protections.
     */
    java.util.List<java.lang.Integer>
    getProtectionsValueList();
    /**
     * <pre>
     * Protections
     * </pre>
     *
     * <code>repeated .Protection protections = 7;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of protections at the given index.
     */
    int getProtectionsValue(int index);

    /**
     * <pre>
     * Cell Volt Min (uint16_t) (V)
     * </pre>
     *
     * <code>float cellVoltMin = 9;</code>
     * @return Whether the cellVoltMin field is set.
     */
    boolean hasCellVoltMin();
    /**
     * <pre>
     * Cell Volt Min (uint16_t) (V)
     * </pre>
     *
     * <code>float cellVoltMin = 9;</code>
     * @return The cellVoltMin.
     */
    float getCellVoltMin();

    /**
     * <pre>
     * Cell Volt Maximum  (V)
     * </pre>
     *
     * <code>float cellVoltMax = 11;</code>
     * @return Whether the cellVoltMax field is set.
     */
    boolean hasCellVoltMax();
    /**
     * <pre>
     * Cell Volt Maximum  (V)
     * </pre>
     *
     * <code>float cellVoltMax = 11;</code>
     * @return The cellVoltMax.
     */
    float getCellVoltMax();

    /**
     * <pre>
     * Temp minimum (in celsius)	.
     * </pre>
     *
     * <code>float temperatureMin = 13;</code>
     * @return Whether the temperatureMin field is set.
     */
    boolean hasTemperatureMin();
    /**
     * <pre>
     * Temp minimum (in celsius)	.
     * </pre>
     *
     * <code>float temperatureMin = 13;</code>
     * @return The temperatureMin.
     */
    float getTemperatureMin();

    /**
     * <pre>
     * Temp maximum  (in Celsius)
     * </pre>
     *
     * <code>float temperatureMax = 15;</code>
     * @return Whether the temperatureMax field is set.
     */
    boolean hasTemperatureMax();
    /**
     * <pre>
     * Temp maximum  (in Celsius)
     * </pre>
     *
     * <code>float temperatureMax = 15;</code>
     * @return The temperatureMax.
     */
    float getTemperatureMax();

    /**
     * <pre>
     ** Batt Volt (float) (V) 
     * </pre>
     *
     * <code>float batteryVolt = 17;</code>
     * @return Whether the batteryVolt field is set.
     */
    boolean hasBatteryVolt();
    /**
     * <pre>
     ** Batt Volt (float) (V) 
     * </pre>
     *
     * <code>float batteryVolt = 17;</code>
     * @return The batteryVolt.
     */
    float getBatteryVolt();

    /**
     * <pre>
     ** SOC  
     * </pre>
     *
     * <code>float soc = 19;</code>
     * @return Whether the soc field is set.
     */
    boolean hasSoc();
    /**
     * <pre>
     ** SOC  
     * </pre>
     *
     * <code>float soc = 19;</code>
     * @return The soc.
     */
    float getSoc();

    /**
     * <pre>
     ** SOH  
     * </pre>
     *
     * <code>float soh = 21;</code>
     * @return Whether the soh field is set.
     */
    boolean hasSoh();
    /**
     * <pre>
     ** SOH  
     * </pre>
     *
     * <code>float soh = 21;</code>
     * @return The soh.
     */
    float getSoh();

    /**
     * <pre>
     * Current (float) (A).
     * </pre>
     *
     * <code>float current = 23;</code>
     * @return Whether the current field is set.
     */
    boolean hasCurrent();
    /**
     * <pre>
     * Current (float) (A).
     * </pre>
     *
     * <code>float current = 23;</code>
     * @return The current.
     */
    float getCurrent();

    /**
     * <pre>
     * Balancing Status(uint32_t) Need to do bit wise operation.
     * </pre>
     *
     * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
     */
    java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus> 
        getBalancingStatusesList();
    /**
     * <pre>
     * Balancing Status(uint32_t) Need to do bit wise operation.
     * </pre>
     *
     * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
     */
    com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus getBalancingStatuses(int index);
    /**
     * <pre>
     * Balancing Status(uint32_t) Need to do bit wise operation.
     * </pre>
     *
     * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
     */
    int getBalancingStatusesCount();
    /**
     * <pre>
     * Balancing Status(uint32_t) Need to do bit wise operation.
     * </pre>
     *
     * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
     */
    java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatusOrBuilder> 
        getBalancingStatusesOrBuilderList();
    /**
     * <pre>
     * Balancing Status(uint32_t) Need to do bit wise operation.
     * </pre>
     *
     * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
     */
    com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatusOrBuilder getBalancingStatusesOrBuilder(
        int index);

    /**
     * <pre>
     * Remaining Capacity
     * </pre>
     *
     * <code>sint32 remainingCapacity = 26;</code>
     * @return Whether the remainingCapacity field is set.
     */
    boolean hasRemainingCapacity();
    /**
     * <pre>
     * Remaining Capacity
     * </pre>
     *
     * <code>sint32 remainingCapacity = 26;</code>
     * @return The remainingCapacity.
     */
    int getRemainingCapacity();

    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 27;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 27;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 27;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 28;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 28;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 28;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();

    /**
     * <pre>
     * Temp maximum  (in Celsius)
     * </pre>
     *
     * <code>float mosfetTemperature = 29;</code>
     * @return Whether the mosfetTemperature field is set.
     */
    boolean hasMosfetTemperature();
    /**
     * <pre>
     * Temp maximum  (in Celsius)
     * </pre>
     *
     * <code>float mosfetTemperature = 29;</code>
     * @return The mosfetTemperature.
     */
    float getMosfetTemperature();
  }
  /**
   * Protobuf type {@code Status}
   */
  public static final class Status extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Status)
      StatusOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Status.newBuilder() to construct.
    private Status(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Status() {
      alarms_ = java.util.Collections.emptyList();
      protections_ = java.util.Collections.emptyList();
      balancingStatuses_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Status();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Status(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              chgCycleCount_ = input.readSInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              dsgCycleCount_ = input.readSInt32();
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                alarms_ = new java.util.ArrayList<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm>();
                mutable_bitField0_ |= 0x00000004;
              }
              alarms_.add(
                  input.readMessage(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.parser(), extensionRegistry));
              break;
            }
            case 56: {
              int rawValue = input.readEnum();
              if (!((mutable_bitField0_ & 0x00000008) != 0)) {
                protections_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000008;
              }
              protections_.add(rawValue);
              break;
            }
            case 58: {
              int length = input.readRawVarint32();
              int oldLimit = input.pushLimit(length);
              while(input.getBytesUntilLimit() > 0) {
                int rawValue = input.readEnum();
                if (!((mutable_bitField0_ & 0x00000008) != 0)) {
                  protections_ = new java.util.ArrayList<java.lang.Integer>();
                  mutable_bitField0_ |= 0x00000008;
                }
                protections_.add(rawValue);
              }
              input.popLimit(oldLimit);
              break;
            }
            case 77: {
              bitField0_ |= 0x00000004;
              cellVoltMin_ = input.readFloat();
              break;
            }
            case 93: {
              bitField0_ |= 0x00000008;
              cellVoltMax_ = input.readFloat();
              break;
            }
            case 109: {
              bitField0_ |= 0x00000010;
              temperatureMin_ = input.readFloat();
              break;
            }
            case 125: {
              bitField0_ |= 0x00000020;
              temperatureMax_ = input.readFloat();
              break;
            }
            case 141: {
              bitField0_ |= 0x00000040;
              batteryVolt_ = input.readFloat();
              break;
            }
            case 157: {
              bitField0_ |= 0x00000080;
              soc_ = input.readFloat();
              break;
            }
            case 173: {
              bitField0_ |= 0x00000100;
              soh_ = input.readFloat();
              break;
            }
            case 189: {
              bitField0_ |= 0x00000200;
              current_ = input.readFloat();
              break;
            }
            case 202: {
              if (!((mutable_bitField0_ & 0x00001000) != 0)) {
                balancingStatuses_ = new java.util.ArrayList<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus>();
                mutable_bitField0_ |= 0x00001000;
              }
              balancingStatuses_.add(
                  input.readMessage(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.parser(), extensionRegistry));
              break;
            }
            case 208: {
              bitField0_ |= 0x00000400;
              remainingCapacity_ = input.readSInt32();
              break;
            }
            case 218: {
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder subBuilder = null;
              if (timestamp_ != null) {
                subBuilder = timestamp_.toBuilder();
              }
              timestamp_ = input.readMessage(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(timestamp_);
                timestamp_ = subBuilder.buildPartial();
              }

              break;
            }
            case 226: {
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder subBuilder = null;
              if (metadata_ != null) {
                subBuilder = metadata_.toBuilder();
              }
              metadata_ = input.readMessage(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(metadata_);
                metadata_ = subBuilder.buildPartial();
              }

              break;
            }
            case 237: {
              bitField0_ |= 0x00000800;
              mosfetTemperature_ = input.readFloat();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          alarms_ = java.util.Collections.unmodifiableList(alarms_);
        }
        if (((mutable_bitField0_ & 0x00000008) != 0)) {
          protections_ = java.util.Collections.unmodifiableList(protections_);
        }
        if (((mutable_bitField0_ & 0x00001000) != 0)) {
          balancingStatuses_ = java.util.Collections.unmodifiableList(balancingStatuses_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Status_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Status_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status.Builder.class);
    }

    private int bitField0_;
    public static final int CHGCYCLECOUNT_FIELD_NUMBER = 1;
    private int chgCycleCount_;
    /**
     * <code>sint32 chgCycleCount = 1;</code>
     * @return Whether the chgCycleCount field is set.
     */
    @java.lang.Override
    public boolean hasChgCycleCount() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>sint32 chgCycleCount = 1;</code>
     * @return The chgCycleCount.
     */
    @java.lang.Override
    public int getChgCycleCount() {
      return chgCycleCount_;
    }

    public static final int DSGCYCLECOUNT_FIELD_NUMBER = 3;
    private int dsgCycleCount_;
    /**
     * <pre>
     * DSG CYCLE COUNT
     * </pre>
     *
     * <code>sint32 dsgCycleCount = 3;</code>
     * @return Whether the dsgCycleCount field is set.
     */
    @java.lang.Override
    public boolean hasDsgCycleCount() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * DSG CYCLE COUNT
     * </pre>
     *
     * <code>sint32 dsgCycleCount = 3;</code>
     * @return The dsgCycleCount.
     */
    @java.lang.Override
    public int getDsgCycleCount() {
      return dsgCycleCount_;
    }

    public static final int ALARMS_FIELD_NUMBER = 5;
    private java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm> alarms_;
    /**
     * <pre>
     * Alarms
     * </pre>
     *
     * <code>repeated .Alarm alarms = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm> getAlarmsList() {
      return alarms_;
    }
    /**
     * <pre>
     * Alarms
     * </pre>
     *
     * <code>repeated .Alarm alarms = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.AlarmOrBuilder> 
        getAlarmsOrBuilderList() {
      return alarms_;
    }
    /**
     * <pre>
     * Alarms
     * </pre>
     *
     * <code>repeated .Alarm alarms = 5;</code>
     */
    @java.lang.Override
    public int getAlarmsCount() {
      return alarms_.size();
    }
    /**
     * <pre>
     * Alarms
     * </pre>
     *
     * <code>repeated .Alarm alarms = 5;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm getAlarms(int index) {
      return alarms_.get(index);
    }
    /**
     * <pre>
     * Alarms
     * </pre>
     *
     * <code>repeated .Alarm alarms = 5;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.AlarmOrBuilder getAlarmsOrBuilder(
        int index) {
      return alarms_.get(index);
    }

    public static final int PROTECTIONS_FIELD_NUMBER = 7;
    private java.util.List<java.lang.Integer> protections_;
    private static final com.google.protobuf.Internal.ListAdapter.Converter<
        java.lang.Integer, com.nichesolv.nds.model.proto.model.ProtectionProto.Protection> protections_converter_ =
            new com.google.protobuf.Internal.ListAdapter.Converter<
                java.lang.Integer, com.nichesolv.nds.model.proto.model.ProtectionProto.Protection>() {
              public com.nichesolv.nds.model.proto.model.ProtectionProto.Protection convert(java.lang.Integer from) {
                @SuppressWarnings("deprecation")
                com.nichesolv.nds.model.proto.model.ProtectionProto.Protection result = com.nichesolv.nds.model.proto.model.ProtectionProto.Protection.valueOf(from);
                return result == null ? com.nichesolv.nds.model.proto.model.ProtectionProto.Protection.UNRECOGNIZED : result;
              }
            };
    /**
     * <pre>
     * Protections
     * </pre>
     *
     * <code>repeated .Protection protections = 7;</code>
     * @return A list containing the protections.
     */
    @java.lang.Override
    public java.util.List<com.nichesolv.nds.model.proto.model.ProtectionProto.Protection> getProtectionsList() {
      return new com.google.protobuf.Internal.ListAdapter<
          java.lang.Integer, com.nichesolv.nds.model.proto.model.ProtectionProto.Protection>(protections_, protections_converter_);
    }
    /**
     * <pre>
     * Protections
     * </pre>
     *
     * <code>repeated .Protection protections = 7;</code>
     * @return The count of protections.
     */
    @java.lang.Override
    public int getProtectionsCount() {
      return protections_.size();
    }
    /**
     * <pre>
     * Protections
     * </pre>
     *
     * <code>repeated .Protection protections = 7;</code>
     * @param index The index of the element to return.
     * @return The protections at the given index.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.ProtectionProto.Protection getProtections(int index) {
      return protections_converter_.convert(protections_.get(index));
    }
    /**
     * <pre>
     * Protections
     * </pre>
     *
     * <code>repeated .Protection protections = 7;</code>
     * @return A list containing the enum numeric values on the wire for protections.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
    getProtectionsValueList() {
      return protections_;
    }
    /**
     * <pre>
     * Protections
     * </pre>
     *
     * <code>repeated .Protection protections = 7;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of protections at the given index.
     */
    @java.lang.Override
    public int getProtectionsValue(int index) {
      return protections_.get(index);
    }
    private int protectionsMemoizedSerializedSize;

    public static final int CELLVOLTMIN_FIELD_NUMBER = 9;
    private float cellVoltMin_;
    /**
     * <pre>
     * Cell Volt Min (uint16_t) (V)
     * </pre>
     *
     * <code>float cellVoltMin = 9;</code>
     * @return Whether the cellVoltMin field is set.
     */
    @java.lang.Override
    public boolean hasCellVoltMin() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * Cell Volt Min (uint16_t) (V)
     * </pre>
     *
     * <code>float cellVoltMin = 9;</code>
     * @return The cellVoltMin.
     */
    @java.lang.Override
    public float getCellVoltMin() {
      return cellVoltMin_;
    }

    public static final int CELLVOLTMAX_FIELD_NUMBER = 11;
    private float cellVoltMax_;
    /**
     * <pre>
     * Cell Volt Maximum  (V)
     * </pre>
     *
     * <code>float cellVoltMax = 11;</code>
     * @return Whether the cellVoltMax field is set.
     */
    @java.lang.Override
    public boolean hasCellVoltMax() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * Cell Volt Maximum  (V)
     * </pre>
     *
     * <code>float cellVoltMax = 11;</code>
     * @return The cellVoltMax.
     */
    @java.lang.Override
    public float getCellVoltMax() {
      return cellVoltMax_;
    }

    public static final int TEMPERATUREMIN_FIELD_NUMBER = 13;
    private float temperatureMin_;
    /**
     * <pre>
     * Temp minimum (in celsius)	.
     * </pre>
     *
     * <code>float temperatureMin = 13;</code>
     * @return Whether the temperatureMin field is set.
     */
    @java.lang.Override
    public boolean hasTemperatureMin() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * Temp minimum (in celsius)	.
     * </pre>
     *
     * <code>float temperatureMin = 13;</code>
     * @return The temperatureMin.
     */
    @java.lang.Override
    public float getTemperatureMin() {
      return temperatureMin_;
    }

    public static final int TEMPERATUREMAX_FIELD_NUMBER = 15;
    private float temperatureMax_;
    /**
     * <pre>
     * Temp maximum  (in Celsius)
     * </pre>
     *
     * <code>float temperatureMax = 15;</code>
     * @return Whether the temperatureMax field is set.
     */
    @java.lang.Override
    public boolean hasTemperatureMax() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * Temp maximum  (in Celsius)
     * </pre>
     *
     * <code>float temperatureMax = 15;</code>
     * @return The temperatureMax.
     */
    @java.lang.Override
    public float getTemperatureMax() {
      return temperatureMax_;
    }

    public static final int BATTERYVOLT_FIELD_NUMBER = 17;
    private float batteryVolt_;
    /**
     * <pre>
     ** Batt Volt (float) (V) 
     * </pre>
     *
     * <code>float batteryVolt = 17;</code>
     * @return Whether the batteryVolt field is set.
     */
    @java.lang.Override
    public boolean hasBatteryVolt() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     ** Batt Volt (float) (V) 
     * </pre>
     *
     * <code>float batteryVolt = 17;</code>
     * @return The batteryVolt.
     */
    @java.lang.Override
    public float getBatteryVolt() {
      return batteryVolt_;
    }

    public static final int SOC_FIELD_NUMBER = 19;
    private float soc_;
    /**
     * <pre>
     ** SOC  
     * </pre>
     *
     * <code>float soc = 19;</code>
     * @return Whether the soc field is set.
     */
    @java.lang.Override
    public boolean hasSoc() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     ** SOC  
     * </pre>
     *
     * <code>float soc = 19;</code>
     * @return The soc.
     */
    @java.lang.Override
    public float getSoc() {
      return soc_;
    }

    public static final int SOH_FIELD_NUMBER = 21;
    private float soh_;
    /**
     * <pre>
     ** SOH  
     * </pre>
     *
     * <code>float soh = 21;</code>
     * @return Whether the soh field is set.
     */
    @java.lang.Override
    public boolean hasSoh() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     ** SOH  
     * </pre>
     *
     * <code>float soh = 21;</code>
     * @return The soh.
     */
    @java.lang.Override
    public float getSoh() {
      return soh_;
    }

    public static final int CURRENT_FIELD_NUMBER = 23;
    private float current_;
    /**
     * <pre>
     * Current (float) (A).
     * </pre>
     *
     * <code>float current = 23;</code>
     * @return Whether the current field is set.
     */
    @java.lang.Override
    public boolean hasCurrent() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * Current (float) (A).
     * </pre>
     *
     * <code>float current = 23;</code>
     * @return The current.
     */
    @java.lang.Override
    public float getCurrent() {
      return current_;
    }

    public static final int BALANCINGSTATUSES_FIELD_NUMBER = 25;
    private java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus> balancingStatuses_;
    /**
     * <pre>
     * Balancing Status(uint32_t) Need to do bit wise operation.
     * </pre>
     *
     * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
     */
    @java.lang.Override
    public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus> getBalancingStatusesList() {
      return balancingStatuses_;
    }
    /**
     * <pre>
     * Balancing Status(uint32_t) Need to do bit wise operation.
     * </pre>
     *
     * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatusOrBuilder> 
        getBalancingStatusesOrBuilderList() {
      return balancingStatuses_;
    }
    /**
     * <pre>
     * Balancing Status(uint32_t) Need to do bit wise operation.
     * </pre>
     *
     * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
     */
    @java.lang.Override
    public int getBalancingStatusesCount() {
      return balancingStatuses_.size();
    }
    /**
     * <pre>
     * Balancing Status(uint32_t) Need to do bit wise operation.
     * </pre>
     *
     * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus getBalancingStatuses(int index) {
      return balancingStatuses_.get(index);
    }
    /**
     * <pre>
     * Balancing Status(uint32_t) Need to do bit wise operation.
     * </pre>
     *
     * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatusOrBuilder getBalancingStatusesOrBuilder(
        int index) {
      return balancingStatuses_.get(index);
    }

    public static final int REMAININGCAPACITY_FIELD_NUMBER = 26;
    private int remainingCapacity_;
    /**
     * <pre>
     * Remaining Capacity
     * </pre>
     *
     * <code>sint32 remainingCapacity = 26;</code>
     * @return Whether the remainingCapacity field is set.
     */
    @java.lang.Override
    public boolean hasRemainingCapacity() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * Remaining Capacity
     * </pre>
     *
     * <code>sint32 remainingCapacity = 26;</code>
     * @return The remainingCapacity.
     */
    @java.lang.Override
    public int getRemainingCapacity() {
      return remainingCapacity_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 27;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 27;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return timestamp_ != null;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 27;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 27;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return getTimestamp();
    }

    public static final int METADATA_FIELD_NUMBER = 28;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 28;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return metadata_ != null;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 28;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 28;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return getMetadata();
    }

    public static final int MOSFETTEMPERATURE_FIELD_NUMBER = 29;
    private float mosfetTemperature_;
    /**
     * <pre>
     * Temp maximum  (in Celsius)
     * </pre>
     *
     * <code>float mosfetTemperature = 29;</code>
     * @return Whether the mosfetTemperature field is set.
     */
    @java.lang.Override
    public boolean hasMosfetTemperature() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * Temp maximum  (in Celsius)
     * </pre>
     *
     * <code>float mosfetTemperature = 29;</code>
     * @return The mosfetTemperature.
     */
    @java.lang.Override
    public float getMosfetTemperature() {
      return mosfetTemperature_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeSInt32(1, chgCycleCount_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeSInt32(3, dsgCycleCount_);
      }
      for (int i = 0; i < alarms_.size(); i++) {
        output.writeMessage(5, alarms_.get(i));
      }
      if (getProtectionsList().size() > 0) {
        output.writeUInt32NoTag(58);
        output.writeUInt32NoTag(protectionsMemoizedSerializedSize);
      }
      for (int i = 0; i < protections_.size(); i++) {
        output.writeEnumNoTag(protections_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeFloat(9, cellVoltMin_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeFloat(11, cellVoltMax_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeFloat(13, temperatureMin_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeFloat(15, temperatureMax_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeFloat(17, batteryVolt_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeFloat(19, soc_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeFloat(21, soh_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeFloat(23, current_);
      }
      for (int i = 0; i < balancingStatuses_.size(); i++) {
        output.writeMessage(25, balancingStatuses_.get(i));
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeSInt32(26, remainingCapacity_);
      }
      if (timestamp_ != null) {
        output.writeMessage(27, getTimestamp());
      }
      if (metadata_ != null) {
        output.writeMessage(28, getMetadata());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeFloat(29, mosfetTemperature_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(1, chgCycleCount_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(3, dsgCycleCount_);
      }
      for (int i = 0; i < alarms_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, alarms_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protections_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeEnumSizeNoTag(protections_.get(i));
        }
        size += dataSize;
        if (!getProtectionsList().isEmpty()) {  size += 1;
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(dataSize);
        }protectionsMemoizedSerializedSize = dataSize;
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(9, cellVoltMin_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(11, cellVoltMax_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(13, temperatureMin_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(15, temperatureMax_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(17, batteryVolt_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(19, soc_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(21, soh_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(23, current_);
      }
      for (int i = 0; i < balancingStatuses_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(25, balancingStatuses_.get(i));
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(26, remainingCapacity_);
      }
      if (timestamp_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(27, getTimestamp());
      }
      if (metadata_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(28, getMetadata());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(29, mosfetTemperature_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status other = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status) obj;

      if (hasChgCycleCount() != other.hasChgCycleCount()) return false;
      if (hasChgCycleCount()) {
        if (getChgCycleCount()
            != other.getChgCycleCount()) return false;
      }
      if (hasDsgCycleCount() != other.hasDsgCycleCount()) return false;
      if (hasDsgCycleCount()) {
        if (getDsgCycleCount()
            != other.getDsgCycleCount()) return false;
      }
      if (!getAlarmsList()
          .equals(other.getAlarmsList())) return false;
      if (!protections_.equals(other.protections_)) return false;
      if (hasCellVoltMin() != other.hasCellVoltMin()) return false;
      if (hasCellVoltMin()) {
        if (java.lang.Float.floatToIntBits(getCellVoltMin())
            != java.lang.Float.floatToIntBits(
                other.getCellVoltMin())) return false;
      }
      if (hasCellVoltMax() != other.hasCellVoltMax()) return false;
      if (hasCellVoltMax()) {
        if (java.lang.Float.floatToIntBits(getCellVoltMax())
            != java.lang.Float.floatToIntBits(
                other.getCellVoltMax())) return false;
      }
      if (hasTemperatureMin() != other.hasTemperatureMin()) return false;
      if (hasTemperatureMin()) {
        if (java.lang.Float.floatToIntBits(getTemperatureMin())
            != java.lang.Float.floatToIntBits(
                other.getTemperatureMin())) return false;
      }
      if (hasTemperatureMax() != other.hasTemperatureMax()) return false;
      if (hasTemperatureMax()) {
        if (java.lang.Float.floatToIntBits(getTemperatureMax())
            != java.lang.Float.floatToIntBits(
                other.getTemperatureMax())) return false;
      }
      if (hasBatteryVolt() != other.hasBatteryVolt()) return false;
      if (hasBatteryVolt()) {
        if (java.lang.Float.floatToIntBits(getBatteryVolt())
            != java.lang.Float.floatToIntBits(
                other.getBatteryVolt())) return false;
      }
      if (hasSoc() != other.hasSoc()) return false;
      if (hasSoc()) {
        if (java.lang.Float.floatToIntBits(getSoc())
            != java.lang.Float.floatToIntBits(
                other.getSoc())) return false;
      }
      if (hasSoh() != other.hasSoh()) return false;
      if (hasSoh()) {
        if (java.lang.Float.floatToIntBits(getSoh())
            != java.lang.Float.floatToIntBits(
                other.getSoh())) return false;
      }
      if (hasCurrent() != other.hasCurrent()) return false;
      if (hasCurrent()) {
        if (java.lang.Float.floatToIntBits(getCurrent())
            != java.lang.Float.floatToIntBits(
                other.getCurrent())) return false;
      }
      if (!getBalancingStatusesList()
          .equals(other.getBalancingStatusesList())) return false;
      if (hasRemainingCapacity() != other.hasRemainingCapacity()) return false;
      if (hasRemainingCapacity()) {
        if (getRemainingCapacity()
            != other.getRemainingCapacity()) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (hasMosfetTemperature() != other.hasMosfetTemperature()) return false;
      if (hasMosfetTemperature()) {
        if (java.lang.Float.floatToIntBits(getMosfetTemperature())
            != java.lang.Float.floatToIntBits(
                other.getMosfetTemperature())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChgCycleCount()) {
        hash = (37 * hash) + CHGCYCLECOUNT_FIELD_NUMBER;
        hash = (53 * hash) + getChgCycleCount();
      }
      if (hasDsgCycleCount()) {
        hash = (37 * hash) + DSGCYCLECOUNT_FIELD_NUMBER;
        hash = (53 * hash) + getDsgCycleCount();
      }
      if (getAlarmsCount() > 0) {
        hash = (37 * hash) + ALARMS_FIELD_NUMBER;
        hash = (53 * hash) + getAlarmsList().hashCode();
      }
      if (getProtectionsCount() > 0) {
        hash = (37 * hash) + PROTECTIONS_FIELD_NUMBER;
        hash = (53 * hash) + protections_.hashCode();
      }
      if (hasCellVoltMin()) {
        hash = (37 * hash) + CELLVOLTMIN_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getCellVoltMin());
      }
      if (hasCellVoltMax()) {
        hash = (37 * hash) + CELLVOLTMAX_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getCellVoltMax());
      }
      if (hasTemperatureMin()) {
        hash = (37 * hash) + TEMPERATUREMIN_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getTemperatureMin());
      }
      if (hasTemperatureMax()) {
        hash = (37 * hash) + TEMPERATUREMAX_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getTemperatureMax());
      }
      if (hasBatteryVolt()) {
        hash = (37 * hash) + BATTERYVOLT_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getBatteryVolt());
      }
      if (hasSoc()) {
        hash = (37 * hash) + SOC_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getSoc());
      }
      if (hasSoh()) {
        hash = (37 * hash) + SOH_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getSoh());
      }
      if (hasCurrent()) {
        hash = (37 * hash) + CURRENT_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getCurrent());
      }
      if (getBalancingStatusesCount() > 0) {
        hash = (37 * hash) + BALANCINGSTATUSES_FIELD_NUMBER;
        hash = (53 * hash) + getBalancingStatusesList().hashCode();
      }
      if (hasRemainingCapacity()) {
        hash = (37 * hash) + REMAININGCAPACITY_FIELD_NUMBER;
        hash = (53 * hash) + getRemainingCapacity();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      if (hasMosfetTemperature()) {
        hash = (37 * hash) + MOSFETTEMPERATURE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getMosfetTemperature());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Status}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Status)
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.StatusOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Status_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Status_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getAlarmsFieldBuilder();
          getBalancingStatusesFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chgCycleCount_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        dsgCycleCount_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (alarmsBuilder_ == null) {
          alarms_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          alarmsBuilder_.clear();
        }
        protections_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        cellVoltMin_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000010);
        cellVoltMax_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000020);
        temperatureMin_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000040);
        temperatureMax_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000080);
        batteryVolt_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000100);
        soc_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000200);
        soh_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000400);
        current_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000800);
        if (balancingStatusesBuilder_ == null) {
          balancingStatuses_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00001000);
        } else {
          balancingStatusesBuilder_.clear();
        }
        remainingCapacity_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        if (timestampBuilder_ == null) {
          timestamp_ = null;
        } else {
          timestamp_ = null;
          timestampBuilder_ = null;
        }
        if (metadataBuilder_ == null) {
          metadata_ = null;
        } else {
          metadata_ = null;
          metadataBuilder_ = null;
        }
        mosfetTemperature_ = 0F;
        bitField0_ = (bitField0_ & ~0x00004000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Status_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status build() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status buildPartial() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status result = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chgCycleCount_ = chgCycleCount_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.dsgCycleCount_ = dsgCycleCount_;
          to_bitField0_ |= 0x00000002;
        }
        if (alarmsBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            alarms_ = java.util.Collections.unmodifiableList(alarms_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.alarms_ = alarms_;
        } else {
          result.alarms_ = alarmsBuilder_.build();
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          protections_ = java.util.Collections.unmodifiableList(protections_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.protections_ = protections_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.cellVoltMin_ = cellVoltMin_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.cellVoltMax_ = cellVoltMax_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.temperatureMin_ = temperatureMin_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.temperatureMax_ = temperatureMax_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.batteryVolt_ = batteryVolt_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.soc_ = soc_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.soh_ = soh_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.current_ = current_;
          to_bitField0_ |= 0x00000200;
        }
        if (balancingStatusesBuilder_ == null) {
          if (((bitField0_ & 0x00001000) != 0)) {
            balancingStatuses_ = java.util.Collections.unmodifiableList(balancingStatuses_);
            bitField0_ = (bitField0_ & ~0x00001000);
          }
          result.balancingStatuses_ = balancingStatuses_;
        } else {
          result.balancingStatuses_ = balancingStatusesBuilder_.build();
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.remainingCapacity_ = remainingCapacity_;
          to_bitField0_ |= 0x00000400;
        }
        if (timestampBuilder_ == null) {
          result.timestamp_ = timestamp_;
        } else {
          result.timestamp_ = timestampBuilder_.build();
        }
        if (metadataBuilder_ == null) {
          result.metadata_ = metadata_;
        } else {
          result.metadata_ = metadataBuilder_.build();
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.mosfetTemperature_ = mosfetTemperature_;
          to_bitField0_ |= 0x00000800;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status other) {
        if (other == com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status.getDefaultInstance()) return this;
        if (other.hasChgCycleCount()) {
          setChgCycleCount(other.getChgCycleCount());
        }
        if (other.hasDsgCycleCount()) {
          setDsgCycleCount(other.getDsgCycleCount());
        }
        if (alarmsBuilder_ == null) {
          if (!other.alarms_.isEmpty()) {
            if (alarms_.isEmpty()) {
              alarms_ = other.alarms_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureAlarmsIsMutable();
              alarms_.addAll(other.alarms_);
            }
            onChanged();
          }
        } else {
          if (!other.alarms_.isEmpty()) {
            if (alarmsBuilder_.isEmpty()) {
              alarmsBuilder_.dispose();
              alarmsBuilder_ = null;
              alarms_ = other.alarms_;
              bitField0_ = (bitField0_ & ~0x00000004);
              alarmsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getAlarmsFieldBuilder() : null;
            } else {
              alarmsBuilder_.addAllMessages(other.alarms_);
            }
          }
        }
        if (!other.protections_.isEmpty()) {
          if (protections_.isEmpty()) {
            protections_ = other.protections_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureProtectionsIsMutable();
            protections_.addAll(other.protections_);
          }
          onChanged();
        }
        if (other.hasCellVoltMin()) {
          setCellVoltMin(other.getCellVoltMin());
        }
        if (other.hasCellVoltMax()) {
          setCellVoltMax(other.getCellVoltMax());
        }
        if (other.hasTemperatureMin()) {
          setTemperatureMin(other.getTemperatureMin());
        }
        if (other.hasTemperatureMax()) {
          setTemperatureMax(other.getTemperatureMax());
        }
        if (other.hasBatteryVolt()) {
          setBatteryVolt(other.getBatteryVolt());
        }
        if (other.hasSoc()) {
          setSoc(other.getSoc());
        }
        if (other.hasSoh()) {
          setSoh(other.getSoh());
        }
        if (other.hasCurrent()) {
          setCurrent(other.getCurrent());
        }
        if (balancingStatusesBuilder_ == null) {
          if (!other.balancingStatuses_.isEmpty()) {
            if (balancingStatuses_.isEmpty()) {
              balancingStatuses_ = other.balancingStatuses_;
              bitField0_ = (bitField0_ & ~0x00001000);
            } else {
              ensureBalancingStatusesIsMutable();
              balancingStatuses_.addAll(other.balancingStatuses_);
            }
            onChanged();
          }
        } else {
          if (!other.balancingStatuses_.isEmpty()) {
            if (balancingStatusesBuilder_.isEmpty()) {
              balancingStatusesBuilder_.dispose();
              balancingStatusesBuilder_ = null;
              balancingStatuses_ = other.balancingStatuses_;
              bitField0_ = (bitField0_ & ~0x00001000);
              balancingStatusesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getBalancingStatusesFieldBuilder() : null;
            } else {
              balancingStatusesBuilder_.addAllMessages(other.balancingStatuses_);
            }
          }
        }
        if (other.hasRemainingCapacity()) {
          setRemainingCapacity(other.getRemainingCapacity());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        if (other.hasMosfetTemperature()) {
          setMosfetTemperature(other.getMosfetTemperature());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int chgCycleCount_ ;
      /**
       * <code>sint32 chgCycleCount = 1;</code>
       * @return Whether the chgCycleCount field is set.
       */
      @java.lang.Override
      public boolean hasChgCycleCount() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>sint32 chgCycleCount = 1;</code>
       * @return The chgCycleCount.
       */
      @java.lang.Override
      public int getChgCycleCount() {
        return chgCycleCount_;
      }
      /**
       * <code>sint32 chgCycleCount = 1;</code>
       * @param value The chgCycleCount to set.
       * @return This builder for chaining.
       */
      public Builder setChgCycleCount(int value) {
        bitField0_ |= 0x00000001;
        chgCycleCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>sint32 chgCycleCount = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChgCycleCount() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chgCycleCount_ = 0;
        onChanged();
        return this;
      }

      private int dsgCycleCount_ ;
      /**
       * <pre>
       * DSG CYCLE COUNT
       * </pre>
       *
       * <code>sint32 dsgCycleCount = 3;</code>
       * @return Whether the dsgCycleCount field is set.
       */
      @java.lang.Override
      public boolean hasDsgCycleCount() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * DSG CYCLE COUNT
       * </pre>
       *
       * <code>sint32 dsgCycleCount = 3;</code>
       * @return The dsgCycleCount.
       */
      @java.lang.Override
      public int getDsgCycleCount() {
        return dsgCycleCount_;
      }
      /**
       * <pre>
       * DSG CYCLE COUNT
       * </pre>
       *
       * <code>sint32 dsgCycleCount = 3;</code>
       * @param value The dsgCycleCount to set.
       * @return This builder for chaining.
       */
      public Builder setDsgCycleCount(int value) {
        bitField0_ |= 0x00000002;
        dsgCycleCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DSG CYCLE COUNT
       * </pre>
       *
       * <code>sint32 dsgCycleCount = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDsgCycleCount() {
        bitField0_ = (bitField0_ & ~0x00000002);
        dsgCycleCount_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm> alarms_ =
        java.util.Collections.emptyList();
      private void ensureAlarmsIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          alarms_ = new java.util.ArrayList<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm>(alarms_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.AlarmOrBuilder> alarmsBuilder_;

      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm> getAlarmsList() {
        if (alarmsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(alarms_);
        } else {
          return alarmsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public int getAlarmsCount() {
        if (alarmsBuilder_ == null) {
          return alarms_.size();
        } else {
          return alarmsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm getAlarms(int index) {
        if (alarmsBuilder_ == null) {
          return alarms_.get(index);
        } else {
          return alarmsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public Builder setAlarms(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm value) {
        if (alarmsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAlarmsIsMutable();
          alarms_.set(index, value);
          onChanged();
        } else {
          alarmsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public Builder setAlarms(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder builderForValue) {
        if (alarmsBuilder_ == null) {
          ensureAlarmsIsMutable();
          alarms_.set(index, builderForValue.build());
          onChanged();
        } else {
          alarmsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public Builder addAlarms(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm value) {
        if (alarmsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAlarmsIsMutable();
          alarms_.add(value);
          onChanged();
        } else {
          alarmsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public Builder addAlarms(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm value) {
        if (alarmsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAlarmsIsMutable();
          alarms_.add(index, value);
          onChanged();
        } else {
          alarmsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public Builder addAlarms(
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder builderForValue) {
        if (alarmsBuilder_ == null) {
          ensureAlarmsIsMutable();
          alarms_.add(builderForValue.build());
          onChanged();
        } else {
          alarmsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public Builder addAlarms(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder builderForValue) {
        if (alarmsBuilder_ == null) {
          ensureAlarmsIsMutable();
          alarms_.add(index, builderForValue.build());
          onChanged();
        } else {
          alarmsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public Builder addAllAlarms(
          java.lang.Iterable<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm> values) {
        if (alarmsBuilder_ == null) {
          ensureAlarmsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, alarms_);
          onChanged();
        } else {
          alarmsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public Builder clearAlarms() {
        if (alarmsBuilder_ == null) {
          alarms_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          alarmsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public Builder removeAlarms(int index) {
        if (alarmsBuilder_ == null) {
          ensureAlarmsIsMutable();
          alarms_.remove(index);
          onChanged();
        } else {
          alarmsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder getAlarmsBuilder(
          int index) {
        return getAlarmsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.AlarmOrBuilder getAlarmsOrBuilder(
          int index) {
        if (alarmsBuilder_ == null) {
          return alarms_.get(index);  } else {
          return alarmsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.AlarmOrBuilder> 
           getAlarmsOrBuilderList() {
        if (alarmsBuilder_ != null) {
          return alarmsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(alarms_);
        }
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder addAlarmsBuilder() {
        return getAlarmsFieldBuilder().addBuilder(
            com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.getDefaultInstance());
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder addAlarmsBuilder(
          int index) {
        return getAlarmsFieldBuilder().addBuilder(
            index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.getDefaultInstance());
      }
      /**
       * <pre>
       * Alarms
       * </pre>
       *
       * <code>repeated .Alarm alarms = 5;</code>
       */
      public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder> 
           getAlarmsBuilderList() {
        return getAlarmsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.AlarmOrBuilder> 
          getAlarmsFieldBuilder() {
        if (alarmsBuilder_ == null) {
          alarmsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.AlarmOrBuilder>(
                  alarms_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          alarms_ = null;
        }
        return alarmsBuilder_;
      }

      private java.util.List<java.lang.Integer> protections_ =
        java.util.Collections.emptyList();
      private void ensureProtectionsIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          protections_ = new java.util.ArrayList<java.lang.Integer>(protections_);
          bitField0_ |= 0x00000008;
        }
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @return A list containing the protections.
       */
      public java.util.List<com.nichesolv.nds.model.proto.model.ProtectionProto.Protection> getProtectionsList() {
        return new com.google.protobuf.Internal.ListAdapter<
            java.lang.Integer, com.nichesolv.nds.model.proto.model.ProtectionProto.Protection>(protections_, protections_converter_);
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @return The count of protections.
       */
      public int getProtectionsCount() {
        return protections_.size();
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @param index The index of the element to return.
       * @return The protections at the given index.
       */
      public com.nichesolv.nds.model.proto.model.ProtectionProto.Protection getProtections(int index) {
        return protections_converter_.convert(protections_.get(index));
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @param index The index to set the value at.
       * @param value The protections to set.
       * @return This builder for chaining.
       */
      public Builder setProtections(
          int index, com.nichesolv.nds.model.proto.model.ProtectionProto.Protection value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProtectionsIsMutable();
        protections_.set(index, value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @param value The protections to add.
       * @return This builder for chaining.
       */
      public Builder addProtections(com.nichesolv.nds.model.proto.model.ProtectionProto.Protection value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProtectionsIsMutable();
        protections_.add(value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @param values The protections to add.
       * @return This builder for chaining.
       */
      public Builder addAllProtections(
          java.lang.Iterable<? extends com.nichesolv.nds.model.proto.model.ProtectionProto.Protection> values) {
        ensureProtectionsIsMutable();
        for (com.nichesolv.nds.model.proto.model.ProtectionProto.Protection value : values) {
          protections_.add(value.getNumber());
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtections() {
        protections_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @return A list containing the enum numeric values on the wire for protections.
       */
      public java.util.List<java.lang.Integer>
      getProtectionsValueList() {
        return java.util.Collections.unmodifiableList(protections_);
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @param index The index of the value to return.
       * @return The enum numeric value on the wire of protections at the given index.
       */
      public int getProtectionsValue(int index) {
        return protections_.get(index);
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @param index The index of the value to return.
       * @return The enum numeric value on the wire of protections at the given index.
       * @return This builder for chaining.
       */
      public Builder setProtectionsValue(
          int index, int value) {
        ensureProtectionsIsMutable();
        protections_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @param value The enum numeric value on the wire for protections to add.
       * @return This builder for chaining.
       */
      public Builder addProtectionsValue(int value) {
        ensureProtectionsIsMutable();
        protections_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Protections
       * </pre>
       *
       * <code>repeated .Protection protections = 7;</code>
       * @param values The enum numeric values on the wire for protections to add.
       * @return This builder for chaining.
       */
      public Builder addAllProtectionsValue(
          java.lang.Iterable<java.lang.Integer> values) {
        ensureProtectionsIsMutable();
        for (int value : values) {
          protections_.add(value);
        }
        onChanged();
        return this;
      }

      private float cellVoltMin_ ;
      /**
       * <pre>
       * Cell Volt Min (uint16_t) (V)
       * </pre>
       *
       * <code>float cellVoltMin = 9;</code>
       * @return Whether the cellVoltMin field is set.
       */
      @java.lang.Override
      public boolean hasCellVoltMin() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * Cell Volt Min (uint16_t) (V)
       * </pre>
       *
       * <code>float cellVoltMin = 9;</code>
       * @return The cellVoltMin.
       */
      @java.lang.Override
      public float getCellVoltMin() {
        return cellVoltMin_;
      }
      /**
       * <pre>
       * Cell Volt Min (uint16_t) (V)
       * </pre>
       *
       * <code>float cellVoltMin = 9;</code>
       * @param value The cellVoltMin to set.
       * @return This builder for chaining.
       */
      public Builder setCellVoltMin(float value) {
        bitField0_ |= 0x00000010;
        cellVoltMin_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Cell Volt Min (uint16_t) (V)
       * </pre>
       *
       * <code>float cellVoltMin = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearCellVoltMin() {
        bitField0_ = (bitField0_ & ~0x00000010);
        cellVoltMin_ = 0F;
        onChanged();
        return this;
      }

      private float cellVoltMax_ ;
      /**
       * <pre>
       * Cell Volt Maximum  (V)
       * </pre>
       *
       * <code>float cellVoltMax = 11;</code>
       * @return Whether the cellVoltMax field is set.
       */
      @java.lang.Override
      public boolean hasCellVoltMax() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * Cell Volt Maximum  (V)
       * </pre>
       *
       * <code>float cellVoltMax = 11;</code>
       * @return The cellVoltMax.
       */
      @java.lang.Override
      public float getCellVoltMax() {
        return cellVoltMax_;
      }
      /**
       * <pre>
       * Cell Volt Maximum  (V)
       * </pre>
       *
       * <code>float cellVoltMax = 11;</code>
       * @param value The cellVoltMax to set.
       * @return This builder for chaining.
       */
      public Builder setCellVoltMax(float value) {
        bitField0_ |= 0x00000020;
        cellVoltMax_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Cell Volt Maximum  (V)
       * </pre>
       *
       * <code>float cellVoltMax = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearCellVoltMax() {
        bitField0_ = (bitField0_ & ~0x00000020);
        cellVoltMax_ = 0F;
        onChanged();
        return this;
      }

      private float temperatureMin_ ;
      /**
       * <pre>
       * Temp minimum (in celsius)	.
       * </pre>
       *
       * <code>float temperatureMin = 13;</code>
       * @return Whether the temperatureMin field is set.
       */
      @java.lang.Override
      public boolean hasTemperatureMin() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * Temp minimum (in celsius)	.
       * </pre>
       *
       * <code>float temperatureMin = 13;</code>
       * @return The temperatureMin.
       */
      @java.lang.Override
      public float getTemperatureMin() {
        return temperatureMin_;
      }
      /**
       * <pre>
       * Temp minimum (in celsius)	.
       * </pre>
       *
       * <code>float temperatureMin = 13;</code>
       * @param value The temperatureMin to set.
       * @return This builder for chaining.
       */
      public Builder setTemperatureMin(float value) {
        bitField0_ |= 0x00000040;
        temperatureMin_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Temp minimum (in celsius)	.
       * </pre>
       *
       * <code>float temperatureMin = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemperatureMin() {
        bitField0_ = (bitField0_ & ~0x00000040);
        temperatureMin_ = 0F;
        onChanged();
        return this;
      }

      private float temperatureMax_ ;
      /**
       * <pre>
       * Temp maximum  (in Celsius)
       * </pre>
       *
       * <code>float temperatureMax = 15;</code>
       * @return Whether the temperatureMax field is set.
       */
      @java.lang.Override
      public boolean hasTemperatureMax() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * Temp maximum  (in Celsius)
       * </pre>
       *
       * <code>float temperatureMax = 15;</code>
       * @return The temperatureMax.
       */
      @java.lang.Override
      public float getTemperatureMax() {
        return temperatureMax_;
      }
      /**
       * <pre>
       * Temp maximum  (in Celsius)
       * </pre>
       *
       * <code>float temperatureMax = 15;</code>
       * @param value The temperatureMax to set.
       * @return This builder for chaining.
       */
      public Builder setTemperatureMax(float value) {
        bitField0_ |= 0x00000080;
        temperatureMax_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Temp maximum  (in Celsius)
       * </pre>
       *
       * <code>float temperatureMax = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemperatureMax() {
        bitField0_ = (bitField0_ & ~0x00000080);
        temperatureMax_ = 0F;
        onChanged();
        return this;
      }

      private float batteryVolt_ ;
      /**
       * <pre>
       ** Batt Volt (float) (V) 
       * </pre>
       *
       * <code>float batteryVolt = 17;</code>
       * @return Whether the batteryVolt field is set.
       */
      @java.lang.Override
      public boolean hasBatteryVolt() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       ** Batt Volt (float) (V) 
       * </pre>
       *
       * <code>float batteryVolt = 17;</code>
       * @return The batteryVolt.
       */
      @java.lang.Override
      public float getBatteryVolt() {
        return batteryVolt_;
      }
      /**
       * <pre>
       ** Batt Volt (float) (V) 
       * </pre>
       *
       * <code>float batteryVolt = 17;</code>
       * @param value The batteryVolt to set.
       * @return This builder for chaining.
       */
      public Builder setBatteryVolt(float value) {
        bitField0_ |= 0x00000100;
        batteryVolt_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** Batt Volt (float) (V) 
       * </pre>
       *
       * <code>float batteryVolt = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearBatteryVolt() {
        bitField0_ = (bitField0_ & ~0x00000100);
        batteryVolt_ = 0F;
        onChanged();
        return this;
      }

      private float soc_ ;
      /**
       * <pre>
       ** SOC  
       * </pre>
       *
       * <code>float soc = 19;</code>
       * @return Whether the soc field is set.
       */
      @java.lang.Override
      public boolean hasSoc() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       ** SOC  
       * </pre>
       *
       * <code>float soc = 19;</code>
       * @return The soc.
       */
      @java.lang.Override
      public float getSoc() {
        return soc_;
      }
      /**
       * <pre>
       ** SOC  
       * </pre>
       *
       * <code>float soc = 19;</code>
       * @param value The soc to set.
       * @return This builder for chaining.
       */
      public Builder setSoc(float value) {
        bitField0_ |= 0x00000200;
        soc_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** SOC  
       * </pre>
       *
       * <code>float soc = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoc() {
        bitField0_ = (bitField0_ & ~0x00000200);
        soc_ = 0F;
        onChanged();
        return this;
      }

      private float soh_ ;
      /**
       * <pre>
       ** SOH  
       * </pre>
       *
       * <code>float soh = 21;</code>
       * @return Whether the soh field is set.
       */
      @java.lang.Override
      public boolean hasSoh() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       ** SOH  
       * </pre>
       *
       * <code>float soh = 21;</code>
       * @return The soh.
       */
      @java.lang.Override
      public float getSoh() {
        return soh_;
      }
      /**
       * <pre>
       ** SOH  
       * </pre>
       *
       * <code>float soh = 21;</code>
       * @param value The soh to set.
       * @return This builder for chaining.
       */
      public Builder setSoh(float value) {
        bitField0_ |= 0x00000400;
        soh_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** SOH  
       * </pre>
       *
       * <code>float soh = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoh() {
        bitField0_ = (bitField0_ & ~0x00000400);
        soh_ = 0F;
        onChanged();
        return this;
      }

      private float current_ ;
      /**
       * <pre>
       * Current (float) (A).
       * </pre>
       *
       * <code>float current = 23;</code>
       * @return Whether the current field is set.
       */
      @java.lang.Override
      public boolean hasCurrent() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * Current (float) (A).
       * </pre>
       *
       * <code>float current = 23;</code>
       * @return The current.
       */
      @java.lang.Override
      public float getCurrent() {
        return current_;
      }
      /**
       * <pre>
       * Current (float) (A).
       * </pre>
       *
       * <code>float current = 23;</code>
       * @param value The current to set.
       * @return This builder for chaining.
       */
      public Builder setCurrent(float value) {
        bitField0_ |= 0x00000800;
        current_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Current (float) (A).
       * </pre>
       *
       * <code>float current = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrent() {
        bitField0_ = (bitField0_ & ~0x00000800);
        current_ = 0F;
        onChanged();
        return this;
      }

      private java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus> balancingStatuses_ =
        java.util.Collections.emptyList();
      private void ensureBalancingStatusesIsMutable() {
        if (!((bitField0_ & 0x00001000) != 0)) {
          balancingStatuses_ = new java.util.ArrayList<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus>(balancingStatuses_);
          bitField0_ |= 0x00001000;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatusOrBuilder> balancingStatusesBuilder_;

      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus> getBalancingStatusesList() {
        if (balancingStatusesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(balancingStatuses_);
        } else {
          return balancingStatusesBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public int getBalancingStatusesCount() {
        if (balancingStatusesBuilder_ == null) {
          return balancingStatuses_.size();
        } else {
          return balancingStatusesBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus getBalancingStatuses(int index) {
        if (balancingStatusesBuilder_ == null) {
          return balancingStatuses_.get(index);
        } else {
          return balancingStatusesBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public Builder setBalancingStatuses(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus value) {
        if (balancingStatusesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBalancingStatusesIsMutable();
          balancingStatuses_.set(index, value);
          onChanged();
        } else {
          balancingStatusesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public Builder setBalancingStatuses(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder builderForValue) {
        if (balancingStatusesBuilder_ == null) {
          ensureBalancingStatusesIsMutable();
          balancingStatuses_.set(index, builderForValue.build());
          onChanged();
        } else {
          balancingStatusesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public Builder addBalancingStatuses(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus value) {
        if (balancingStatusesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBalancingStatusesIsMutable();
          balancingStatuses_.add(value);
          onChanged();
        } else {
          balancingStatusesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public Builder addBalancingStatuses(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus value) {
        if (balancingStatusesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBalancingStatusesIsMutable();
          balancingStatuses_.add(index, value);
          onChanged();
        } else {
          balancingStatusesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public Builder addBalancingStatuses(
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder builderForValue) {
        if (balancingStatusesBuilder_ == null) {
          ensureBalancingStatusesIsMutable();
          balancingStatuses_.add(builderForValue.build());
          onChanged();
        } else {
          balancingStatusesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public Builder addBalancingStatuses(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder builderForValue) {
        if (balancingStatusesBuilder_ == null) {
          ensureBalancingStatusesIsMutable();
          balancingStatuses_.add(index, builderForValue.build());
          onChanged();
        } else {
          balancingStatusesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public Builder addAllBalancingStatuses(
          java.lang.Iterable<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus> values) {
        if (balancingStatusesBuilder_ == null) {
          ensureBalancingStatusesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, balancingStatuses_);
          onChanged();
        } else {
          balancingStatusesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public Builder clearBalancingStatuses() {
        if (balancingStatusesBuilder_ == null) {
          balancingStatuses_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00001000);
          onChanged();
        } else {
          balancingStatusesBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public Builder removeBalancingStatuses(int index) {
        if (balancingStatusesBuilder_ == null) {
          ensureBalancingStatusesIsMutable();
          balancingStatuses_.remove(index);
          onChanged();
        } else {
          balancingStatusesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder getBalancingStatusesBuilder(
          int index) {
        return getBalancingStatusesFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatusOrBuilder getBalancingStatusesOrBuilder(
          int index) {
        if (balancingStatusesBuilder_ == null) {
          return balancingStatuses_.get(index);  } else {
          return balancingStatusesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatusOrBuilder> 
           getBalancingStatusesOrBuilderList() {
        if (balancingStatusesBuilder_ != null) {
          return balancingStatusesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(balancingStatuses_);
        }
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder addBalancingStatusesBuilder() {
        return getBalancingStatusesFieldBuilder().addBuilder(
            com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.getDefaultInstance());
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder addBalancingStatusesBuilder(
          int index) {
        return getBalancingStatusesFieldBuilder().addBuilder(
            index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.getDefaultInstance());
      }
      /**
       * <pre>
       * Balancing Status(uint32_t) Need to do bit wise operation.
       * </pre>
       *
       * <code>repeated .BalancingStatus balancingStatuses = 25;</code>
       */
      public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder> 
           getBalancingStatusesBuilderList() {
        return getBalancingStatusesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatusOrBuilder> 
          getBalancingStatusesFieldBuilder() {
        if (balancingStatusesBuilder_ == null) {
          balancingStatusesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatusOrBuilder>(
                  balancingStatuses_,
                  ((bitField0_ & 0x00001000) != 0),
                  getParentForChildren(),
                  isClean());
          balancingStatuses_ = null;
        }
        return balancingStatusesBuilder_;
      }

      private int remainingCapacity_ ;
      /**
       * <pre>
       * Remaining Capacity
       * </pre>
       *
       * <code>sint32 remainingCapacity = 26;</code>
       * @return Whether the remainingCapacity field is set.
       */
      @java.lang.Override
      public boolean hasRemainingCapacity() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * Remaining Capacity
       * </pre>
       *
       * <code>sint32 remainingCapacity = 26;</code>
       * @return The remainingCapacity.
       */
      @java.lang.Override
      public int getRemainingCapacity() {
        return remainingCapacity_;
      }
      /**
       * <pre>
       * Remaining Capacity
       * </pre>
       *
       * <code>sint32 remainingCapacity = 26;</code>
       * @param value The remainingCapacity to set.
       * @return This builder for chaining.
       */
      public Builder setRemainingCapacity(int value) {
        bitField0_ |= 0x00002000;
        remainingCapacity_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Remaining Capacity
       * </pre>
       *
       * <code>sint32 remainingCapacity = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearRemainingCapacity() {
        bitField0_ = (bitField0_ & ~0x00002000);
        remainingCapacity_ = 0;
        onChanged();
        return this;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 27;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return timestampBuilder_ != null || timestamp_ != null;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 27;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 27;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
          onChanged();
        } else {
          timestampBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 27;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
          onChanged();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 27;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (timestamp_ != null) {
            timestamp_ =
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.newBuilder(timestamp_).mergeFrom(value).buildPartial();
          } else {
            timestamp_ = value;
          }
          onChanged();
        } else {
          timestampBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 27;</code>
       */
      public Builder clearTimestamp() {
        if (timestampBuilder_ == null) {
          timestamp_ = null;
          onChanged();
        } else {
          timestamp_ = null;
          timestampBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 27;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 27;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 27;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 28;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return metadataBuilder_ != null || metadata_ != null;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 28;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 28;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
          onChanged();
        } else {
          metadataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 28;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
          onChanged();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 28;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (metadata_ != null) {
            metadata_ =
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.newBuilder(metadata_).mergeFrom(value).buildPartial();
          } else {
            metadata_ = value;
          }
          onChanged();
        } else {
          metadataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 28;</code>
       */
      public Builder clearMetadata() {
        if (metadataBuilder_ == null) {
          metadata_ = null;
          onChanged();
        } else {
          metadata_ = null;
          metadataBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 28;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 28;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 28;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }

      private float mosfetTemperature_ ;
      /**
       * <pre>
       * Temp maximum  (in Celsius)
       * </pre>
       *
       * <code>float mosfetTemperature = 29;</code>
       * @return Whether the mosfetTemperature field is set.
       */
      @java.lang.Override
      public boolean hasMosfetTemperature() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * Temp maximum  (in Celsius)
       * </pre>
       *
       * <code>float mosfetTemperature = 29;</code>
       * @return The mosfetTemperature.
       */
      @java.lang.Override
      public float getMosfetTemperature() {
        return mosfetTemperature_;
      }
      /**
       * <pre>
       * Temp maximum  (in Celsius)
       * </pre>
       *
       * <code>float mosfetTemperature = 29;</code>
       * @param value The mosfetTemperature to set.
       * @return This builder for chaining.
       */
      public Builder setMosfetTemperature(float value) {
        bitField0_ |= 0x00004000;
        mosfetTemperature_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Temp maximum  (in Celsius)
       * </pre>
       *
       * <code>float mosfetTemperature = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearMosfetTemperature() {
        bitField0_ = (bitField0_ & ~0x00004000);
        mosfetTemperature_ = 0F;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Status)
    }

    // @@protoc_insertion_point(class_scope:Status)
    private static final com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status();
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Status>
        PARSER = new com.google.protobuf.AbstractParser<Status>() {
      @java.lang.Override
      public Status parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Status(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Status> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Status> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Status getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TemperatureOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Temperature)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .CellTemperature cell_temperatures = 1;</code>
     */
    java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature> 
        getCellTemperaturesList();
    /**
     * <code>repeated .CellTemperature cell_temperatures = 1;</code>
     */
    com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature getCellTemperatures(int index);
    /**
     * <code>repeated .CellTemperature cell_temperatures = 1;</code>
     */
    int getCellTemperaturesCount();
    /**
     * <code>repeated .CellTemperature cell_temperatures = 1;</code>
     */
    java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperatureOrBuilder> 
        getCellTemperaturesOrBuilderList();
    /**
     * <code>repeated .CellTemperature cell_temperatures = 1;</code>
     */
    com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperatureOrBuilder getCellTemperaturesOrBuilder(
        int index);

    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();
  }
  /**
   * Protobuf type {@code Temperature}
   */
  public static final class Temperature extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Temperature)
      TemperatureOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Temperature.newBuilder() to construct.
    private Temperature(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Temperature() {
      cellTemperatures_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Temperature();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Temperature(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                cellTemperatures_ = new java.util.ArrayList<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature>();
                mutable_bitField0_ |= 0x00000001;
              }
              cellTemperatures_.add(
                  input.readMessage(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.parser(), extensionRegistry));
              break;
            }
            case 18: {
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder subBuilder = null;
              if (timestamp_ != null) {
                subBuilder = timestamp_.toBuilder();
              }
              timestamp_ = input.readMessage(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(timestamp_);
                timestamp_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder subBuilder = null;
              if (metadata_ != null) {
                subBuilder = metadata_.toBuilder();
              }
              metadata_ = input.readMessage(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(metadata_);
                metadata_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          cellTemperatures_ = java.util.Collections.unmodifiableList(cellTemperatures_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Temperature_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Temperature_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature.Builder.class);
    }

    public static final int CELL_TEMPERATURES_FIELD_NUMBER = 1;
    private java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature> cellTemperatures_;
    /**
     * <code>repeated .CellTemperature cell_temperatures = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature> getCellTemperaturesList() {
      return cellTemperatures_;
    }
    /**
     * <code>repeated .CellTemperature cell_temperatures = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperatureOrBuilder> 
        getCellTemperaturesOrBuilderList() {
      return cellTemperatures_;
    }
    /**
     * <code>repeated .CellTemperature cell_temperatures = 1;</code>
     */
    @java.lang.Override
    public int getCellTemperaturesCount() {
      return cellTemperatures_.size();
    }
    /**
     * <code>repeated .CellTemperature cell_temperatures = 1;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature getCellTemperatures(int index) {
      return cellTemperatures_.get(index);
    }
    /**
     * <code>repeated .CellTemperature cell_temperatures = 1;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperatureOrBuilder getCellTemperaturesOrBuilder(
        int index) {
      return cellTemperatures_.get(index);
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 2;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return timestamp_ != null;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return getTimestamp();
    }

    public static final int METADATA_FIELD_NUMBER = 3;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return metadata_ != null;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return getMetadata();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < cellTemperatures_.size(); i++) {
        output.writeMessage(1, cellTemperatures_.get(i));
      }
      if (timestamp_ != null) {
        output.writeMessage(2, getTimestamp());
      }
      if (metadata_ != null) {
        output.writeMessage(3, getMetadata());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < cellTemperatures_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, cellTemperatures_.get(i));
      }
      if (timestamp_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getTimestamp());
      }
      if (metadata_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getMetadata());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature other = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature) obj;

      if (!getCellTemperaturesList()
          .equals(other.getCellTemperaturesList())) return false;
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getCellTemperaturesCount() > 0) {
        hash = (37 * hash) + CELL_TEMPERATURES_FIELD_NUMBER;
        hash = (53 * hash) + getCellTemperaturesList().hashCode();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Temperature}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Temperature)
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.TemperatureOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Temperature_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Temperature_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCellTemperaturesFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (cellTemperaturesBuilder_ == null) {
          cellTemperatures_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          cellTemperaturesBuilder_.clear();
        }
        if (timestampBuilder_ == null) {
          timestamp_ = null;
        } else {
          timestamp_ = null;
          timestampBuilder_ = null;
        }
        if (metadataBuilder_ == null) {
          metadata_ = null;
        } else {
          metadata_ = null;
          metadataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Temperature_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature build() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature buildPartial() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature result = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature(this);
        int from_bitField0_ = bitField0_;
        if (cellTemperaturesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            cellTemperatures_ = java.util.Collections.unmodifiableList(cellTemperatures_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.cellTemperatures_ = cellTemperatures_;
        } else {
          result.cellTemperatures_ = cellTemperaturesBuilder_.build();
        }
        if (timestampBuilder_ == null) {
          result.timestamp_ = timestamp_;
        } else {
          result.timestamp_ = timestampBuilder_.build();
        }
        if (metadataBuilder_ == null) {
          result.metadata_ = metadata_;
        } else {
          result.metadata_ = metadataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature other) {
        if (other == com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature.getDefaultInstance()) return this;
        if (cellTemperaturesBuilder_ == null) {
          if (!other.cellTemperatures_.isEmpty()) {
            if (cellTemperatures_.isEmpty()) {
              cellTemperatures_ = other.cellTemperatures_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureCellTemperaturesIsMutable();
              cellTemperatures_.addAll(other.cellTemperatures_);
            }
            onChanged();
          }
        } else {
          if (!other.cellTemperatures_.isEmpty()) {
            if (cellTemperaturesBuilder_.isEmpty()) {
              cellTemperaturesBuilder_.dispose();
              cellTemperaturesBuilder_ = null;
              cellTemperatures_ = other.cellTemperatures_;
              bitField0_ = (bitField0_ & ~0x00000001);
              cellTemperaturesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getCellTemperaturesFieldBuilder() : null;
            } else {
              cellTemperaturesBuilder_.addAllMessages(other.cellTemperatures_);
            }
          }
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature> cellTemperatures_ =
        java.util.Collections.emptyList();
      private void ensureCellTemperaturesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          cellTemperatures_ = new java.util.ArrayList<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature>(cellTemperatures_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperatureOrBuilder> cellTemperaturesBuilder_;

      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature> getCellTemperaturesList() {
        if (cellTemperaturesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(cellTemperatures_);
        } else {
          return cellTemperaturesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public int getCellTemperaturesCount() {
        if (cellTemperaturesBuilder_ == null) {
          return cellTemperatures_.size();
        } else {
          return cellTemperaturesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature getCellTemperatures(int index) {
        if (cellTemperaturesBuilder_ == null) {
          return cellTemperatures_.get(index);
        } else {
          return cellTemperaturesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public Builder setCellTemperatures(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature value) {
        if (cellTemperaturesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCellTemperaturesIsMutable();
          cellTemperatures_.set(index, value);
          onChanged();
        } else {
          cellTemperaturesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public Builder setCellTemperatures(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder builderForValue) {
        if (cellTemperaturesBuilder_ == null) {
          ensureCellTemperaturesIsMutable();
          cellTemperatures_.set(index, builderForValue.build());
          onChanged();
        } else {
          cellTemperaturesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public Builder addCellTemperatures(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature value) {
        if (cellTemperaturesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCellTemperaturesIsMutable();
          cellTemperatures_.add(value);
          onChanged();
        } else {
          cellTemperaturesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public Builder addCellTemperatures(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature value) {
        if (cellTemperaturesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCellTemperaturesIsMutable();
          cellTemperatures_.add(index, value);
          onChanged();
        } else {
          cellTemperaturesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public Builder addCellTemperatures(
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder builderForValue) {
        if (cellTemperaturesBuilder_ == null) {
          ensureCellTemperaturesIsMutable();
          cellTemperatures_.add(builderForValue.build());
          onChanged();
        } else {
          cellTemperaturesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public Builder addCellTemperatures(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder builderForValue) {
        if (cellTemperaturesBuilder_ == null) {
          ensureCellTemperaturesIsMutable();
          cellTemperatures_.add(index, builderForValue.build());
          onChanged();
        } else {
          cellTemperaturesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public Builder addAllCellTemperatures(
          java.lang.Iterable<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature> values) {
        if (cellTemperaturesBuilder_ == null) {
          ensureCellTemperaturesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, cellTemperatures_);
          onChanged();
        } else {
          cellTemperaturesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public Builder clearCellTemperatures() {
        if (cellTemperaturesBuilder_ == null) {
          cellTemperatures_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          cellTemperaturesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public Builder removeCellTemperatures(int index) {
        if (cellTemperaturesBuilder_ == null) {
          ensureCellTemperaturesIsMutable();
          cellTemperatures_.remove(index);
          onChanged();
        } else {
          cellTemperaturesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder getCellTemperaturesBuilder(
          int index) {
        return getCellTemperaturesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperatureOrBuilder getCellTemperaturesOrBuilder(
          int index) {
        if (cellTemperaturesBuilder_ == null) {
          return cellTemperatures_.get(index);  } else {
          return cellTemperaturesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperatureOrBuilder> 
           getCellTemperaturesOrBuilderList() {
        if (cellTemperaturesBuilder_ != null) {
          return cellTemperaturesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(cellTemperatures_);
        }
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder addCellTemperaturesBuilder() {
        return getCellTemperaturesFieldBuilder().addBuilder(
            com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.getDefaultInstance());
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder addCellTemperaturesBuilder(
          int index) {
        return getCellTemperaturesFieldBuilder().addBuilder(
            index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.getDefaultInstance());
      }
      /**
       * <code>repeated .CellTemperature cell_temperatures = 1;</code>
       */
      public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder> 
           getCellTemperaturesBuilderList() {
        return getCellTemperaturesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperatureOrBuilder> 
          getCellTemperaturesFieldBuilder() {
        if (cellTemperaturesBuilder_ == null) {
          cellTemperaturesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperatureOrBuilder>(
                  cellTemperatures_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          cellTemperatures_ = null;
        }
        return cellTemperaturesBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return timestampBuilder_ != null || timestamp_ != null;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
          onChanged();
        } else {
          timestampBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
          onChanged();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (timestamp_ != null) {
            timestamp_ =
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.newBuilder(timestamp_).mergeFrom(value).buildPartial();
          } else {
            timestamp_ = value;
          }
          onChanged();
        } else {
          timestampBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder clearTimestamp() {
        if (timestampBuilder_ == null) {
          timestamp_ = null;
          onChanged();
        } else {
          timestamp_ = null;
          timestampBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return metadataBuilder_ != null || metadata_ != null;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
          onChanged();
        } else {
          metadataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
          onChanged();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (metadata_ != null) {
            metadata_ =
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.newBuilder(metadata_).mergeFrom(value).buildPartial();
          } else {
            metadata_ = value;
          }
          onChanged();
        } else {
          metadataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public Builder clearMetadata() {
        if (metadataBuilder_ == null) {
          metadata_ = null;
          onChanged();
        } else {
          metadata_ = null;
          metadataBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Temperature)
    }

    // @@protoc_insertion_point(class_scope:Temperature)
    private static final com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature();
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Temperature>
        PARSER = new com.google.protobuf.AbstractParser<Temperature>() {
      @java.lang.Override
      public Temperature parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Temperature(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Temperature> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Temperature> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Temperature getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CellTemperatureOrBuilder extends
      // @@protoc_insertion_point(interface_extends:CellTemperature)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>sint32 cell_id = 1;</code>
     * @return Whether the cellId field is set.
     */
    boolean hasCellId();
    /**
     * <code>sint32 cell_id = 1;</code>
     * @return The cellId.
     */
    int getCellId();

    /**
     * <code>float temperature = 3;</code>
     * @return Whether the temperature field is set.
     */
    boolean hasTemperature();
    /**
     * <code>float temperature = 3;</code>
     * @return The temperature.
     */
    float getTemperature();
  }
  /**
   * Protobuf type {@code CellTemperature}
   */
  public static final class CellTemperature extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:CellTemperature)
      CellTemperatureOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CellTemperature.newBuilder() to construct.
    private CellTemperature(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CellTemperature() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CellTemperature();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CellTemperature(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              cellId_ = input.readSInt32();
              break;
            }
            case 29: {
              bitField0_ |= 0x00000002;
              temperature_ = input.readFloat();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_CellTemperature_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_CellTemperature_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder.class);
    }

    private int bitField0_;
    public static final int CELL_ID_FIELD_NUMBER = 1;
    private int cellId_;
    /**
     * <code>sint32 cell_id = 1;</code>
     * @return Whether the cellId field is set.
     */
    @java.lang.Override
    public boolean hasCellId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>sint32 cell_id = 1;</code>
     * @return The cellId.
     */
    @java.lang.Override
    public int getCellId() {
      return cellId_;
    }

    public static final int TEMPERATURE_FIELD_NUMBER = 3;
    private float temperature_;
    /**
     * <code>float temperature = 3;</code>
     * @return Whether the temperature field is set.
     */
    @java.lang.Override
    public boolean hasTemperature() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>float temperature = 3;</code>
     * @return The temperature.
     */
    @java.lang.Override
    public float getTemperature() {
      return temperature_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeSInt32(1, cellId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeFloat(3, temperature_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(1, cellId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(3, temperature_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature other = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature) obj;

      if (hasCellId() != other.hasCellId()) return false;
      if (hasCellId()) {
        if (getCellId()
            != other.getCellId()) return false;
      }
      if (hasTemperature() != other.hasTemperature()) return false;
      if (hasTemperature()) {
        if (java.lang.Float.floatToIntBits(getTemperature())
            != java.lang.Float.floatToIntBits(
                other.getTemperature())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCellId()) {
        hash = (37 * hash) + CELL_ID_FIELD_NUMBER;
        hash = (53 * hash) + getCellId();
      }
      if (hasTemperature()) {
        hash = (37 * hash) + TEMPERATURE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getTemperature());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code CellTemperature}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:CellTemperature)
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperatureOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_CellTemperature_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_CellTemperature_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        cellId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        temperature_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_CellTemperature_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature build() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature buildPartial() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature result = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.cellId_ = cellId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.temperature_ = temperature_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature other) {
        if (other == com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature.getDefaultInstance()) return this;
        if (other.hasCellId()) {
          setCellId(other.getCellId());
        }
        if (other.hasTemperature()) {
          setTemperature(other.getTemperature());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int cellId_ ;
      /**
       * <code>sint32 cell_id = 1;</code>
       * @return Whether the cellId field is set.
       */
      @java.lang.Override
      public boolean hasCellId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>sint32 cell_id = 1;</code>
       * @return The cellId.
       */
      @java.lang.Override
      public int getCellId() {
        return cellId_;
      }
      /**
       * <code>sint32 cell_id = 1;</code>
       * @param value The cellId to set.
       * @return This builder for chaining.
       */
      public Builder setCellId(int value) {
        bitField0_ |= 0x00000001;
        cellId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>sint32 cell_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCellId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cellId_ = 0;
        onChanged();
        return this;
      }

      private float temperature_ ;
      /**
       * <code>float temperature = 3;</code>
       * @return Whether the temperature field is set.
       */
      @java.lang.Override
      public boolean hasTemperature() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>float temperature = 3;</code>
       * @return The temperature.
       */
      @java.lang.Override
      public float getTemperature() {
        return temperature_;
      }
      /**
       * <code>float temperature = 3;</code>
       * @param value The temperature to set.
       * @return This builder for chaining.
       */
      public Builder setTemperature(float value) {
        bitField0_ |= 0x00000002;
        temperature_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>float temperature = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemperature() {
        bitField0_ = (bitField0_ & ~0x00000002);
        temperature_ = 0F;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:CellTemperature)
    }

    // @@protoc_insertion_point(class_scope:CellTemperature)
    private static final com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature();
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CellTemperature>
        PARSER = new com.google.protobuf.AbstractParser<CellTemperature>() {
      @java.lang.Override
      public CellTemperature parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CellTemperature(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CellTemperature> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CellTemperature> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellTemperature getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface VoltageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Voltage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .CellVoltage cell_voltages = 1;</code>
     */
    java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage> 
        getCellVoltagesList();
    /**
     * <code>repeated .CellVoltage cell_voltages = 1;</code>
     */
    com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage getCellVoltages(int index);
    /**
     * <code>repeated .CellVoltage cell_voltages = 1;</code>
     */
    int getCellVoltagesCount();
    /**
     * <code>repeated .CellVoltage cell_voltages = 1;</code>
     */
    java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltageOrBuilder> 
        getCellVoltagesOrBuilderList();
    /**
     * <code>repeated .CellVoltage cell_voltages = 1;</code>
     */
    com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltageOrBuilder getCellVoltagesOrBuilder(
        int index);

    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();
  }
  /**
   * Protobuf type {@code Voltage}
   */
  public static final class Voltage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Voltage)
      VoltageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Voltage.newBuilder() to construct.
    private Voltage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Voltage() {
      cellVoltages_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Voltage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Voltage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                cellVoltages_ = new java.util.ArrayList<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage>();
                mutable_bitField0_ |= 0x00000001;
              }
              cellVoltages_.add(
                  input.readMessage(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.parser(), extensionRegistry));
              break;
            }
            case 18: {
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder subBuilder = null;
              if (timestamp_ != null) {
                subBuilder = timestamp_.toBuilder();
              }
              timestamp_ = input.readMessage(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(timestamp_);
                timestamp_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder subBuilder = null;
              if (metadata_ != null) {
                subBuilder = metadata_.toBuilder();
              }
              metadata_ = input.readMessage(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(metadata_);
                metadata_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          cellVoltages_ = java.util.Collections.unmodifiableList(cellVoltages_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Voltage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Voltage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage.Builder.class);
    }

    public static final int CELL_VOLTAGES_FIELD_NUMBER = 1;
    private java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage> cellVoltages_;
    /**
     * <code>repeated .CellVoltage cell_voltages = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage> getCellVoltagesList() {
      return cellVoltages_;
    }
    /**
     * <code>repeated .CellVoltage cell_voltages = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltageOrBuilder> 
        getCellVoltagesOrBuilderList() {
      return cellVoltages_;
    }
    /**
     * <code>repeated .CellVoltage cell_voltages = 1;</code>
     */
    @java.lang.Override
    public int getCellVoltagesCount() {
      return cellVoltages_.size();
    }
    /**
     * <code>repeated .CellVoltage cell_voltages = 1;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage getCellVoltages(int index) {
      return cellVoltages_.get(index);
    }
    /**
     * <code>repeated .CellVoltage cell_voltages = 1;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltageOrBuilder getCellVoltagesOrBuilder(
        int index) {
      return cellVoltages_.get(index);
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 2;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return timestamp_ != null;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return getTimestamp();
    }

    public static final int METADATA_FIELD_NUMBER = 3;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return metadata_ != null;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 3;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return getMetadata();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < cellVoltages_.size(); i++) {
        output.writeMessage(1, cellVoltages_.get(i));
      }
      if (timestamp_ != null) {
        output.writeMessage(2, getTimestamp());
      }
      if (metadata_ != null) {
        output.writeMessage(3, getMetadata());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < cellVoltages_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, cellVoltages_.get(i));
      }
      if (timestamp_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getTimestamp());
      }
      if (metadata_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getMetadata());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage other = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage) obj;

      if (!getCellVoltagesList()
          .equals(other.getCellVoltagesList())) return false;
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getCellVoltagesCount() > 0) {
        hash = (37 * hash) + CELL_VOLTAGES_FIELD_NUMBER;
        hash = (53 * hash) + getCellVoltagesList().hashCode();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Voltage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Voltage)
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.VoltageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Voltage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Voltage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCellVoltagesFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (cellVoltagesBuilder_ == null) {
          cellVoltages_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          cellVoltagesBuilder_.clear();
        }
        if (timestampBuilder_ == null) {
          timestamp_ = null;
        } else {
          timestamp_ = null;
          timestampBuilder_ = null;
        }
        if (metadataBuilder_ == null) {
          metadata_ = null;
        } else {
          metadata_ = null;
          metadataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Voltage_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage build() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage buildPartial() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage result = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage(this);
        int from_bitField0_ = bitField0_;
        if (cellVoltagesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            cellVoltages_ = java.util.Collections.unmodifiableList(cellVoltages_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.cellVoltages_ = cellVoltages_;
        } else {
          result.cellVoltages_ = cellVoltagesBuilder_.build();
        }
        if (timestampBuilder_ == null) {
          result.timestamp_ = timestamp_;
        } else {
          result.timestamp_ = timestampBuilder_.build();
        }
        if (metadataBuilder_ == null) {
          result.metadata_ = metadata_;
        } else {
          result.metadata_ = metadataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage other) {
        if (other == com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage.getDefaultInstance()) return this;
        if (cellVoltagesBuilder_ == null) {
          if (!other.cellVoltages_.isEmpty()) {
            if (cellVoltages_.isEmpty()) {
              cellVoltages_ = other.cellVoltages_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureCellVoltagesIsMutable();
              cellVoltages_.addAll(other.cellVoltages_);
            }
            onChanged();
          }
        } else {
          if (!other.cellVoltages_.isEmpty()) {
            if (cellVoltagesBuilder_.isEmpty()) {
              cellVoltagesBuilder_.dispose();
              cellVoltagesBuilder_ = null;
              cellVoltages_ = other.cellVoltages_;
              bitField0_ = (bitField0_ & ~0x00000001);
              cellVoltagesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getCellVoltagesFieldBuilder() : null;
            } else {
              cellVoltagesBuilder_.addAllMessages(other.cellVoltages_);
            }
          }
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage> cellVoltages_ =
        java.util.Collections.emptyList();
      private void ensureCellVoltagesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          cellVoltages_ = new java.util.ArrayList<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage>(cellVoltages_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltageOrBuilder> cellVoltagesBuilder_;

      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage> getCellVoltagesList() {
        if (cellVoltagesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(cellVoltages_);
        } else {
          return cellVoltagesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public int getCellVoltagesCount() {
        if (cellVoltagesBuilder_ == null) {
          return cellVoltages_.size();
        } else {
          return cellVoltagesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage getCellVoltages(int index) {
        if (cellVoltagesBuilder_ == null) {
          return cellVoltages_.get(index);
        } else {
          return cellVoltagesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public Builder setCellVoltages(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage value) {
        if (cellVoltagesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCellVoltagesIsMutable();
          cellVoltages_.set(index, value);
          onChanged();
        } else {
          cellVoltagesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public Builder setCellVoltages(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder builderForValue) {
        if (cellVoltagesBuilder_ == null) {
          ensureCellVoltagesIsMutable();
          cellVoltages_.set(index, builderForValue.build());
          onChanged();
        } else {
          cellVoltagesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public Builder addCellVoltages(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage value) {
        if (cellVoltagesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCellVoltagesIsMutable();
          cellVoltages_.add(value);
          onChanged();
        } else {
          cellVoltagesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public Builder addCellVoltages(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage value) {
        if (cellVoltagesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCellVoltagesIsMutable();
          cellVoltages_.add(index, value);
          onChanged();
        } else {
          cellVoltagesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public Builder addCellVoltages(
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder builderForValue) {
        if (cellVoltagesBuilder_ == null) {
          ensureCellVoltagesIsMutable();
          cellVoltages_.add(builderForValue.build());
          onChanged();
        } else {
          cellVoltagesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public Builder addCellVoltages(
          int index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder builderForValue) {
        if (cellVoltagesBuilder_ == null) {
          ensureCellVoltagesIsMutable();
          cellVoltages_.add(index, builderForValue.build());
          onChanged();
        } else {
          cellVoltagesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public Builder addAllCellVoltages(
          java.lang.Iterable<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage> values) {
        if (cellVoltagesBuilder_ == null) {
          ensureCellVoltagesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, cellVoltages_);
          onChanged();
        } else {
          cellVoltagesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public Builder clearCellVoltages() {
        if (cellVoltagesBuilder_ == null) {
          cellVoltages_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          cellVoltagesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public Builder removeCellVoltages(int index) {
        if (cellVoltagesBuilder_ == null) {
          ensureCellVoltagesIsMutable();
          cellVoltages_.remove(index);
          onChanged();
        } else {
          cellVoltagesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder getCellVoltagesBuilder(
          int index) {
        return getCellVoltagesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltageOrBuilder getCellVoltagesOrBuilder(
          int index) {
        if (cellVoltagesBuilder_ == null) {
          return cellVoltages_.get(index);  } else {
          return cellVoltagesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public java.util.List<? extends com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltageOrBuilder> 
           getCellVoltagesOrBuilderList() {
        if (cellVoltagesBuilder_ != null) {
          return cellVoltagesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(cellVoltages_);
        }
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder addCellVoltagesBuilder() {
        return getCellVoltagesFieldBuilder().addBuilder(
            com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.getDefaultInstance());
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder addCellVoltagesBuilder(
          int index) {
        return getCellVoltagesFieldBuilder().addBuilder(
            index, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.getDefaultInstance());
      }
      /**
       * <code>repeated .CellVoltage cell_voltages = 1;</code>
       */
      public java.util.List<com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder> 
           getCellVoltagesBuilderList() {
        return getCellVoltagesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltageOrBuilder> 
          getCellVoltagesFieldBuilder() {
        if (cellVoltagesBuilder_ == null) {
          cellVoltagesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltageOrBuilder>(
                  cellVoltages_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          cellVoltages_ = null;
        }
        return cellVoltagesBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return timestampBuilder_ != null || timestamp_ != null;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
          onChanged();
        } else {
          timestampBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
          onChanged();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (timestamp_ != null) {
            timestamp_ =
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.newBuilder(timestamp_).mergeFrom(value).buildPartial();
          } else {
            timestamp_ = value;
          }
          onChanged();
        } else {
          timestampBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder clearTimestamp() {
        if (timestampBuilder_ == null) {
          timestamp_ = null;
          onChanged();
        } else {
          timestamp_ = null;
          timestampBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return metadataBuilder_ != null || metadata_ != null;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
          onChanged();
        } else {
          metadataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
          onChanged();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (metadata_ != null) {
            metadata_ =
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.newBuilder(metadata_).mergeFrom(value).buildPartial();
          } else {
            metadata_ = value;
          }
          onChanged();
        } else {
          metadataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public Builder clearMetadata() {
        if (metadataBuilder_ == null) {
          metadata_ = null;
          onChanged();
        } else {
          metadata_ = null;
          metadataBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Voltage)
    }

    // @@protoc_insertion_point(class_scope:Voltage)
    private static final com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage();
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Voltage>
        PARSER = new com.google.protobuf.AbstractParser<Voltage>() {
      @java.lang.Override
      public Voltage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Voltage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Voltage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Voltage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Voltage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CellVoltageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:CellVoltage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 cell_id = 1;</code>
     * @return Whether the cellId field is set.
     */
    boolean hasCellId();
    /**
     * <code>uint32 cell_id = 1;</code>
     * @return The cellId.
     */
    int getCellId();

    /**
     * <code>float volts = 3;</code>
     * @return Whether the volts field is set.
     */
    boolean hasVolts();
    /**
     * <code>float volts = 3;</code>
     * @return The volts.
     */
    float getVolts();
  }
  /**
   * Protobuf type {@code CellVoltage}
   */
  public static final class CellVoltage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:CellVoltage)
      CellVoltageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CellVoltage.newBuilder() to construct.
    private CellVoltage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CellVoltage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CellVoltage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CellVoltage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              cellId_ = input.readUInt32();
              break;
            }
            case 29: {
              bitField0_ |= 0x00000002;
              volts_ = input.readFloat();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_CellVoltage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_CellVoltage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder.class);
    }

    private int bitField0_;
    public static final int CELL_ID_FIELD_NUMBER = 1;
    private int cellId_;
    /**
     * <code>uint32 cell_id = 1;</code>
     * @return Whether the cellId field is set.
     */
    @java.lang.Override
    public boolean hasCellId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>uint32 cell_id = 1;</code>
     * @return The cellId.
     */
    @java.lang.Override
    public int getCellId() {
      return cellId_;
    }

    public static final int VOLTS_FIELD_NUMBER = 3;
    private float volts_;
    /**
     * <code>float volts = 3;</code>
     * @return Whether the volts field is set.
     */
    @java.lang.Override
    public boolean hasVolts() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>float volts = 3;</code>
     * @return The volts.
     */
    @java.lang.Override
    public float getVolts() {
      return volts_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(1, cellId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeFloat(3, volts_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, cellId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(3, volts_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage other = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage) obj;

      if (hasCellId() != other.hasCellId()) return false;
      if (hasCellId()) {
        if (getCellId()
            != other.getCellId()) return false;
      }
      if (hasVolts() != other.hasVolts()) return false;
      if (hasVolts()) {
        if (java.lang.Float.floatToIntBits(getVolts())
            != java.lang.Float.floatToIntBits(
                other.getVolts())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCellId()) {
        hash = (37 * hash) + CELL_ID_FIELD_NUMBER;
        hash = (53 * hash) + getCellId();
      }
      if (hasVolts()) {
        hash = (37 * hash) + VOLTS_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getVolts());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code CellVoltage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:CellVoltage)
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_CellVoltage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_CellVoltage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        cellId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        volts_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_CellVoltage_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage build() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage buildPartial() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage result = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.cellId_ = cellId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.volts_ = volts_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage other) {
        if (other == com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage.getDefaultInstance()) return this;
        if (other.hasCellId()) {
          setCellId(other.getCellId());
        }
        if (other.hasVolts()) {
          setVolts(other.getVolts());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int cellId_ ;
      /**
       * <code>uint32 cell_id = 1;</code>
       * @return Whether the cellId field is set.
       */
      @java.lang.Override
      public boolean hasCellId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>uint32 cell_id = 1;</code>
       * @return The cellId.
       */
      @java.lang.Override
      public int getCellId() {
        return cellId_;
      }
      /**
       * <code>uint32 cell_id = 1;</code>
       * @param value The cellId to set.
       * @return This builder for chaining.
       */
      public Builder setCellId(int value) {
        bitField0_ |= 0x00000001;
        cellId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 cell_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCellId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cellId_ = 0;
        onChanged();
        return this;
      }

      private float volts_ ;
      /**
       * <code>float volts = 3;</code>
       * @return Whether the volts field is set.
       */
      @java.lang.Override
      public boolean hasVolts() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>float volts = 3;</code>
       * @return The volts.
       */
      @java.lang.Override
      public float getVolts() {
        return volts_;
      }
      /**
       * <code>float volts = 3;</code>
       * @param value The volts to set.
       * @return This builder for chaining.
       */
      public Builder setVolts(float value) {
        bitField0_ |= 0x00000002;
        volts_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>float volts = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearVolts() {
        bitField0_ = (bitField0_ & ~0x00000002);
        volts_ = 0F;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:CellVoltage)
    }

    // @@protoc_insertion_point(class_scope:CellVoltage)
    private static final com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage();
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CellVoltage>
        PARSER = new com.google.protobuf.AbstractParser<CellVoltage>() {
      @java.lang.Override
      public CellVoltage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CellVoltage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CellVoltage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CellVoltage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.CellVoltage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BalancingStatusOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BalancingStatus)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 cell_id = 1;</code>
     * @return Whether the cellId field is set.
     */
    boolean hasCellId();
    /**
     * <code>uint32 cell_id = 1;</code>
     * @return The cellId.
     */
    int getCellId();

    /**
     * <code>bool isSet = 3;</code>
     * @return Whether the isSet field is set.
     */
    boolean hasIsSet();
    /**
     * <code>bool isSet = 3;</code>
     * @return The isSet.
     */
    boolean getIsSet();
  }
  /**
   * Protobuf type {@code BalancingStatus}
   */
  public static final class BalancingStatus extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BalancingStatus)
      BalancingStatusOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BalancingStatus.newBuilder() to construct.
    private BalancingStatus(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BalancingStatus() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BalancingStatus();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BalancingStatus(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              cellId_ = input.readUInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              isSet_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_BalancingStatus_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_BalancingStatus_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder.class);
    }

    private int bitField0_;
    public static final int CELL_ID_FIELD_NUMBER = 1;
    private int cellId_;
    /**
     * <code>uint32 cell_id = 1;</code>
     * @return Whether the cellId field is set.
     */
    @java.lang.Override
    public boolean hasCellId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>uint32 cell_id = 1;</code>
     * @return The cellId.
     */
    @java.lang.Override
    public int getCellId() {
      return cellId_;
    }

    public static final int ISSET_FIELD_NUMBER = 3;
    private boolean isSet_;
    /**
     * <code>bool isSet = 3;</code>
     * @return Whether the isSet field is set.
     */
    @java.lang.Override
    public boolean hasIsSet() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>bool isSet = 3;</code>
     * @return The isSet.
     */
    @java.lang.Override
    public boolean getIsSet() {
      return isSet_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(1, cellId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(3, isSet_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, cellId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, isSet_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus other = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus) obj;

      if (hasCellId() != other.hasCellId()) return false;
      if (hasCellId()) {
        if (getCellId()
            != other.getCellId()) return false;
      }
      if (hasIsSet() != other.hasIsSet()) return false;
      if (hasIsSet()) {
        if (getIsSet()
            != other.getIsSet()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCellId()) {
        hash = (37 * hash) + CELL_ID_FIELD_NUMBER;
        hash = (53 * hash) + getCellId();
      }
      if (hasIsSet()) {
        hash = (37 * hash) + ISSET_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsSet());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code BalancingStatus}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BalancingStatus)
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatusOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_BalancingStatus_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_BalancingStatus_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        cellId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        isSet_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_BalancingStatus_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus build() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus buildPartial() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus result = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.cellId_ = cellId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isSet_ = isSet_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus other) {
        if (other == com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus.getDefaultInstance()) return this;
        if (other.hasCellId()) {
          setCellId(other.getCellId());
        }
        if (other.hasIsSet()) {
          setIsSet(other.getIsSet());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int cellId_ ;
      /**
       * <code>uint32 cell_id = 1;</code>
       * @return Whether the cellId field is set.
       */
      @java.lang.Override
      public boolean hasCellId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>uint32 cell_id = 1;</code>
       * @return The cellId.
       */
      @java.lang.Override
      public int getCellId() {
        return cellId_;
      }
      /**
       * <code>uint32 cell_id = 1;</code>
       * @param value The cellId to set.
       * @return This builder for chaining.
       */
      public Builder setCellId(int value) {
        bitField0_ |= 0x00000001;
        cellId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 cell_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCellId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cellId_ = 0;
        onChanged();
        return this;
      }

      private boolean isSet_ ;
      /**
       * <code>bool isSet = 3;</code>
       * @return Whether the isSet field is set.
       */
      @java.lang.Override
      public boolean hasIsSet() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>bool isSet = 3;</code>
       * @return The isSet.
       */
      @java.lang.Override
      public boolean getIsSet() {
        return isSet_;
      }
      /**
       * <code>bool isSet = 3;</code>
       * @param value The isSet to set.
       * @return This builder for chaining.
       */
      public Builder setIsSet(boolean value) {
        bitField0_ |= 0x00000002;
        isSet_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool isSet = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsSet() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isSet_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BalancingStatus)
    }

    // @@protoc_insertion_point(class_scope:BalancingStatus)
    private static final com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus();
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BalancingStatus>
        PARSER = new com.google.protobuf.AbstractParser<BalancingStatus>() {
      @java.lang.Override
      public BalancingStatus parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BalancingStatus(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BalancingStatus> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BalancingStatus> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.BalancingStatus getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AlarmOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Alarm)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.AlarmType alarm_type = 1;</code>
     * @return Whether the alarmType field is set.
     */
    boolean hasAlarmType();
    /**
     * <code>.AlarmType alarm_type = 1;</code>
     * @return The enum numeric value on the wire for alarmType.
     */
    int getAlarmTypeValue();
    /**
     * <code>.AlarmType alarm_type = 1;</code>
     * @return The alarmType.
     */
    com.nichesolv.nds.model.proto.model.AlarmTypeProto.AlarmType getAlarmType();

    /**
     * <code>bool isSet = 3;</code>
     * @return Whether the isSet field is set.
     */
    boolean hasIsSet();
    /**
     * <code>bool isSet = 3;</code>
     * @return The isSet.
     */
    boolean getIsSet();
  }
  /**
   * Protobuf type {@code Alarm}
   */
  public static final class Alarm extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Alarm)
      AlarmOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Alarm.newBuilder() to construct.
    private Alarm(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Alarm() {
      alarmType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Alarm();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Alarm(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
              bitField0_ |= 0x00000001;
              alarmType_ = rawValue;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              isSet_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Alarm_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Alarm_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder.class);
    }

    private int bitField0_;
    public static final int ALARM_TYPE_FIELD_NUMBER = 1;
    private int alarmType_;
    /**
     * <code>.AlarmType alarm_type = 1;</code>
     * @return Whether the alarmType field is set.
     */
    @java.lang.Override public boolean hasAlarmType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.AlarmType alarm_type = 1;</code>
     * @return The enum numeric value on the wire for alarmType.
     */
    @java.lang.Override public int getAlarmTypeValue() {
      return alarmType_;
    }
    /**
     * <code>.AlarmType alarm_type = 1;</code>
     * @return The alarmType.
     */
    @java.lang.Override public com.nichesolv.nds.model.proto.model.AlarmTypeProto.AlarmType getAlarmType() {
      @SuppressWarnings("deprecation")
      com.nichesolv.nds.model.proto.model.AlarmTypeProto.AlarmType result = com.nichesolv.nds.model.proto.model.AlarmTypeProto.AlarmType.valueOf(alarmType_);
      return result == null ? com.nichesolv.nds.model.proto.model.AlarmTypeProto.AlarmType.UNRECOGNIZED : result;
    }

    public static final int ISSET_FIELD_NUMBER = 3;
    private boolean isSet_;
    /**
     * <code>bool isSet = 3;</code>
     * @return Whether the isSet field is set.
     */
    @java.lang.Override
    public boolean hasIsSet() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>bool isSet = 3;</code>
     * @return The isSet.
     */
    @java.lang.Override
    public boolean getIsSet() {
      return isSet_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, alarmType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(3, isSet_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, alarmType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, isSet_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm other = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm) obj;

      if (hasAlarmType() != other.hasAlarmType()) return false;
      if (hasAlarmType()) {
        if (alarmType_ != other.alarmType_) return false;
      }
      if (hasIsSet() != other.hasIsSet()) return false;
      if (hasIsSet()) {
        if (getIsSet()
            != other.getIsSet()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAlarmType()) {
        hash = (37 * hash) + ALARM_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + alarmType_;
      }
      if (hasIsSet()) {
        hash = (37 * hash) + ISSET_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsSet());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Alarm}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Alarm)
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.AlarmOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Alarm_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Alarm_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.class, com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        alarmType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        isSet_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.internal_static_Alarm_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm build() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm buildPartial() {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm result = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.alarmType_ = alarmType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isSet_ = isSet_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm other) {
        if (other == com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm.getDefaultInstance()) return this;
        if (other.hasAlarmType()) {
          setAlarmType(other.getAlarmType());
        }
        if (other.hasIsSet()) {
          setIsSet(other.getIsSet());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int alarmType_ = 0;
      /**
       * <code>.AlarmType alarm_type = 1;</code>
       * @return Whether the alarmType field is set.
       */
      @java.lang.Override public boolean hasAlarmType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.AlarmType alarm_type = 1;</code>
       * @return The enum numeric value on the wire for alarmType.
       */
      @java.lang.Override public int getAlarmTypeValue() {
        return alarmType_;
      }
      /**
       * <code>.AlarmType alarm_type = 1;</code>
       * @param value The enum numeric value on the wire for alarmType to set.
       * @return This builder for chaining.
       */
      public Builder setAlarmTypeValue(int value) {
        bitField0_ |= 0x00000001;
        alarmType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.AlarmType alarm_type = 1;</code>
       * @return The alarmType.
       */
      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.AlarmTypeProto.AlarmType getAlarmType() {
        @SuppressWarnings("deprecation")
        com.nichesolv.nds.model.proto.model.AlarmTypeProto.AlarmType result = com.nichesolv.nds.model.proto.model.AlarmTypeProto.AlarmType.valueOf(alarmType_);
        return result == null ? com.nichesolv.nds.model.proto.model.AlarmTypeProto.AlarmType.UNRECOGNIZED : result;
      }
      /**
       * <code>.AlarmType alarm_type = 1;</code>
       * @param value The alarmType to set.
       * @return This builder for chaining.
       */
      public Builder setAlarmType(com.nichesolv.nds.model.proto.model.AlarmTypeProto.AlarmType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        alarmType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.AlarmType alarm_type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAlarmType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        alarmType_ = 0;
        onChanged();
        return this;
      }

      private boolean isSet_ ;
      /**
       * <code>bool isSet = 3;</code>
       * @return Whether the isSet field is set.
       */
      @java.lang.Override
      public boolean hasIsSet() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>bool isSet = 3;</code>
       * @return The isSet.
       */
      @java.lang.Override
      public boolean getIsSet() {
        return isSet_;
      }
      /**
       * <code>bool isSet = 3;</code>
       * @param value The isSet to set.
       * @return This builder for chaining.
       */
      public Builder setIsSet(boolean value) {
        bitField0_ |= 0x00000002;
        isSet_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool isSet = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsSet() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isSet_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Alarm)
    }

    // @@protoc_insertion_point(class_scope:Alarm)
    private static final com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm();
    }

    public static com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Alarm>
        PARSER = new com.google.protobuf.AbstractParser<Alarm>() {
      @java.lang.Override
      public Alarm parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Alarm(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Alarm> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Alarm> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.BatteryManagementSystemProto.Alarm getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BmsMetadata_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BmsMetadata_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Status_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Status_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Temperature_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Temperature_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_CellTemperature_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_CellTemperature_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Voltage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Voltage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_CellVoltage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_CellVoltage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BalancingStatus_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BalancingStatus_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Alarm_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Alarm_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\'com/nichesolv/nds/model/proto/bms.prot" +
      "o\032.com/nichesolv/nds/model/proto/alarm_t" +
      "ype.proto\032.com/nichesolv/nds/model/proto" +
      "/protection.proto\032-com/nichesolv/nds/mod" +
      "el/proto/timestamp.proto\032,com/nichesolv/" +
      "nds/model/proto/metadata.proto\"\301\003\n\013BmsMe" +
      "tadata\022\"\n\025battery_serial_number\030\001 \001(\tH\000\210" +
      "\001\001\022\036\n\021bms_serial_number\030\003 \001(\tH\001\210\001\001\022\035\n\020pr" +
      "otocol_version\030\005 \001(\tH\002\210\001\001\022\033\n\016rated_capac" +
      "ity\030\007 \001(\022H\003\210\001\001\022\030\n\013active_cell\030\t \001(\021H\004\210\001\001" +
      "\022\030\n\013active_temp\030\013 \001(\021H\005\210\001\001\022\020\n\003dod\030\r \001(\021H" +
      "\006\210\001\001\022\"\n\tchemistry\030\017 \001(\0162\n.ChemistryH\007\210\001\001" +
      "\022\035\n\ttimestamp\030\020 \001(\0132\n.Timestamp\022\033\n\010metad" +
      "ata\030\021 \001(\0132\t.MetadataB\030\n\026_battery_serial_" +
      "numberB\024\n\022_bms_serial_numberB\023\n\021_protoco" +
      "l_versionB\021\n\017_rated_capacityB\016\n\014_active_" +
      "cellB\016\n\014_active_tempB\006\n\004_dodB\014\n\n_chemist" +
      "ry\"\247\005\n\006Status\022\032\n\rchgCycleCount\030\001 \001(\021H\000\210\001" +
      "\001\022\032\n\rdsgCycleCount\030\003 \001(\021H\001\210\001\001\022\026\n\006alarms\030" +
      "\005 \003(\0132\006.Alarm\022 \n\013protections\030\007 \003(\0162\013.Pro" +
      "tection\022\030\n\013cellVoltMin\030\t \001(\002H\002\210\001\001\022\030\n\013cel" +
      "lVoltMax\030\013 \001(\002H\003\210\001\001\022\033\n\016temperatureMin\030\r " +
      "\001(\002H\004\210\001\001\022\033\n\016temperatureMax\030\017 \001(\002H\005\210\001\001\022\030\n" +
      "\013batteryVolt\030\021 \001(\002H\006\210\001\001\022\020\n\003soc\030\023 \001(\002H\007\210\001" +
      "\001\022\020\n\003soh\030\025 \001(\002H\010\210\001\001\022\024\n\007current\030\027 \001(\002H\t\210\001" +
      "\001\022+\n\021balancingStatuses\030\031 \003(\0132\020.Balancing" +
      "Status\022\036\n\021remainingCapacity\030\032 \001(\021H\n\210\001\001\022\035" +
      "\n\ttimestamp\030\033 \001(\0132\n.Timestamp\022\033\n\010metadat" +
      "a\030\034 \001(\0132\t.Metadata\022\036\n\021mosfetTemperature\030" +
      "\035 \001(\002H\013\210\001\001B\020\n\016_chgCycleCountB\020\n\016_dsgCycl" +
      "eCountB\016\n\014_cellVoltMinB\016\n\014_cellVoltMaxB\021" +
      "\n\017_temperatureMinB\021\n\017_temperatureMaxB\016\n\014" +
      "_batteryVoltB\006\n\004_socB\006\n\004_sohB\n\n\010_current" +
      "B\024\n\022_remainingCapacityB\024\n\022_mosfetTempera" +
      "ture\"v\n\013Temperature\022+\n\021cell_temperatures" +
      "\030\001 \003(\0132\020.CellTemperature\022\035\n\ttimestamp\030\002 " +
      "\001(\0132\n.Timestamp\022\033\n\010metadata\030\003 \001(\0132\t.Meta" +
      "data\"]\n\017CellTemperature\022\024\n\007cell_id\030\001 \001(\021" +
      "H\000\210\001\001\022\030\n\013temperature\030\003 \001(\002H\001\210\001\001B\n\n\010_cell" +
      "_idB\016\n\014_temperature\"j\n\007Voltage\022#\n\rcell_v" +
      "oltages\030\001 \003(\0132\014.CellVoltage\022\035\n\ttimestamp" +
      "\030\002 \001(\0132\n.Timestamp\022\033\n\010metadata\030\003 \001(\0132\t.M" +
      "etadata\"M\n\013CellVoltage\022\024\n\007cell_id\030\001 \001(\rH" +
      "\000\210\001\001\022\022\n\005volts\030\003 \001(\002H\001\210\001\001B\n\n\010_cell_idB\010\n\006" +
      "_volts\"Q\n\017BalancingStatus\022\024\n\007cell_id\030\001 \001" +
      "(\rH\000\210\001\001\022\022\n\005isSet\030\003 \001(\010H\001\210\001\001B\n\n\010_cell_idB" +
      "\010\n\006_isSet\"Y\n\005Alarm\022#\n\nalarm_type\030\001 \001(\0162\n" +
      ".AlarmTypeH\000\210\001\001\022\022\n\005isSet\030\003 \001(\010H\001\210\001\001B\r\n\013_" +
      "alarm_typeB\010\n\006_isSet*0\n\tChemistry\022\007\n\003NCM" +
      "\020\000\022\007\n\003LFP\020\001\022\021\n\014NO_CHEMISTRY\020\217NBC\n#com.ni" +
      "chesolv.nds.model.proto.modelB\034BatteryMa" +
      "nagementSystemProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.nichesolv.nds.model.proto.model.AlarmTypeProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.ProtectionProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor(),
        });
    internal_static_BmsMetadata_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_BmsMetadata_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BmsMetadata_descriptor,
        new java.lang.String[] { "BatterySerialNumber", "BmsSerialNumber", "ProtocolVersion", "RatedCapacity", "ActiveCell", "ActiveTemp", "Dod", "Chemistry", "Timestamp", "Metadata", "BatterySerialNumber", "BmsSerialNumber", "ProtocolVersion", "RatedCapacity", "ActiveCell", "ActiveTemp", "Dod", "Chemistry", });
    internal_static_Status_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Status_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Status_descriptor,
        new java.lang.String[] { "ChgCycleCount", "DsgCycleCount", "Alarms", "Protections", "CellVoltMin", "CellVoltMax", "TemperatureMin", "TemperatureMax", "BatteryVolt", "Soc", "Soh", "Current", "BalancingStatuses", "RemainingCapacity", "Timestamp", "Metadata", "MosfetTemperature", "ChgCycleCount", "DsgCycleCount", "CellVoltMin", "CellVoltMax", "TemperatureMin", "TemperatureMax", "BatteryVolt", "Soc", "Soh", "Current", "RemainingCapacity", "MosfetTemperature", });
    internal_static_Temperature_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Temperature_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Temperature_descriptor,
        new java.lang.String[] { "CellTemperatures", "Timestamp", "Metadata", });
    internal_static_CellTemperature_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_CellTemperature_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_CellTemperature_descriptor,
        new java.lang.String[] { "CellId", "Temperature", "CellId", "Temperature", });
    internal_static_Voltage_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Voltage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Voltage_descriptor,
        new java.lang.String[] { "CellVoltages", "Timestamp", "Metadata", });
    internal_static_CellVoltage_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_CellVoltage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_CellVoltage_descriptor,
        new java.lang.String[] { "CellId", "Volts", "CellId", "Volts", });
    internal_static_BalancingStatus_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_BalancingStatus_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BalancingStatus_descriptor,
        new java.lang.String[] { "CellId", "IsSet", "CellId", "IsSet", });
    internal_static_Alarm_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Alarm_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Alarm_descriptor,
        new java.lang.String[] { "AlarmType", "IsSet", "AlarmType", "IsSet", });
    com.nichesolv.nds.model.proto.model.AlarmTypeProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.ProtectionProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
