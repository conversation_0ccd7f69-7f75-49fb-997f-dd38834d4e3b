// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/digital_input.proto

package com.nichesolv.nds.model.proto.model;

public final class DigitalInputProto {
  private DigitalInputProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DigitalInputOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DigitalInput)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool usr1 = 1;</code>
     * @return Whether the usr1 field is set.
     */
    boolean hasUsr1();
    /**
     * <code>optional bool usr1 = 1;</code>
     * @return The usr1.
     */
    boolean getUsr1();

    /**
     * <code>optional bool usr2 = 3;</code>
     * @return Whether the usr2 field is set.
     */
    boolean hasUsr2();
    /**
     * <code>optional bool usr2 = 3;</code>
     * @return The usr2.
     */
    boolean getUsr2();

    /**
     * <code>optional bool motion = 5;</code>
     * @return Whether the motion field is set.
     */
    boolean hasMotion();
    /**
     * <code>optional bool motion = 5;</code>
     * @return The motion.
     */
    boolean getMotion();

    /**
     * <code>optional bool tamper = 7;</code>
     * @return Whether the tamper field is set.
     */
    boolean hasTamper();
    /**
     * <code>optional bool tamper = 7;</code>
     * @return The tamper.
     */
    boolean getTamper();

    /**
     * <code>optional bool mainPower = 9;</code>
     * @return Whether the mainPower field is set.
     */
    boolean hasMainPower();
    /**
     * <code>optional bool mainPower = 9;</code>
     * @return The mainPower.
     */
    boolean getMainPower();

    /**
     * <code>optional bool ignition = 11;</code>
     * @return Whether the ignition field is set.
     */
    boolean hasIgnition();
    /**
     * <code>optional bool ignition = 11;</code>
     * @return The ignition.
     */
    boolean getIgnition();

    /**
     * <code>.Timestamp timestamp = 12;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <code>.Timestamp timestamp = 12;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <code>.Timestamp timestamp = 12;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <code>.Metadata metadata = 13;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <code>.Metadata metadata = 13;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <code>.Metadata metadata = 13;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();
  }
  /**
   * <pre>
   * Represents digital input.
   * </pre>
   *
   * Protobuf type {@code DigitalInput}
   */
  public static final class DigitalInput extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DigitalInput)
      DigitalInputOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DigitalInput.newBuilder() to construct.
    private DigitalInput(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DigitalInput() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DigitalInput();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.DigitalInputProto.internal_static_DigitalInput_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.DigitalInputProto.internal_static_DigitalInput_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput.class, com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput.Builder.class);
    }

    private int bitField0_;
    public static final int USR1_FIELD_NUMBER = 1;
    private boolean usr1_ = false;
    /**
     * <code>optional bool usr1 = 1;</code>
     * @return Whether the usr1 field is set.
     */
    @java.lang.Override
    public boolean hasUsr1() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool usr1 = 1;</code>
     * @return The usr1.
     */
    @java.lang.Override
    public boolean getUsr1() {
      return usr1_;
    }

    public static final int USR2_FIELD_NUMBER = 3;
    private boolean usr2_ = false;
    /**
     * <code>optional bool usr2 = 3;</code>
     * @return Whether the usr2 field is set.
     */
    @java.lang.Override
    public boolean hasUsr2() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool usr2 = 3;</code>
     * @return The usr2.
     */
    @java.lang.Override
    public boolean getUsr2() {
      return usr2_;
    }

    public static final int MOTION_FIELD_NUMBER = 5;
    private boolean motion_ = false;
    /**
     * <code>optional bool motion = 5;</code>
     * @return Whether the motion field is set.
     */
    @java.lang.Override
    public boolean hasMotion() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool motion = 5;</code>
     * @return The motion.
     */
    @java.lang.Override
    public boolean getMotion() {
      return motion_;
    }

    public static final int TAMPER_FIELD_NUMBER = 7;
    private boolean tamper_ = false;
    /**
     * <code>optional bool tamper = 7;</code>
     * @return Whether the tamper field is set.
     */
    @java.lang.Override
    public boolean hasTamper() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bool tamper = 7;</code>
     * @return The tamper.
     */
    @java.lang.Override
    public boolean getTamper() {
      return tamper_;
    }

    public static final int MAINPOWER_FIELD_NUMBER = 9;
    private boolean mainPower_ = false;
    /**
     * <code>optional bool mainPower = 9;</code>
     * @return Whether the mainPower field is set.
     */
    @java.lang.Override
    public boolean hasMainPower() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool mainPower = 9;</code>
     * @return The mainPower.
     */
    @java.lang.Override
    public boolean getMainPower() {
      return mainPower_;
    }

    public static final int IGNITION_FIELD_NUMBER = 11;
    private boolean ignition_ = false;
    /**
     * <code>optional bool ignition = 11;</code>
     * @return Whether the ignition field is set.
     */
    @java.lang.Override
    public boolean hasIgnition() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bool ignition = 11;</code>
     * @return The ignition.
     */
    @java.lang.Override
    public boolean getIgnition() {
      return ignition_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 12;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <code>.Timestamp timestamp = 12;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>.Timestamp timestamp = 12;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <code>.Timestamp timestamp = 12;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }

    public static final int METADATA_FIELD_NUMBER = 13;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <code>.Metadata metadata = 13;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>.Metadata metadata = 13;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <code>.Metadata metadata = 13;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, usr1_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(3, usr2_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBool(5, motion_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBool(7, tamper_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBool(9, mainPower_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBool(11, ignition_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(12, getTimestamp());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeMessage(13, getMetadata());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, usr1_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, usr2_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, motion_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(7, tamper_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(9, mainPower_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(11, ignition_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, getTimestamp());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, getMetadata());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput other = (com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput) obj;

      if (hasUsr1() != other.hasUsr1()) return false;
      if (hasUsr1()) {
        if (getUsr1()
            != other.getUsr1()) return false;
      }
      if (hasUsr2() != other.hasUsr2()) return false;
      if (hasUsr2()) {
        if (getUsr2()
            != other.getUsr2()) return false;
      }
      if (hasMotion() != other.hasMotion()) return false;
      if (hasMotion()) {
        if (getMotion()
            != other.getMotion()) return false;
      }
      if (hasTamper() != other.hasTamper()) return false;
      if (hasTamper()) {
        if (getTamper()
            != other.getTamper()) return false;
      }
      if (hasMainPower() != other.hasMainPower()) return false;
      if (hasMainPower()) {
        if (getMainPower()
            != other.getMainPower()) return false;
      }
      if (hasIgnition() != other.hasIgnition()) return false;
      if (hasIgnition()) {
        if (getIgnition()
            != other.getIgnition()) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUsr1()) {
        hash = (37 * hash) + USR1_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getUsr1());
      }
      if (hasUsr2()) {
        hash = (37 * hash) + USR2_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getUsr2());
      }
      if (hasMotion()) {
        hash = (37 * hash) + MOTION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getMotion());
      }
      if (hasTamper()) {
        hash = (37 * hash) + TAMPER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getTamper());
      }
      if (hasMainPower()) {
        hash = (37 * hash) + MAINPOWER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getMainPower());
      }
      if (hasIgnition()) {
        hash = (37 * hash) + IGNITION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIgnition());
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Represents digital input.
     * </pre>
     *
     * Protobuf type {@code DigitalInput}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DigitalInput)
        com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInputOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.DigitalInputProto.internal_static_DigitalInput_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.DigitalInputProto.internal_static_DigitalInput_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput.class, com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTimestampFieldBuilder();
          getMetadataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        usr1_ = false;
        usr2_ = false;
        motion_ = false;
        tamper_ = false;
        mainPower_ = false;
        ignition_ = false;
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.DigitalInputProto.internal_static_DigitalInput_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput build() {
        com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput buildPartial() {
        com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput result = new com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.usr1_ = usr1_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.usr2_ = usr2_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.motion_ = motion_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.tamper_ = tamper_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.mainPower_ = mainPower_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.ignition_ = ignition_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.timestamp_ = timestampBuilder_ == null
              ? timestamp_
              : timestampBuilder_.build();
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.metadata_ = metadataBuilder_ == null
              ? metadata_
              : metadataBuilder_.build();
          to_bitField0_ |= 0x00000080;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput other) {
        if (other == com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput.getDefaultInstance()) return this;
        if (other.hasUsr1()) {
          setUsr1(other.getUsr1());
        }
        if (other.hasUsr2()) {
          setUsr2(other.getUsr2());
        }
        if (other.hasMotion()) {
          setMotion(other.getMotion());
        }
        if (other.hasTamper()) {
          setTamper(other.getTamper());
        }
        if (other.hasMainPower()) {
          setMainPower(other.getMainPower());
        }
        if (other.hasIgnition()) {
          setIgnition(other.getIgnition());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                usr1_ = input.readBool();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 24: {
                usr2_ = input.readBool();
                bitField0_ |= 0x00000002;
                break;
              } // case 24
              case 40: {
                motion_ = input.readBool();
                bitField0_ |= 0x00000004;
                break;
              } // case 40
              case 56: {
                tamper_ = input.readBool();
                bitField0_ |= 0x00000008;
                break;
              } // case 56
              case 72: {
                mainPower_ = input.readBool();
                bitField0_ |= 0x00000010;
                break;
              } // case 72
              case 88: {
                ignition_ = input.readBool();
                bitField0_ |= 0x00000020;
                break;
              } // case 88
              case 98: {
                input.readMessage(
                    getTimestampFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000040;
                break;
              } // case 98
              case 106: {
                input.readMessage(
                    getMetadataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000080;
                break;
              } // case 106
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private boolean usr1_ ;
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return Whether the usr1 field is set.
       */
      @java.lang.Override
      public boolean hasUsr1() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return The usr1.
       */
      @java.lang.Override
      public boolean getUsr1() {
        return usr1_;
      }
      /**
       * <code>optional bool usr1 = 1;</code>
       * @param value The usr1 to set.
       * @return This builder for chaining.
       */
      public Builder setUsr1(boolean value) {

        usr1_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUsr1() {
        bitField0_ = (bitField0_ & ~0x00000001);
        usr1_ = false;
        onChanged();
        return this;
      }

      private boolean usr2_ ;
      /**
       * <code>optional bool usr2 = 3;</code>
       * @return Whether the usr2 field is set.
       */
      @java.lang.Override
      public boolean hasUsr2() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bool usr2 = 3;</code>
       * @return The usr2.
       */
      @java.lang.Override
      public boolean getUsr2() {
        return usr2_;
      }
      /**
       * <code>optional bool usr2 = 3;</code>
       * @param value The usr2 to set.
       * @return This builder for chaining.
       */
      public Builder setUsr2(boolean value) {

        usr2_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool usr2 = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUsr2() {
        bitField0_ = (bitField0_ & ~0x00000002);
        usr2_ = false;
        onChanged();
        return this;
      }

      private boolean motion_ ;
      /**
       * <code>optional bool motion = 5;</code>
       * @return Whether the motion field is set.
       */
      @java.lang.Override
      public boolean hasMotion() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool motion = 5;</code>
       * @return The motion.
       */
      @java.lang.Override
      public boolean getMotion() {
        return motion_;
      }
      /**
       * <code>optional bool motion = 5;</code>
       * @param value The motion to set.
       * @return This builder for chaining.
       */
      public Builder setMotion(boolean value) {

        motion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool motion = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearMotion() {
        bitField0_ = (bitField0_ & ~0x00000004);
        motion_ = false;
        onChanged();
        return this;
      }

      private boolean tamper_ ;
      /**
       * <code>optional bool tamper = 7;</code>
       * @return Whether the tamper field is set.
       */
      @java.lang.Override
      public boolean hasTamper() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bool tamper = 7;</code>
       * @return The tamper.
       */
      @java.lang.Override
      public boolean getTamper() {
        return tamper_;
      }
      /**
       * <code>optional bool tamper = 7;</code>
       * @param value The tamper to set.
       * @return This builder for chaining.
       */
      public Builder setTamper(boolean value) {

        tamper_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool tamper = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearTamper() {
        bitField0_ = (bitField0_ & ~0x00000008);
        tamper_ = false;
        onChanged();
        return this;
      }

      private boolean mainPower_ ;
      /**
       * <code>optional bool mainPower = 9;</code>
       * @return Whether the mainPower field is set.
       */
      @java.lang.Override
      public boolean hasMainPower() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bool mainPower = 9;</code>
       * @return The mainPower.
       */
      @java.lang.Override
      public boolean getMainPower() {
        return mainPower_;
      }
      /**
       * <code>optional bool mainPower = 9;</code>
       * @param value The mainPower to set.
       * @return This builder for chaining.
       */
      public Builder setMainPower(boolean value) {

        mainPower_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool mainPower = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearMainPower() {
        bitField0_ = (bitField0_ & ~0x00000010);
        mainPower_ = false;
        onChanged();
        return this;
      }

      private boolean ignition_ ;
      /**
       * <code>optional bool ignition = 11;</code>
       * @return Whether the ignition field is set.
       */
      @java.lang.Override
      public boolean hasIgnition() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bool ignition = 11;</code>
       * @return The ignition.
       */
      @java.lang.Override
      public boolean getIgnition() {
        return ignition_;
      }
      /**
       * <code>optional bool ignition = 11;</code>
       * @param value The ignition to set.
       * @return This builder for chaining.
       */
      public Builder setIgnition(boolean value) {

        ignition_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool ignition = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearIgnition() {
        bitField0_ = (bitField0_ & ~0x00000020);
        ignition_ = false;
        onChanged();
        return this;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <code>.Timestamp timestamp = 12;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>.Timestamp timestamp = 12;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <code>.Timestamp timestamp = 12;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
        } else {
          timestampBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 12;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 12;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
            timestamp_ != null &&
            timestamp_ != com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance()) {
            getTimestampBuilder().mergeFrom(value);
          } else {
            timestamp_ = value;
          }
        } else {
          timestampBuilder_.mergeFrom(value);
        }
        if (timestamp_ != null) {
          bitField0_ |= 0x00000040;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 12;</code>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000040);
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 12;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <code>.Timestamp timestamp = 12;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <code>.Timestamp timestamp = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <code>.Metadata metadata = 13;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>.Metadata metadata = 13;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Metadata metadata = 13;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
        } else {
          metadataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 13;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 13;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0) &&
            metadata_ != null &&
            metadata_ != com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance()) {
            getMetadataBuilder().mergeFrom(value);
          } else {
            metadata_ = value;
          }
        } else {
          metadataBuilder_.mergeFrom(value);
        }
        if (metadata_ != null) {
          bitField0_ |= 0x00000080;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Metadata metadata = 13;</code>
       */
      public Builder clearMetadata() {
        bitField0_ = (bitField0_ & ~0x00000080);
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 13;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        bitField0_ |= 0x00000080;
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Metadata metadata = 13;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <code>.Metadata metadata = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DigitalInput)
    }

    // @@protoc_insertion_point(class_scope:DigitalInput)
    private static final com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput();
    }

    public static com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DigitalInput>
        PARSER = new com.google.protobuf.AbstractParser<DigitalInput>() {
      @java.lang.Override
      public DigitalInput parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DigitalInput> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DigitalInput> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.DigitalInputProto.DigitalInput getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DigitalInput_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DigitalInput_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n1com/nichesolv/nds/model/proto/digital_" +
      "input.proto\032-com/nichesolv/nds/model/pro" +
      "to/timestamp.proto\032,com/nichesolv/nds/mo" +
      "del/proto/metadata.proto\"\214\002\n\014DigitalInpu" +
      "t\022\021\n\004usr1\030\001 \001(\010H\000\210\001\001\022\021\n\004usr2\030\003 \001(\010H\001\210\001\001\022" +
      "\023\n\006motion\030\005 \001(\010H\002\210\001\001\022\023\n\006tamper\030\007 \001(\010H\003\210\001" +
      "\001\022\026\n\tmainPower\030\t \001(\010H\004\210\001\001\022\025\n\010ignition\030\013 " +
      "\001(\010H\005\210\001\001\022\035\n\ttimestamp\030\014 \001(\0132\n.Timestamp\022" +
      "\033\n\010metadata\030\r \001(\0132\t.MetadataB\007\n\005_usr1B\007\n" +
      "\005_usr2B\t\n\007_motionB\t\n\007_tamperB\014\n\n_mainPow" +
      "erB\013\n\t_ignitionB8\n#com.nichesolv.nds.mod" +
      "el.proto.modelB\021DigitalInputProtob\006proto" +
      "3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor(),
        });
    internal_static_DigitalInput_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_DigitalInput_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DigitalInput_descriptor,
        new java.lang.String[] { "Usr1", "Usr2", "Motion", "Tamper", "MainPower", "Ignition", "Timestamp", "Metadata", "Usr1", "Usr2", "Motion", "Tamper", "MainPower", "Ignition", });
    com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
