// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/digital_output.proto

package com.nichesolv.nds.model.proto.model;

public final class DigitalOutputProto {
  private DigitalOutputProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DigitalOutputOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DigitalOutput)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool usr1 = 1;</code>
     * @return Whether the usr1 field is set.
     */
    boolean hasUsr1();
    /**
     * <code>optional bool usr1 = 1;</code>
     * @return The usr1.
     */
    boolean getUsr1();

    /**
     * <code>optional bool usr2 = 3;</code>
     * @return Whether the usr2 field is set.
     */
    boolean hasUsr2();
    /**
     * <code>optional bool usr2 = 3;</code>
     * @return The usr2.
     */
    boolean getUsr2();

    /**
     * <code>.Timestamp timestamp = 4;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <code>.Timestamp timestamp = 4;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <code>.Timestamp timestamp = 4;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <code>.Metadata metadata = 5;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <code>.Metadata metadata = 5;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <code>.Metadata metadata = 5;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();
  }
  /**
   * <pre>
   * Digital output.
   * </pre>
   *
   * Protobuf type {@code DigitalOutput}
   */
  public static final class DigitalOutput extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DigitalOutput)
      DigitalOutputOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DigitalOutput.newBuilder() to construct.
    private DigitalOutput(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DigitalOutput() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DigitalOutput();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.DigitalOutputProto.internal_static_DigitalOutput_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.DigitalOutputProto.internal_static_DigitalOutput_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput.class, com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput.Builder.class);
    }

    private int bitField0_;
    public static final int USR1_FIELD_NUMBER = 1;
    private boolean usr1_ = false;
    /**
     * <code>optional bool usr1 = 1;</code>
     * @return Whether the usr1 field is set.
     */
    @java.lang.Override
    public boolean hasUsr1() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool usr1 = 1;</code>
     * @return The usr1.
     */
    @java.lang.Override
    public boolean getUsr1() {
      return usr1_;
    }

    public static final int USR2_FIELD_NUMBER = 3;
    private boolean usr2_ = false;
    /**
     * <code>optional bool usr2 = 3;</code>
     * @return Whether the usr2 field is set.
     */
    @java.lang.Override
    public boolean hasUsr2() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool usr2 = 3;</code>
     * @return The usr2.
     */
    @java.lang.Override
    public boolean getUsr2() {
      return usr2_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 4;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <code>.Timestamp timestamp = 4;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.Timestamp timestamp = 4;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <code>.Timestamp timestamp = 4;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }

    public static final int METADATA_FIELD_NUMBER = 5;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <code>.Metadata metadata = 5;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>.Metadata metadata = 5;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <code>.Metadata metadata = 5;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, usr1_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(3, usr2_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(4, getTimestamp());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(5, getMetadata());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, usr1_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, usr2_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getTimestamp());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getMetadata());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput other = (com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput) obj;

      if (hasUsr1() != other.hasUsr1()) return false;
      if (hasUsr1()) {
        if (getUsr1()
            != other.getUsr1()) return false;
      }
      if (hasUsr2() != other.hasUsr2()) return false;
      if (hasUsr2()) {
        if (getUsr2()
            != other.getUsr2()) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUsr1()) {
        hash = (37 * hash) + USR1_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getUsr1());
      }
      if (hasUsr2()) {
        hash = (37 * hash) + USR2_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getUsr2());
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Digital output.
     * </pre>
     *
     * Protobuf type {@code DigitalOutput}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DigitalOutput)
        com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutputOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.DigitalOutputProto.internal_static_DigitalOutput_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.DigitalOutputProto.internal_static_DigitalOutput_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput.class, com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTimestampFieldBuilder();
          getMetadataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        usr1_ = false;
        usr2_ = false;
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.DigitalOutputProto.internal_static_DigitalOutput_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput build() {
        com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput buildPartial() {
        com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput result = new com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.usr1_ = usr1_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.usr2_ = usr2_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.timestamp_ = timestampBuilder_ == null
              ? timestamp_
              : timestampBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.metadata_ = metadataBuilder_ == null
              ? metadata_
              : metadataBuilder_.build();
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput other) {
        if (other == com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput.getDefaultInstance()) return this;
        if (other.hasUsr1()) {
          setUsr1(other.getUsr1());
        }
        if (other.hasUsr2()) {
          setUsr2(other.getUsr2());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                usr1_ = input.readBool();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 24: {
                usr2_ = input.readBool();
                bitField0_ |= 0x00000002;
                break;
              } // case 24
              case 34: {
                input.readMessage(
                    getTimestampFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 34
              case 42: {
                input.readMessage(
                    getMetadataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 42
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private boolean usr1_ ;
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return Whether the usr1 field is set.
       */
      @java.lang.Override
      public boolean hasUsr1() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return The usr1.
       */
      @java.lang.Override
      public boolean getUsr1() {
        return usr1_;
      }
      /**
       * <code>optional bool usr1 = 1;</code>
       * @param value The usr1 to set.
       * @return This builder for chaining.
       */
      public Builder setUsr1(boolean value) {

        usr1_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUsr1() {
        bitField0_ = (bitField0_ & ~0x00000001);
        usr1_ = false;
        onChanged();
        return this;
      }

      private boolean usr2_ ;
      /**
       * <code>optional bool usr2 = 3;</code>
       * @return Whether the usr2 field is set.
       */
      @java.lang.Override
      public boolean hasUsr2() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bool usr2 = 3;</code>
       * @return The usr2.
       */
      @java.lang.Override
      public boolean getUsr2() {
        return usr2_;
      }
      /**
       * <code>optional bool usr2 = 3;</code>
       * @param value The usr2 to set.
       * @return This builder for chaining.
       */
      public Builder setUsr2(boolean value) {

        usr2_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool usr2 = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUsr2() {
        bitField0_ = (bitField0_ & ~0x00000002);
        usr2_ = false;
        onChanged();
        return this;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <code>.Timestamp timestamp = 4;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.Timestamp timestamp = 4;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <code>.Timestamp timestamp = 4;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
        } else {
          timestampBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 4;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 4;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            timestamp_ != null &&
            timestamp_ != com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance()) {
            getTimestampBuilder().mergeFrom(value);
          } else {
            timestamp_ = value;
          }
        } else {
          timestampBuilder_.mergeFrom(value);
        }
        if (timestamp_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 4;</code>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 4;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <code>.Timestamp timestamp = 4;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <code>.Timestamp timestamp = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <code>.Metadata metadata = 5;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>.Metadata metadata = 5;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Metadata metadata = 5;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
        } else {
          metadataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 5;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 5;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            metadata_ != null &&
            metadata_ != com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance()) {
            getMetadataBuilder().mergeFrom(value);
          } else {
            metadata_ = value;
          }
        } else {
          metadataBuilder_.mergeFrom(value);
        }
        if (metadata_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Metadata metadata = 5;</code>
       */
      public Builder clearMetadata() {
        bitField0_ = (bitField0_ & ~0x00000008);
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Metadata metadata = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <code>.Metadata metadata = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DigitalOutput)
    }

    // @@protoc_insertion_point(class_scope:DigitalOutput)
    private static final com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput();
    }

    public static com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DigitalOutput>
        PARSER = new com.google.protobuf.AbstractParser<DigitalOutput>() {
      @java.lang.Override
      public DigitalOutput parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DigitalOutput> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DigitalOutput> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.DigitalOutputProto.DigitalOutput getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DigitalOutput_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DigitalOutput_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n2com/nichesolv/nds/model/proto/digital_" +
      "output.proto\032-com/nichesolv/nds/model/pr" +
      "oto/timestamp.proto\032,com/nichesolv/nds/m" +
      "odel/proto/metadata.proto\"\203\001\n\rDigitalOut" +
      "put\022\021\n\004usr1\030\001 \001(\010H\000\210\001\001\022\021\n\004usr2\030\003 \001(\010H\001\210\001" +
      "\001\022\035\n\ttimestamp\030\004 \001(\0132\n.Timestamp\022\033\n\010meta" +
      "data\030\005 \001(\0132\t.MetadataB\007\n\005_usr1B\007\n\005_usr2B" +
      "9\n#com.nichesolv.nds.model.proto.modelB\022" +
      "DigitalOutputProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor(),
        });
    internal_static_DigitalOutput_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_DigitalOutput_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DigitalOutput_descriptor,
        new java.lang.String[] { "Usr1", "Usr2", "Timestamp", "Metadata", "Usr1", "Usr2", });
    com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
