// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/gravitational_vector.proto

package com.nichesolv.nds.model.proto.model;

public final class GravitationalVectorProto {
  private GravitationalVectorProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface GravitationalVectorOrBuilder extends
      // @@protoc_insertion_point(interface_extends:GravitationalVector)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * x axis.
     * </pre>
     *
     * <code>float x = 1;</code>
     * @return Whether the x field is set.
     */
    boolean hasX();
    /**
     * <pre>
     * x axis.
     * </pre>
     *
     * <code>float x = 1;</code>
     * @return The x.
     */
    float getX();

    /**
     * <pre>
     * y axis.
     * </pre>
     *
     * <code>float y = 2;</code>
     * @return Whether the y field is set.
     */
    boolean hasY();
    /**
     * <pre>
     * y axis.
     * </pre>
     *
     * <code>float y = 2;</code>
     * @return The y.
     */
    float getY();

    /**
     * <pre>
     * z axis.
     * </pre>
     *
     * <code>float z = 3;</code>
     * @return Whether the z field is set.
     */
    boolean hasZ();
    /**
     * <pre>
     * z axis.
     * </pre>
     *
     * <code>float z = 3;</code>
     * @return The z.
     */
    float getZ();

    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();
  }
  /**
   * <pre>
   * Represents the GravitationalVector protocol. GravitationalVector data is part of the TCU.
   * </pre>
   *
   * Protobuf type {@code GravitationalVector}
   */
  public static final class GravitationalVector extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:GravitationalVector)
      GravitationalVectorOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GravitationalVector.newBuilder() to construct.
    private GravitationalVector(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GravitationalVector() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GravitationalVector();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GravitationalVector(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 13: {
              bitField0_ |= 0x00000001;
              x_ = input.readFloat();
              break;
            }
            case 21: {
              bitField0_ |= 0x00000002;
              y_ = input.readFloat();
              break;
            }
            case 29: {
              bitField0_ |= 0x00000004;
              z_ = input.readFloat();
              break;
            }
            case 50: {
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder subBuilder = null;
              if (timestamp_ != null) {
                subBuilder = timestamp_.toBuilder();
              }
              timestamp_ = input.readMessage(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(timestamp_);
                timestamp_ = subBuilder.buildPartial();
              }

              break;
            }
            case 58: {
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder subBuilder = null;
              if (metadata_ != null) {
                subBuilder = metadata_.toBuilder();
              }
              metadata_ = input.readMessage(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(metadata_);
                metadata_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.GravitationalVectorProto.internal_static_GravitationalVector_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.GravitationalVectorProto.internal_static_GravitationalVector_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector.class, com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector.Builder.class);
    }

    private int bitField0_;
    public static final int X_FIELD_NUMBER = 1;
    private float x_;
    /**
     * <pre>
     * x axis.
     * </pre>
     *
     * <code>float x = 1;</code>
     * @return Whether the x field is set.
     */
    @java.lang.Override
    public boolean hasX() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * x axis.
     * </pre>
     *
     * <code>float x = 1;</code>
     * @return The x.
     */
    @java.lang.Override
    public float getX() {
      return x_;
    }

    public static final int Y_FIELD_NUMBER = 2;
    private float y_;
    /**
     * <pre>
     * y axis.
     * </pre>
     *
     * <code>float y = 2;</code>
     * @return Whether the y field is set.
     */
    @java.lang.Override
    public boolean hasY() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * y axis.
     * </pre>
     *
     * <code>float y = 2;</code>
     * @return The y.
     */
    @java.lang.Override
    public float getY() {
      return y_;
    }

    public static final int Z_FIELD_NUMBER = 3;
    private float z_;
    /**
     * <pre>
     * z axis.
     * </pre>
     *
     * <code>float z = 3;</code>
     * @return Whether the z field is set.
     */
    @java.lang.Override
    public boolean hasZ() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * z axis.
     * </pre>
     *
     * <code>float z = 3;</code>
     * @return The z.
     */
    @java.lang.Override
    public float getZ() {
      return z_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 6;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return timestamp_ != null;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return getTimestamp();
    }

    public static final int METADATA_FIELD_NUMBER = 7;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return metadata_ != null;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return getMetadata();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeFloat(1, x_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeFloat(2, y_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeFloat(3, z_);
      }
      if (timestamp_ != null) {
        output.writeMessage(6, getTimestamp());
      }
      if (metadata_ != null) {
        output.writeMessage(7, getMetadata());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(1, x_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(2, y_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(3, z_);
      }
      if (timestamp_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getTimestamp());
      }
      if (metadata_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getMetadata());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector other = (com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector) obj;

      if (hasX() != other.hasX()) return false;
      if (hasX()) {
        if (java.lang.Float.floatToIntBits(getX())
            != java.lang.Float.floatToIntBits(
                other.getX())) return false;
      }
      if (hasY() != other.hasY()) return false;
      if (hasY()) {
        if (java.lang.Float.floatToIntBits(getY())
            != java.lang.Float.floatToIntBits(
                other.getY())) return false;
      }
      if (hasZ() != other.hasZ()) return false;
      if (hasZ()) {
        if (java.lang.Float.floatToIntBits(getZ())
            != java.lang.Float.floatToIntBits(
                other.getZ())) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasX()) {
        hash = (37 * hash) + X_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getX());
      }
      if (hasY()) {
        hash = (37 * hash) + Y_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getY());
      }
      if (hasZ()) {
        hash = (37 * hash) + Z_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getZ());
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Represents the GravitationalVector protocol. GravitationalVector data is part of the TCU.
     * </pre>
     *
     * Protobuf type {@code GravitationalVector}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:GravitationalVector)
        com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVectorOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.GravitationalVectorProto.internal_static_GravitationalVector_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.GravitationalVectorProto.internal_static_GravitationalVector_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector.class, com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        x_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000001);
        y_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000002);
        z_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (timestampBuilder_ == null) {
          timestamp_ = null;
        } else {
          timestamp_ = null;
          timestampBuilder_ = null;
        }
        if (metadataBuilder_ == null) {
          metadata_ = null;
        } else {
          metadata_ = null;
          metadataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.GravitationalVectorProto.internal_static_GravitationalVector_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector build() {
        com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector buildPartial() {
        com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector result = new com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.x_ = x_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.y_ = y_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.z_ = z_;
          to_bitField0_ |= 0x00000004;
        }
        if (timestampBuilder_ == null) {
          result.timestamp_ = timestamp_;
        } else {
          result.timestamp_ = timestampBuilder_.build();
        }
        if (metadataBuilder_ == null) {
          result.metadata_ = metadata_;
        } else {
          result.metadata_ = metadataBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector other) {
        if (other == com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector.getDefaultInstance()) return this;
        if (other.hasX()) {
          setX(other.getX());
        }
        if (other.hasY()) {
          setY(other.getY());
        }
        if (other.hasZ()) {
          setZ(other.getZ());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private float x_ ;
      /**
       * <pre>
       * x axis.
       * </pre>
       *
       * <code>float x = 1;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * x axis.
       * </pre>
       *
       * <code>float x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public float getX() {
        return x_;
      }
      /**
       * <pre>
       * x axis.
       * </pre>
       *
       * <code>float x = 1;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(float value) {
        bitField0_ |= 0x00000001;
        x_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * x axis.
       * </pre>
       *
       * <code>float x = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        bitField0_ = (bitField0_ & ~0x00000001);
        x_ = 0F;
        onChanged();
        return this;
      }

      private float y_ ;
      /**
       * <pre>
       * y axis.
       * </pre>
       *
       * <code>float y = 2;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * y axis.
       * </pre>
       *
       * <code>float y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public float getY() {
        return y_;
      }
      /**
       * <pre>
       * y axis.
       * </pre>
       *
       * <code>float y = 2;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(float value) {
        bitField0_ |= 0x00000002;
        y_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * y axis.
       * </pre>
       *
       * <code>float y = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        bitField0_ = (bitField0_ & ~0x00000002);
        y_ = 0F;
        onChanged();
        return this;
      }

      private float z_ ;
      /**
       * <pre>
       * z axis.
       * </pre>
       *
       * <code>float z = 3;</code>
       * @return Whether the z field is set.
       */
      @java.lang.Override
      public boolean hasZ() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * z axis.
       * </pre>
       *
       * <code>float z = 3;</code>
       * @return The z.
       */
      @java.lang.Override
      public float getZ() {
        return z_;
      }
      /**
       * <pre>
       * z axis.
       * </pre>
       *
       * <code>float z = 3;</code>
       * @param value The z to set.
       * @return This builder for chaining.
       */
      public Builder setZ(float value) {
        bitField0_ |= 0x00000004;
        z_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * z axis.
       * </pre>
       *
       * <code>float z = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearZ() {
        bitField0_ = (bitField0_ & ~0x00000004);
        z_ = 0F;
        onChanged();
        return this;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return timestampBuilder_ != null || timestamp_ != null;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
          onChanged();
        } else {
          timestampBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
          onChanged();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (timestamp_ != null) {
            timestamp_ =
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.newBuilder(timestamp_).mergeFrom(value).buildPartial();
          } else {
            timestamp_ = value;
          }
          onChanged();
        } else {
          timestampBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public Builder clearTimestamp() {
        if (timestampBuilder_ == null) {
          timestamp_ = null;
          onChanged();
        } else {
          timestamp_ = null;
          timestampBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return metadataBuilder_ != null || metadata_ != null;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
          onChanged();
        } else {
          metadataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
          onChanged();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (metadata_ != null) {
            metadata_ =
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.newBuilder(metadata_).mergeFrom(value).buildPartial();
          } else {
            metadata_ = value;
          }
          onChanged();
        } else {
          metadataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public Builder clearMetadata() {
        if (metadataBuilder_ == null) {
          metadata_ = null;
          onChanged();
        } else {
          metadata_ = null;
          metadataBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:GravitationalVector)
    }

    // @@protoc_insertion_point(class_scope:GravitationalVector)
    private static final com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector();
    }

    public static com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GravitationalVector>
        PARSER = new com.google.protobuf.AbstractParser<GravitationalVector>() {
      @java.lang.Override
      public GravitationalVector parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GravitationalVector(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GravitationalVector> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GravitationalVector> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.GravitationalVectorProto.GravitationalVector getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_GravitationalVector_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_GravitationalVector_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n8com/nichesolv/nds/model/proto/gravitat" +
      "ional_vector.proto\032-com/nichesolv/nds/mo" +
      "del/proto/timestamp.proto\032,com/nichesolv" +
      "/nds/model/proto/metadata.proto\"\223\001\n\023Grav" +
      "itationalVector\022\016\n\001x\030\001 \001(\002H\000\210\001\001\022\016\n\001y\030\002 \001" +
      "(\002H\001\210\001\001\022\016\n\001z\030\003 \001(\002H\002\210\001\001\022\035\n\ttimestamp\030\006 \001" +
      "(\0132\n.Timestamp\022\033\n\010metadata\030\007 \001(\0132\t.Metad" +
      "ataB\004\n\002_xB\004\n\002_yB\004\n\002_zB?\n#com.nichesolv.n" +
      "ds.model.proto.modelB\030GravitationalVecto" +
      "rProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor(),
        });
    internal_static_GravitationalVector_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_GravitationalVector_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_GravitationalVector_descriptor,
        new java.lang.String[] { "X", "Y", "Z", "Timestamp", "Metadata", "X", "Y", "Z", });
    com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
