// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/gyroscope.proto

// Protobuf Java Version: 3.25.0
package com.nichesolv.nds.model.proto.model;

public final class GyroscopeProto {
  private GyroscopeProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface GyroscopeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Gyroscope)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * x axis.
     * </pre>
     *
     * <code>optional sint32 x = 1;</code>
     * @return Whether the x field is set.
     */
    boolean hasX();
    /**
     * <pre>
     * x axis.
     * </pre>
     *
     * <code>optional sint32 x = 1;</code>
     * @return The x.
     */
    int getX();

    /**
     * <pre>
     * y axis.
     * </pre>
     *
     * <code>optional sint32 y = 2;</code>
     * @return Whether the y field is set.
     */
    boolean hasY();
    /**
     * <pre>
     * y axis.
     * </pre>
     *
     * <code>optional sint32 y = 2;</code>
     * @return The y.
     */
    int getY();

    /**
     * <pre>
     * z axis.
     * </pre>
     *
     * <code>optional sint32 z = 3;</code>
     * @return Whether the z field is set.
     */
    boolean hasZ();
    /**
     * <pre>
     * z axis.
     * </pre>
     *
     * <code>optional sint32 z = 3;</code>
     * @return The z.
     */
    int getZ();

    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();
  }
  /**
   * <pre>
   * Represents the Gyroscope protocol. Gyroscope data is part of the TCU.
   * </pre>
   *
   * Protobuf type {@code Gyroscope}
   */
  public static final class Gyroscope extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Gyroscope)
      GyroscopeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Gyroscope.newBuilder() to construct.
    private Gyroscope(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Gyroscope() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Gyroscope();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.GyroscopeProto.internal_static_Gyroscope_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.GyroscopeProto.internal_static_Gyroscope_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope.class, com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope.Builder.class);
    }

    private int bitField0_;
    public static final int X_FIELD_NUMBER = 1;
    private int x_ = 0;
    /**
     * <pre>
     * x axis.
     * </pre>
     *
     * <code>optional sint32 x = 1;</code>
     * @return Whether the x field is set.
     */
    @java.lang.Override
    public boolean hasX() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * x axis.
     * </pre>
     *
     * <code>optional sint32 x = 1;</code>
     * @return The x.
     */
    @java.lang.Override
    public int getX() {
      return x_;
    }

    public static final int Y_FIELD_NUMBER = 2;
    private int y_ = 0;
    /**
     * <pre>
     * y axis.
     * </pre>
     *
     * <code>optional sint32 y = 2;</code>
     * @return Whether the y field is set.
     */
    @java.lang.Override
    public boolean hasY() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * y axis.
     * </pre>
     *
     * <code>optional sint32 y = 2;</code>
     * @return The y.
     */
    @java.lang.Override
    public int getY() {
      return y_;
    }

    public static final int Z_FIELD_NUMBER = 3;
    private int z_ = 0;
    /**
     * <pre>
     * z axis.
     * </pre>
     *
     * <code>optional sint32 z = 3;</code>
     * @return Whether the z field is set.
     */
    @java.lang.Override
    public boolean hasZ() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * z axis.
     * </pre>
     *
     * <code>optional sint32 z = 3;</code>
     * @return The z.
     */
    @java.lang.Override
    public int getZ() {
      return z_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 6;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <pre>
     * Timestamp.
     * </pre>
     *
     * <code>.Timestamp timestamp = 6;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }

    public static final int METADATA_FIELD_NUMBER = 7;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <pre>
     * Metadata.
     * </pre>
     *
     * <code>.Metadata metadata = 7;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeSInt32(1, x_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeSInt32(2, y_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeSInt32(3, z_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(6, getTimestamp());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(7, getMetadata());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(1, x_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(2, y_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(3, z_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getTimestamp());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getMetadata());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope other = (com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope) obj;

      if (hasX() != other.hasX()) return false;
      if (hasX()) {
        if (getX()
            != other.getX()) return false;
      }
      if (hasY() != other.hasY()) return false;
      if (hasY()) {
        if (getY()
            != other.getY()) return false;
      }
      if (hasZ() != other.hasZ()) return false;
      if (hasZ()) {
        if (getZ()
            != other.getZ()) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasX()) {
        hash = (37 * hash) + X_FIELD_NUMBER;
        hash = (53 * hash) + getX();
      }
      if (hasY()) {
        hash = (37 * hash) + Y_FIELD_NUMBER;
        hash = (53 * hash) + getY();
      }
      if (hasZ()) {
        hash = (37 * hash) + Z_FIELD_NUMBER;
        hash = (53 * hash) + getZ();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Represents the Gyroscope protocol. Gyroscope data is part of the TCU.
     * </pre>
     *
     * Protobuf type {@code Gyroscope}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Gyroscope)
        com.nichesolv.nds.model.proto.model.GyroscopeProto.GyroscopeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.GyroscopeProto.internal_static_Gyroscope_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.GyroscopeProto.internal_static_Gyroscope_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope.class, com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTimestampFieldBuilder();
          getMetadataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        x_ = 0;
        y_ = 0;
        z_ = 0;
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.GyroscopeProto.internal_static_Gyroscope_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope build() {
        com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope buildPartial() {
        com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope result = new com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.x_ = x_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.y_ = y_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.z_ = z_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.timestamp_ = timestampBuilder_ == null
              ? timestamp_
              : timestampBuilder_.build();
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.metadata_ = metadataBuilder_ == null
              ? metadata_
              : metadataBuilder_.build();
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope other) {
        if (other == com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope.getDefaultInstance()) return this;
        if (other.hasX()) {
          setX(other.getX());
        }
        if (other.hasY()) {
          setY(other.getY());
        }
        if (other.hasZ()) {
          setZ(other.getZ());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                x_ = input.readSInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                y_ = input.readSInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                z_ = input.readSInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 50: {
                input.readMessage(
                    getTimestampFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 50
              case 58: {
                input.readMessage(
                    getMetadataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000010;
                break;
              } // case 58
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int x_ ;
      /**
       * <pre>
       * x axis.
       * </pre>
       *
       * <code>optional sint32 x = 1;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * x axis.
       * </pre>
       *
       * <code>optional sint32 x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return x_;
      }
      /**
       * <pre>
       * x axis.
       * </pre>
       *
       * <code>optional sint32 x = 1;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(int value) {

        x_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * x axis.
       * </pre>
       *
       * <code>optional sint32 x = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        bitField0_ = (bitField0_ & ~0x00000001);
        x_ = 0;
        onChanged();
        return this;
      }

      private int y_ ;
      /**
       * <pre>
       * y axis.
       * </pre>
       *
       * <code>optional sint32 y = 2;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * y axis.
       * </pre>
       *
       * <code>optional sint32 y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return y_;
      }
      /**
       * <pre>
       * y axis.
       * </pre>
       *
       * <code>optional sint32 y = 2;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(int value) {

        y_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * y axis.
       * </pre>
       *
       * <code>optional sint32 y = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        bitField0_ = (bitField0_ & ~0x00000002);
        y_ = 0;
        onChanged();
        return this;
      }

      private int z_ ;
      /**
       * <pre>
       * z axis.
       * </pre>
       *
       * <code>optional sint32 z = 3;</code>
       * @return Whether the z field is set.
       */
      @java.lang.Override
      public boolean hasZ() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * z axis.
       * </pre>
       *
       * <code>optional sint32 z = 3;</code>
       * @return The z.
       */
      @java.lang.Override
      public int getZ() {
        return z_;
      }
      /**
       * <pre>
       * z axis.
       * </pre>
       *
       * <code>optional sint32 z = 3;</code>
       * @param value The z to set.
       * @return This builder for chaining.
       */
      public Builder setZ(int value) {

        z_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * z axis.
       * </pre>
       *
       * <code>optional sint32 z = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearZ() {
        bitField0_ = (bitField0_ & ~0x00000004);
        z_ = 0;
        onChanged();
        return this;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
        } else {
          timestampBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            timestamp_ != null &&
            timestamp_ != com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance()) {
            getTimestampBuilder().mergeFrom(value);
          } else {
            timestamp_ = value;
          }
        } else {
          timestampBuilder_.mergeFrom(value);
        }
        if (timestamp_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000008);
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <pre>
       * Timestamp.
       * </pre>
       *
       * <code>.Timestamp timestamp = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
        } else {
          metadataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
            metadata_ != null &&
            metadata_ != com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance()) {
            getMetadataBuilder().mergeFrom(value);
          } else {
            metadata_ = value;
          }
        } else {
          metadataBuilder_.mergeFrom(value);
        }
        if (metadata_ != null) {
          bitField0_ |= 0x00000010;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public Builder clearMetadata() {
        bitField0_ = (bitField0_ & ~0x00000010);
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <pre>
       * Metadata.
       * </pre>
       *
       * <code>.Metadata metadata = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Gyroscope)
    }

    // @@protoc_insertion_point(class_scope:Gyroscope)
    private static final com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope();
    }

    public static com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Gyroscope>
        PARSER = new com.google.protobuf.AbstractParser<Gyroscope>() {
      @java.lang.Override
      public Gyroscope parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Gyroscope> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Gyroscope> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.GyroscopeProto.Gyroscope getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Gyroscope_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Gyroscope_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n-com/nichesolv/nds/model/proto/gyroscop" +
      "e.proto\032-com/nichesolv/nds/model/proto/t" +
      "imestamp.proto\032,com/nichesolv/nds/model/" +
      "proto/metadata.proto\"\211\001\n\tGyroscope\022\016\n\001x\030" +
      "\001 \001(\021H\000\210\001\001\022\016\n\001y\030\002 \001(\021H\001\210\001\001\022\016\n\001z\030\003 \001(\021H\002\210" +
      "\001\001\022\035\n\ttimestamp\030\006 \001(\0132\n.Timestamp\022\033\n\010met" +
      "adata\030\007 \001(\0132\t.MetadataB\004\n\002_xB\004\n\002_yB\004\n\002_z" +
      "B5\n#com.nichesolv.nds.model.proto.modelB" +
      "\016GyroscopeProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor(),
        });
    internal_static_Gyroscope_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Gyroscope_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Gyroscope_descriptor,
        new java.lang.String[] { "X", "Y", "Z", "Timestamp", "Metadata", });
    com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
