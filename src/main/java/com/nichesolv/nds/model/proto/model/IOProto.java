// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/io.proto

package com.nichesolv.nds.model.proto.model;

public final class IOProto {
  private IOProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.DigitalInput digital_input = 1;</code>
     * @return Whether the digitalInput field is set.
     */
    boolean hasDigitalInput();
    /**
     * <code>.DigitalInput digital_input = 1;</code>
     * @return The digitalInput.
     */
    DigitalInputProto.DigitalInput getDigitalInput();
    /**
     * <code>.DigitalInput digital_input = 1;</code>
     */
    DigitalInputProto.DigitalInputOrBuilder getDigitalInputOrBuilder();

    /**
     * <code>.DigitalOutput digital_output = 3;</code>
     * @return Whether the digitalOutput field is set.
     */
    boolean hasDigitalOutput();
    /**
     * <code>.DigitalOutput digital_output = 3;</code>
     * @return The digitalOutput.
     */
    DigitalOutputProto.DigitalOutput getDigitalOutput();
    /**
     * <code>.DigitalOutput digital_output = 3;</code>
     */
    DigitalOutputProto.DigitalOutputOrBuilder getDigitalOutputOrBuilder();

    /**
     * <code>.AnalogInput analog_input = 5;</code>
     * @return Whether the analogInput field is set.
     */
    boolean hasAnalogInput();
    /**
     * <code>.AnalogInput analog_input = 5;</code>
     * @return The analogInput.
     */
    AnalogInputProto.AnalogInput getAnalogInput();
    /**
     * <code>.AnalogInput analog_input = 5;</code>
     */
    AnalogInputProto.AnalogInputOrBuilder getAnalogInputOrBuilder();

    /**
     * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
     * @return Whether the timestampWithMetadata field is set.
     */
    boolean hasTimestampWithMetadata();
    /**
     * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
     * @return The timestampWithMetadata.
     */
    TimestampWithMetadataProto.TimestampWithMetadata getTimestampWithMetadata();
    /**
     * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
     */
    TimestampWithMetadataProto.TimestampWithMetadataOrBuilder getTimestampWithMetadataOrBuilder();
  }
  /**
   * Protobuf type {@code IO}
   */
  public static final class IO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:IO)
      IOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IO.newBuilder() to construct.
    private IO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IO() {
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new IO();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IOProto.internal_static_IO_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IOProto.internal_static_IO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IO.class, Builder.class);
    }

    private int bitField0_;
    public static final int DIGITAL_INPUT_FIELD_NUMBER = 1;
    private DigitalInputProto.DigitalInput digitalInput_;
    /**
     * <code>.DigitalInput digital_input = 1;</code>
     * @return Whether the digitalInput field is set.
     */
    @Override
    public boolean hasDigitalInput() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.DigitalInput digital_input = 1;</code>
     * @return The digitalInput.
     */
    @Override
    public DigitalInputProto.DigitalInput getDigitalInput() {
      return digitalInput_ == null ? DigitalInputProto.DigitalInput.getDefaultInstance() : digitalInput_;
    }
    /**
     * <code>.DigitalInput digital_input = 1;</code>
     */
    @Override
    public DigitalInputProto.DigitalInputOrBuilder getDigitalInputOrBuilder() {
      return digitalInput_ == null ? DigitalInputProto.DigitalInput.getDefaultInstance() : digitalInput_;
    }

    public static final int DIGITAL_OUTPUT_FIELD_NUMBER = 3;
    private DigitalOutputProto.DigitalOutput digitalOutput_;
    /**
     * <code>.DigitalOutput digital_output = 3;</code>
     * @return Whether the digitalOutput field is set.
     */
    @Override
    public boolean hasDigitalOutput() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.DigitalOutput digital_output = 3;</code>
     * @return The digitalOutput.
     */
    @Override
    public DigitalOutputProto.DigitalOutput getDigitalOutput() {
      return digitalOutput_ == null ? DigitalOutputProto.DigitalOutput.getDefaultInstance() : digitalOutput_;
    }
    /**
     * <code>.DigitalOutput digital_output = 3;</code>
     */
    @Override
    public DigitalOutputProto.DigitalOutputOrBuilder getDigitalOutputOrBuilder() {
      return digitalOutput_ == null ? DigitalOutputProto.DigitalOutput.getDefaultInstance() : digitalOutput_;
    }

    public static final int ANALOG_INPUT_FIELD_NUMBER = 5;
    private AnalogInputProto.AnalogInput analogInput_;
    /**
     * <code>.AnalogInput analog_input = 5;</code>
     * @return Whether the analogInput field is set.
     */
    @Override
    public boolean hasAnalogInput() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.AnalogInput analog_input = 5;</code>
     * @return The analogInput.
     */
    @Override
    public AnalogInputProto.AnalogInput getAnalogInput() {
      return analogInput_ == null ? AnalogInputProto.AnalogInput.getDefaultInstance() : analogInput_;
    }
    /**
     * <code>.AnalogInput analog_input = 5;</code>
     */
    @Override
    public AnalogInputProto.AnalogInputOrBuilder getAnalogInputOrBuilder() {
      return analogInput_ == null ? AnalogInputProto.AnalogInput.getDefaultInstance() : analogInput_;
    }

    public static final int TIMESTAMP_WITH_METADATA_FIELD_NUMBER = 7;
    private TimestampWithMetadataProto.TimestampWithMetadata timestampWithMetadata_;
    /**
     * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
     * @return Whether the timestampWithMetadata field is set.
     */
    @Override
    public boolean hasTimestampWithMetadata() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
     * @return The timestampWithMetadata.
     */
    @Override
    public TimestampWithMetadataProto.TimestampWithMetadata getTimestampWithMetadata() {
      return timestampWithMetadata_ == null ? TimestampWithMetadataProto.TimestampWithMetadata.getDefaultInstance() : timestampWithMetadata_;
    }
    /**
     * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
     */
    @Override
    public TimestampWithMetadataProto.TimestampWithMetadataOrBuilder getTimestampWithMetadataOrBuilder() {
      return timestampWithMetadata_ == null ? TimestampWithMetadataProto.TimestampWithMetadata.getDefaultInstance() : timestampWithMetadata_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getDigitalInput());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(3, getDigitalOutput());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(5, getAnalogInput());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(7, getTimestampWithMetadata());
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDigitalInput());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getDigitalOutput());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getAnalogInput());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getTimestampWithMetadata());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IO)) {
        return super.equals(obj);
      }
      IO other = (IO) obj;

      if (hasDigitalInput() != other.hasDigitalInput()) return false;
      if (hasDigitalInput()) {
        if (!getDigitalInput()
            .equals(other.getDigitalInput())) return false;
      }
      if (hasDigitalOutput() != other.hasDigitalOutput()) return false;
      if (hasDigitalOutput()) {
        if (!getDigitalOutput()
            .equals(other.getDigitalOutput())) return false;
      }
      if (hasAnalogInput() != other.hasAnalogInput()) return false;
      if (hasAnalogInput()) {
        if (!getAnalogInput()
            .equals(other.getAnalogInput())) return false;
      }
      if (hasTimestampWithMetadata() != other.hasTimestampWithMetadata()) return false;
      if (hasTimestampWithMetadata()) {
        if (!getTimestampWithMetadata()
            .equals(other.getTimestampWithMetadata())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDigitalInput()) {
        hash = (37 * hash) + DIGITAL_INPUT_FIELD_NUMBER;
        hash = (53 * hash) + getDigitalInput().hashCode();
      }
      if (hasDigitalOutput()) {
        hash = (37 * hash) + DIGITAL_OUTPUT_FIELD_NUMBER;
        hash = (53 * hash) + getDigitalOutput().hashCode();
      }
      if (hasAnalogInput()) {
        hash = (37 * hash) + ANALOG_INPUT_FIELD_NUMBER;
        hash = (53 * hash) + getAnalogInput().hashCode();
      }
      if (hasTimestampWithMetadata()) {
        hash = (37 * hash) + TIMESTAMP_WITH_METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getTimestampWithMetadata().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static IO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static IO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code IO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IO)
        IOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IOProto.internal_static_IO_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IOProto.internal_static_IO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IO.class, Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.IOProto.IO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDigitalInputFieldBuilder();
          getDigitalOutputFieldBuilder();
          getAnalogInputFieldBuilder();
          getTimestampWithMetadataFieldBuilder();
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        digitalInput_ = null;
        if (digitalInputBuilder_ != null) {
          digitalInputBuilder_.dispose();
          digitalInputBuilder_ = null;
        }
        digitalOutput_ = null;
        if (digitalOutputBuilder_ != null) {
          digitalOutputBuilder_.dispose();
          digitalOutputBuilder_ = null;
        }
        analogInput_ = null;
        if (analogInputBuilder_ != null) {
          analogInputBuilder_.dispose();
          analogInputBuilder_ = null;
        }
        timestampWithMetadata_ = null;
        if (timestampWithMetadataBuilder_ != null) {
          timestampWithMetadataBuilder_.dispose();
          timestampWithMetadataBuilder_ = null;
        }
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IOProto.internal_static_IO_descriptor;
      }

      @Override
      public IO getDefaultInstanceForType() {
        return IO.getDefaultInstance();
      }

      @Override
      public IO build() {
        IO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public IO buildPartial() {
        IO result = new IO(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(IO result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.digitalInput_ = digitalInputBuilder_ == null
              ? digitalInput_
              : digitalInputBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.digitalOutput_ = digitalOutputBuilder_ == null
              ? digitalOutput_
              : digitalOutputBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.analogInput_ = analogInputBuilder_ == null
              ? analogInput_
              : analogInputBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.timestampWithMetadata_ = timestampWithMetadataBuilder_ == null
              ? timestampWithMetadata_
              : timestampWithMetadataBuilder_.build();
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IO) {
          return mergeFrom((IO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IO other) {
        if (other == IO.getDefaultInstance()) return this;
        if (other.hasDigitalInput()) {
          mergeDigitalInput(other.getDigitalInput());
        }
        if (other.hasDigitalOutput()) {
          mergeDigitalOutput(other.getDigitalOutput());
        }
        if (other.hasAnalogInput()) {
          mergeAnalogInput(other.getAnalogInput());
        }
        if (other.hasTimestampWithMetadata()) {
          mergeTimestampWithMetadata(other.getTimestampWithMetadata());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getDigitalInputFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 26: {
                input.readMessage(
                    getDigitalOutputFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 26
              case 42: {
                input.readMessage(
                    getAnalogInputFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 42
              case 58: {
                input.readMessage(
                    getTimestampWithMetadataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 58
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private DigitalInputProto.DigitalInput digitalInput_;
      private com.google.protobuf.SingleFieldBuilderV3<
          DigitalInputProto.DigitalInput, DigitalInputProto.DigitalInput.Builder, DigitalInputProto.DigitalInputOrBuilder> digitalInputBuilder_;
      /**
       * <code>.DigitalInput digital_input = 1;</code>
       * @return Whether the digitalInput field is set.
       */
      public boolean hasDigitalInput() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.DigitalInput digital_input = 1;</code>
       * @return The digitalInput.
       */
      public DigitalInputProto.DigitalInput getDigitalInput() {
        if (digitalInputBuilder_ == null) {
          return digitalInput_ == null ? DigitalInputProto.DigitalInput.getDefaultInstance() : digitalInput_;
        } else {
          return digitalInputBuilder_.getMessage();
        }
      }
      /**
       * <code>.DigitalInput digital_input = 1;</code>
       */
      public Builder setDigitalInput(DigitalInputProto.DigitalInput value) {
        if (digitalInputBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          digitalInput_ = value;
        } else {
          digitalInputBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.DigitalInput digital_input = 1;</code>
       */
      public Builder setDigitalInput(
          DigitalInputProto.DigitalInput.Builder builderForValue) {
        if (digitalInputBuilder_ == null) {
          digitalInput_ = builderForValue.build();
        } else {
          digitalInputBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.DigitalInput digital_input = 1;</code>
       */
      public Builder mergeDigitalInput(DigitalInputProto.DigitalInput value) {
        if (digitalInputBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            digitalInput_ != null &&
            digitalInput_ != DigitalInputProto.DigitalInput.getDefaultInstance()) {
            getDigitalInputBuilder().mergeFrom(value);
          } else {
            digitalInput_ = value;
          }
        } else {
          digitalInputBuilder_.mergeFrom(value);
        }
        if (digitalInput_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.DigitalInput digital_input = 1;</code>
       */
      public Builder clearDigitalInput() {
        bitField0_ = (bitField0_ & ~0x00000001);
        digitalInput_ = null;
        if (digitalInputBuilder_ != null) {
          digitalInputBuilder_.dispose();
          digitalInputBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.DigitalInput digital_input = 1;</code>
       */
      public DigitalInputProto.DigitalInput.Builder getDigitalInputBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getDigitalInputFieldBuilder().getBuilder();
      }
      /**
       * <code>.DigitalInput digital_input = 1;</code>
       */
      public DigitalInputProto.DigitalInputOrBuilder getDigitalInputOrBuilder() {
        if (digitalInputBuilder_ != null) {
          return digitalInputBuilder_.getMessageOrBuilder();
        } else {
          return digitalInput_ == null ?
              DigitalInputProto.DigitalInput.getDefaultInstance() : digitalInput_;
        }
      }
      /**
       * <code>.DigitalInput digital_input = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          DigitalInputProto.DigitalInput, DigitalInputProto.DigitalInput.Builder, DigitalInputProto.DigitalInputOrBuilder>
          getDigitalInputFieldBuilder() {
        if (digitalInputBuilder_ == null) {
          digitalInputBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              DigitalInputProto.DigitalInput, DigitalInputProto.DigitalInput.Builder, DigitalInputProto.DigitalInputOrBuilder>(
                  getDigitalInput(),
                  getParentForChildren(),
                  isClean());
          digitalInput_ = null;
        }
        return digitalInputBuilder_;
      }

      private DigitalOutputProto.DigitalOutput digitalOutput_;
      private com.google.protobuf.SingleFieldBuilderV3<
          DigitalOutputProto.DigitalOutput, DigitalOutputProto.DigitalOutput.Builder, DigitalOutputProto.DigitalOutputOrBuilder> digitalOutputBuilder_;
      /**
       * <code>.DigitalOutput digital_output = 3;</code>
       * @return Whether the digitalOutput field is set.
       */
      public boolean hasDigitalOutput() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.DigitalOutput digital_output = 3;</code>
       * @return The digitalOutput.
       */
      public DigitalOutputProto.DigitalOutput getDigitalOutput() {
        if (digitalOutputBuilder_ == null) {
          return digitalOutput_ == null ? DigitalOutputProto.DigitalOutput.getDefaultInstance() : digitalOutput_;
        } else {
          return digitalOutputBuilder_.getMessage();
        }
      }
      /**
       * <code>.DigitalOutput digital_output = 3;</code>
       */
      public Builder setDigitalOutput(DigitalOutputProto.DigitalOutput value) {
        if (digitalOutputBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          digitalOutput_ = value;
        } else {
          digitalOutputBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.DigitalOutput digital_output = 3;</code>
       */
      public Builder setDigitalOutput(
          DigitalOutputProto.DigitalOutput.Builder builderForValue) {
        if (digitalOutputBuilder_ == null) {
          digitalOutput_ = builderForValue.build();
        } else {
          digitalOutputBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.DigitalOutput digital_output = 3;</code>
       */
      public Builder mergeDigitalOutput(DigitalOutputProto.DigitalOutput value) {
        if (digitalOutputBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            digitalOutput_ != null &&
            digitalOutput_ != DigitalOutputProto.DigitalOutput.getDefaultInstance()) {
            getDigitalOutputBuilder().mergeFrom(value);
          } else {
            digitalOutput_ = value;
          }
        } else {
          digitalOutputBuilder_.mergeFrom(value);
        }
        if (digitalOutput_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.DigitalOutput digital_output = 3;</code>
       */
      public Builder clearDigitalOutput() {
        bitField0_ = (bitField0_ & ~0x00000002);
        digitalOutput_ = null;
        if (digitalOutputBuilder_ != null) {
          digitalOutputBuilder_.dispose();
          digitalOutputBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.DigitalOutput digital_output = 3;</code>
       */
      public DigitalOutputProto.DigitalOutput.Builder getDigitalOutputBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getDigitalOutputFieldBuilder().getBuilder();
      }
      /**
       * <code>.DigitalOutput digital_output = 3;</code>
       */
      public DigitalOutputProto.DigitalOutputOrBuilder getDigitalOutputOrBuilder() {
        if (digitalOutputBuilder_ != null) {
          return digitalOutputBuilder_.getMessageOrBuilder();
        } else {
          return digitalOutput_ == null ?
              DigitalOutputProto.DigitalOutput.getDefaultInstance() : digitalOutput_;
        }
      }
      /**
       * <code>.DigitalOutput digital_output = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          DigitalOutputProto.DigitalOutput, DigitalOutputProto.DigitalOutput.Builder, DigitalOutputProto.DigitalOutputOrBuilder>
          getDigitalOutputFieldBuilder() {
        if (digitalOutputBuilder_ == null) {
          digitalOutputBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              DigitalOutputProto.DigitalOutput, DigitalOutputProto.DigitalOutput.Builder, DigitalOutputProto.DigitalOutputOrBuilder>(
                  getDigitalOutput(),
                  getParentForChildren(),
                  isClean());
          digitalOutput_ = null;
        }
        return digitalOutputBuilder_;
      }

      private AnalogInputProto.AnalogInput analogInput_;
      private com.google.protobuf.SingleFieldBuilderV3<
          AnalogInputProto.AnalogInput, AnalogInputProto.AnalogInput.Builder, AnalogInputProto.AnalogInputOrBuilder> analogInputBuilder_;
      /**
       * <code>.AnalogInput analog_input = 5;</code>
       * @return Whether the analogInput field is set.
       */
      public boolean hasAnalogInput() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.AnalogInput analog_input = 5;</code>
       * @return The analogInput.
       */
      public AnalogInputProto.AnalogInput getAnalogInput() {
        if (analogInputBuilder_ == null) {
          return analogInput_ == null ? AnalogInputProto.AnalogInput.getDefaultInstance() : analogInput_;
        } else {
          return analogInputBuilder_.getMessage();
        }
      }
      /**
       * <code>.AnalogInput analog_input = 5;</code>
       */
      public Builder setAnalogInput(AnalogInputProto.AnalogInput value) {
        if (analogInputBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          analogInput_ = value;
        } else {
          analogInputBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.AnalogInput analog_input = 5;</code>
       */
      public Builder setAnalogInput(
          AnalogInputProto.AnalogInput.Builder builderForValue) {
        if (analogInputBuilder_ == null) {
          analogInput_ = builderForValue.build();
        } else {
          analogInputBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.AnalogInput analog_input = 5;</code>
       */
      public Builder mergeAnalogInput(AnalogInputProto.AnalogInput value) {
        if (analogInputBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            analogInput_ != null &&
            analogInput_ != AnalogInputProto.AnalogInput.getDefaultInstance()) {
            getAnalogInputBuilder().mergeFrom(value);
          } else {
            analogInput_ = value;
          }
        } else {
          analogInputBuilder_.mergeFrom(value);
        }
        if (analogInput_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.AnalogInput analog_input = 5;</code>
       */
      public Builder clearAnalogInput() {
        bitField0_ = (bitField0_ & ~0x00000004);
        analogInput_ = null;
        if (analogInputBuilder_ != null) {
          analogInputBuilder_.dispose();
          analogInputBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.AnalogInput analog_input = 5;</code>
       */
      public AnalogInputProto.AnalogInput.Builder getAnalogInputBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getAnalogInputFieldBuilder().getBuilder();
      }
      /**
       * <code>.AnalogInput analog_input = 5;</code>
       */
      public AnalogInputProto.AnalogInputOrBuilder getAnalogInputOrBuilder() {
        if (analogInputBuilder_ != null) {
          return analogInputBuilder_.getMessageOrBuilder();
        } else {
          return analogInput_ == null ?
              AnalogInputProto.AnalogInput.getDefaultInstance() : analogInput_;
        }
      }
      /**
       * <code>.AnalogInput analog_input = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          AnalogInputProto.AnalogInput, AnalogInputProto.AnalogInput.Builder, AnalogInputProto.AnalogInputOrBuilder>
          getAnalogInputFieldBuilder() {
        if (analogInputBuilder_ == null) {
          analogInputBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              AnalogInputProto.AnalogInput, AnalogInputProto.AnalogInput.Builder, AnalogInputProto.AnalogInputOrBuilder>(
                  getAnalogInput(),
                  getParentForChildren(),
                  isClean());
          analogInput_ = null;
        }
        return analogInputBuilder_;
      }

      private TimestampWithMetadataProto.TimestampWithMetadata timestampWithMetadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          TimestampWithMetadataProto.TimestampWithMetadata, TimestampWithMetadataProto.TimestampWithMetadata.Builder, TimestampWithMetadataProto.TimestampWithMetadataOrBuilder> timestampWithMetadataBuilder_;
      /**
       * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
       * @return Whether the timestampWithMetadata field is set.
       */
      public boolean hasTimestampWithMetadata() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
       * @return The timestampWithMetadata.
       */
      public TimestampWithMetadataProto.TimestampWithMetadata getTimestampWithMetadata() {
        if (timestampWithMetadataBuilder_ == null) {
          return timestampWithMetadata_ == null ? TimestampWithMetadataProto.TimestampWithMetadata.getDefaultInstance() : timestampWithMetadata_;
        } else {
          return timestampWithMetadataBuilder_.getMessage();
        }
      }
      /**
       * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
       */
      public Builder setTimestampWithMetadata(TimestampWithMetadataProto.TimestampWithMetadata value) {
        if (timestampWithMetadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestampWithMetadata_ = value;
        } else {
          timestampWithMetadataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
       */
      public Builder setTimestampWithMetadata(
          TimestampWithMetadataProto.TimestampWithMetadata.Builder builderForValue) {
        if (timestampWithMetadataBuilder_ == null) {
          timestampWithMetadata_ = builderForValue.build();
        } else {
          timestampWithMetadataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
       */
      public Builder mergeTimestampWithMetadata(TimestampWithMetadataProto.TimestampWithMetadata value) {
        if (timestampWithMetadataBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            timestampWithMetadata_ != null &&
            timestampWithMetadata_ != TimestampWithMetadataProto.TimestampWithMetadata.getDefaultInstance()) {
            getTimestampWithMetadataBuilder().mergeFrom(value);
          } else {
            timestampWithMetadata_ = value;
          }
        } else {
          timestampWithMetadataBuilder_.mergeFrom(value);
        }
        if (timestampWithMetadata_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
       */
      public Builder clearTimestampWithMetadata() {
        bitField0_ = (bitField0_ & ~0x00000008);
        timestampWithMetadata_ = null;
        if (timestampWithMetadataBuilder_ != null) {
          timestampWithMetadataBuilder_.dispose();
          timestampWithMetadataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
       */
      public TimestampWithMetadataProto.TimestampWithMetadata.Builder getTimestampWithMetadataBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getTimestampWithMetadataFieldBuilder().getBuilder();
      }
      /**
       * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
       */
      public TimestampWithMetadataProto.TimestampWithMetadataOrBuilder getTimestampWithMetadataOrBuilder() {
        if (timestampWithMetadataBuilder_ != null) {
          return timestampWithMetadataBuilder_.getMessageOrBuilder();
        } else {
          return timestampWithMetadata_ == null ?
              TimestampWithMetadataProto.TimestampWithMetadata.getDefaultInstance() : timestampWithMetadata_;
        }
      }
      /**
       * <code>.TimestampWithMetadata timestamp_with_metadata = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          TimestampWithMetadataProto.TimestampWithMetadata, TimestampWithMetadataProto.TimestampWithMetadata.Builder, TimestampWithMetadataProto.TimestampWithMetadataOrBuilder>
          getTimestampWithMetadataFieldBuilder() {
        if (timestampWithMetadataBuilder_ == null) {
          timestampWithMetadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              TimestampWithMetadataProto.TimestampWithMetadata, TimestampWithMetadataProto.TimestampWithMetadata.Builder, TimestampWithMetadataProto.TimestampWithMetadataOrBuilder>(
                  getTimestampWithMetadata(),
                  getParentForChildren(),
                  isClean());
          timestampWithMetadata_ = null;
        }
        return timestampWithMetadataBuilder_;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:IO)
    }

    // @@protoc_insertion_point(class_scope:IO)
    private static final IO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IO();
    }

    public static IO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<IO>
        PARSER = new com.google.protobuf.AbstractParser<IO>() {
      @Override
      public IO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<IO> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<IO> getParserForType() {
      return PARSER;
    }

    @Override
    public IO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_IO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n&com/nichesolv/nds/model/proto/io.proto" +
      "\0322com/nichesolv/nds/model/proto/digital_" +
      "output.proto\0321com/nichesolv/nds/model/pr" +
      "oto/digital_input.proto\0320com/nichesolv/n" +
      "ds/model/proto/analog_input.proto\032;com/n" +
      "ichesolv/nds/model/proto/timestamp_with_" +
      "metadata.proto\"\257\001\n\002IO\022$\n\rdigital_input\030\001" +
      " \001(\0132\r.DigitalInput\022&\n\016digital_output\030\003 " +
      "\001(\0132\016.DigitalOutput\022\"\n\014analog_input\030\005 \001(" +
      "\0132\014.AnalogInput\0227\n\027timestamp_with_metada" +
      "ta\030\007 \001(\0132\026.TimestampWithMetadataB.\n#com." +
      "nichesolv.nds.model.proto.modelB\007IOProto" +
      "b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          DigitalOutputProto.getDescriptor(),
          DigitalInputProto.getDescriptor(),
          AnalogInputProto.getDescriptor(),
          TimestampWithMetadataProto.getDescriptor(),
        });
    internal_static_IO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_IO_descriptor,
        new String[] { "DigitalInput", "DigitalOutput", "AnalogInput", "TimestampWithMetadata", });
    DigitalOutputProto.getDescriptor();
    DigitalInputProto.getDescriptor();
    AnalogInputProto.getDescriptor();
    TimestampWithMetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
