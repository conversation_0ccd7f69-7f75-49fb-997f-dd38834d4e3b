// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/imu.proto

// Protobuf Java Version: 3.25.0
package com.nichesolv.nds.model.proto.model;

public final class ImuProto {
  private ImuProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ImuOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Imu)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();

    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
     * @return Whether the accelerometer field is set.
     */
    boolean hasAccelerometer();
    /**
     * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
     * @return The accelerometer.
     */
    com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer getAccelerometer();
    /**
     * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
     */
    com.nichesolv.nds.model.proto.model.ImuProto.Imu.AccelerometerOrBuilder getAccelerometerOrBuilder();

    /**
     * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
     * @return Whether the gyroscope field is set.
     */
    boolean hasGyroscope();
    /**
     * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
     * @return The gyroscope.
     */
    com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope getGyroscope();
    /**
     * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
     */
    com.nichesolv.nds.model.proto.model.ImuProto.Imu.GyroscopeOrBuilder getGyroscopeOrBuilder();

    /**
     * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
     * @return Whether the gravitationalVector field is set.
     */
    boolean hasGravitationalVector();
    /**
     * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
     * @return The gravitationalVector.
     */
    com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector getGravitationalVector();
    /**
     * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
     */
    com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVectorOrBuilder getGravitationalVectorOrBuilder();

    /**
     * <code>optional .Imu.DigitalInput digital_input = 6;</code>
     * @return Whether the digitalInput field is set.
     */
    boolean hasDigitalInput();
    /**
     * <code>optional .Imu.DigitalInput digital_input = 6;</code>
     * @return The digitalInput.
     */
    com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput getDigitalInput();
    /**
     * <code>optional .Imu.DigitalInput digital_input = 6;</code>
     */
    com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInputOrBuilder getDigitalInputOrBuilder();
  }
  /**
   * Protobuf type {@code Imu}
   */
  public static final class Imu extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Imu)
      ImuOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Imu.newBuilder() to construct.
    private Imu(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Imu() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Imu();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.ImuProto.Imu.class, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Builder.class);
    }

    public interface AccelerometerOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Imu.Accelerometer)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional sint32 x = 1;</code>
       * @return Whether the x field is set.
       */
      boolean hasX();
      /**
       * <code>optional sint32 x = 1;</code>
       * @return The x.
       */
      int getX();

      /**
       * <code>optional sint32 y = 2;</code>
       * @return Whether the y field is set.
       */
      boolean hasY();
      /**
       * <code>optional sint32 y = 2;</code>
       * @return The y.
       */
      int getY();

      /**
       * <code>optional sint32 z = 3;</code>
       * @return Whether the z field is set.
       */
      boolean hasZ();
      /**
       * <code>optional sint32 z = 3;</code>
       * @return The z.
       */
      int getZ();
    }
    /**
     * <pre>
     * Accelerometer Data
     * </pre>
     *
     * Protobuf type {@code Imu.Accelerometer}
     */
    public static final class Accelerometer extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:Imu.Accelerometer)
        AccelerometerOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Accelerometer.newBuilder() to construct.
      private Accelerometer(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Accelerometer() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Accelerometer();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_Accelerometer_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_Accelerometer_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.class, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.Builder.class);
      }

      private int bitField0_;
      public static final int X_FIELD_NUMBER = 1;
      private int x_ = 0;
      /**
       * <code>optional sint32 x = 1;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional sint32 x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return x_;
      }

      public static final int Y_FIELD_NUMBER = 2;
      private int y_ = 0;
      /**
       * <code>optional sint32 y = 2;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional sint32 y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return y_;
      }

      public static final int Z_FIELD_NUMBER = 3;
      private int z_ = 0;
      /**
       * <code>optional sint32 z = 3;</code>
       * @return Whether the z field is set.
       */
      @java.lang.Override
      public boolean hasZ() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional sint32 z = 3;</code>
       * @return The z.
       */
      @java.lang.Override
      public int getZ() {
        return z_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeSInt32(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeSInt32(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeSInt32(3, z_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(3, z_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer)) {
          return super.equals(obj);
        }
        com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer other = (com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer) obj;

        if (hasX() != other.hasX()) return false;
        if (hasX()) {
          if (getX()
              != other.getX()) return false;
        }
        if (hasY() != other.hasY()) return false;
        if (hasY()) {
          if (getY()
              != other.getY()) return false;
        }
        if (hasZ() != other.hasZ()) return false;
        if (hasZ()) {
          if (getZ()
              != other.getZ()) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasX()) {
          hash = (37 * hash) + X_FIELD_NUMBER;
          hash = (53 * hash) + getX();
        }
        if (hasY()) {
          hash = (37 * hash) + Y_FIELD_NUMBER;
          hash = (53 * hash) + getY();
        }
        if (hasZ()) {
          hash = (37 * hash) + Z_FIELD_NUMBER;
          hash = (53 * hash) + getZ();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * Accelerometer Data
       * </pre>
       *
       * Protobuf type {@code Imu.Accelerometer}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Imu.Accelerometer)
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.AccelerometerOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_Accelerometer_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_Accelerometer_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.class, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.Builder.class);
        }

        // Construct using com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          x_ = 0;
          y_ = 0;
          z_ = 0;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_Accelerometer_descriptor;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer getDefaultInstanceForType() {
          return com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.getDefaultInstance();
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer build() {
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer buildPartial() {
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer result = new com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.x_ = x_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.y_ = y_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.z_ = z_;
            to_bitField0_ |= 0x00000004;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer) {
            return mergeFrom((com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer other) {
          if (other == com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.getDefaultInstance()) return this;
          if (other.hasX()) {
            setX(other.getX());
          }
          if (other.hasY()) {
            setY(other.getY());
          }
          if (other.hasZ()) {
            setZ(other.getZ());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  x_ = input.readSInt32();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 16: {
                  y_ = input.readSInt32();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                case 24: {
                  z_ = input.readSInt32();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private int x_ ;
        /**
         * <code>optional sint32 x = 1;</code>
         * @return Whether the x field is set.
         */
        @java.lang.Override
        public boolean hasX() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @return The x.
         */
        @java.lang.Override
        public int getX() {
          return x_;
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @param value The x to set.
         * @return This builder for chaining.
         */
        public Builder setX(int value) {

          x_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearX() {
          bitField0_ = (bitField0_ & ~0x00000001);
          x_ = 0;
          onChanged();
          return this;
        }

        private int y_ ;
        /**
         * <code>optional sint32 y = 2;</code>
         * @return Whether the y field is set.
         */
        @java.lang.Override
        public boolean hasY() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @return The y.
         */
        @java.lang.Override
        public int getY() {
          return y_;
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @param value The y to set.
         * @return This builder for chaining.
         */
        public Builder setY(int value) {

          y_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearY() {
          bitField0_ = (bitField0_ & ~0x00000002);
          y_ = 0;
          onChanged();
          return this;
        }

        private int z_ ;
        /**
         * <code>optional sint32 z = 3;</code>
         * @return Whether the z field is set.
         */
        @java.lang.Override
        public boolean hasZ() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @return The z.
         */
        @java.lang.Override
        public int getZ() {
          return z_;
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @param value The z to set.
         * @return This builder for chaining.
         */
        public Builder setZ(int value) {

          z_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearZ() {
          bitField0_ = (bitField0_ & ~0x00000004);
          z_ = 0;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:Imu.Accelerometer)
      }

      // @@protoc_insertion_point(class_scope:Imu.Accelerometer)
      private static final com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer();
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Accelerometer>
          PARSER = new com.google.protobuf.AbstractParser<Accelerometer>() {
        @java.lang.Override
        public Accelerometer parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Accelerometer> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Accelerometer> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface GyroscopeOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Imu.Gyroscope)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional sint32 x = 1;</code>
       * @return Whether the x field is set.
       */
      boolean hasX();
      /**
       * <code>optional sint32 x = 1;</code>
       * @return The x.
       */
      int getX();

      /**
       * <code>optional sint32 y = 2;</code>
       * @return Whether the y field is set.
       */
      boolean hasY();
      /**
       * <code>optional sint32 y = 2;</code>
       * @return The y.
       */
      int getY();

      /**
       * <code>optional sint32 z = 3;</code>
       * @return Whether the z field is set.
       */
      boolean hasZ();
      /**
       * <code>optional sint32 z = 3;</code>
       * @return The z.
       */
      int getZ();
    }
    /**
     * <pre>
     * Gyroscope Data
     * </pre>
     *
     * Protobuf type {@code Imu.Gyroscope}
     */
    public static final class Gyroscope extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:Imu.Gyroscope)
        GyroscopeOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Gyroscope.newBuilder() to construct.
      private Gyroscope(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Gyroscope() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Gyroscope();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_Gyroscope_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_Gyroscope_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.class, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.Builder.class);
      }

      private int bitField0_;
      public static final int X_FIELD_NUMBER = 1;
      private int x_ = 0;
      /**
       * <code>optional sint32 x = 1;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional sint32 x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return x_;
      }

      public static final int Y_FIELD_NUMBER = 2;
      private int y_ = 0;
      /**
       * <code>optional sint32 y = 2;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional sint32 y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return y_;
      }

      public static final int Z_FIELD_NUMBER = 3;
      private int z_ = 0;
      /**
       * <code>optional sint32 z = 3;</code>
       * @return Whether the z field is set.
       */
      @java.lang.Override
      public boolean hasZ() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional sint32 z = 3;</code>
       * @return The z.
       */
      @java.lang.Override
      public int getZ() {
        return z_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeSInt32(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeSInt32(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeSInt32(3, z_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(3, z_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope)) {
          return super.equals(obj);
        }
        com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope other = (com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope) obj;

        if (hasX() != other.hasX()) return false;
        if (hasX()) {
          if (getX()
              != other.getX()) return false;
        }
        if (hasY() != other.hasY()) return false;
        if (hasY()) {
          if (getY()
              != other.getY()) return false;
        }
        if (hasZ() != other.hasZ()) return false;
        if (hasZ()) {
          if (getZ()
              != other.getZ()) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasX()) {
          hash = (37 * hash) + X_FIELD_NUMBER;
          hash = (53 * hash) + getX();
        }
        if (hasY()) {
          hash = (37 * hash) + Y_FIELD_NUMBER;
          hash = (53 * hash) + getY();
        }
        if (hasZ()) {
          hash = (37 * hash) + Z_FIELD_NUMBER;
          hash = (53 * hash) + getZ();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * Gyroscope Data
       * </pre>
       *
       * Protobuf type {@code Imu.Gyroscope}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Imu.Gyroscope)
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.GyroscopeOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_Gyroscope_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_Gyroscope_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.class, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.Builder.class);
        }

        // Construct using com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          x_ = 0;
          y_ = 0;
          z_ = 0;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_Gyroscope_descriptor;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope getDefaultInstanceForType() {
          return com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.getDefaultInstance();
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope build() {
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope buildPartial() {
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope result = new com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.x_ = x_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.y_ = y_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.z_ = z_;
            to_bitField0_ |= 0x00000004;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope) {
            return mergeFrom((com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope other) {
          if (other == com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.getDefaultInstance()) return this;
          if (other.hasX()) {
            setX(other.getX());
          }
          if (other.hasY()) {
            setY(other.getY());
          }
          if (other.hasZ()) {
            setZ(other.getZ());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  x_ = input.readSInt32();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 16: {
                  y_ = input.readSInt32();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                case 24: {
                  z_ = input.readSInt32();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private int x_ ;
        /**
         * <code>optional sint32 x = 1;</code>
         * @return Whether the x field is set.
         */
        @java.lang.Override
        public boolean hasX() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @return The x.
         */
        @java.lang.Override
        public int getX() {
          return x_;
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @param value The x to set.
         * @return This builder for chaining.
         */
        public Builder setX(int value) {

          x_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearX() {
          bitField0_ = (bitField0_ & ~0x00000001);
          x_ = 0;
          onChanged();
          return this;
        }

        private int y_ ;
        /**
         * <code>optional sint32 y = 2;</code>
         * @return Whether the y field is set.
         */
        @java.lang.Override
        public boolean hasY() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @return The y.
         */
        @java.lang.Override
        public int getY() {
          return y_;
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @param value The y to set.
         * @return This builder for chaining.
         */
        public Builder setY(int value) {

          y_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearY() {
          bitField0_ = (bitField0_ & ~0x00000002);
          y_ = 0;
          onChanged();
          return this;
        }

        private int z_ ;
        /**
         * <code>optional sint32 z = 3;</code>
         * @return Whether the z field is set.
         */
        @java.lang.Override
        public boolean hasZ() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @return The z.
         */
        @java.lang.Override
        public int getZ() {
          return z_;
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @param value The z to set.
         * @return This builder for chaining.
         */
        public Builder setZ(int value) {

          z_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearZ() {
          bitField0_ = (bitField0_ & ~0x00000004);
          z_ = 0;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:Imu.Gyroscope)
      }

      // @@protoc_insertion_point(class_scope:Imu.Gyroscope)
      private static final com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope();
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Gyroscope>
          PARSER = new com.google.protobuf.AbstractParser<Gyroscope>() {
        @java.lang.Override
        public Gyroscope parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Gyroscope> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Gyroscope> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface GravitationalVectorOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Imu.GravitationalVector)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional float x = 1;</code>
       * @return Whether the x field is set.
       */
      boolean hasX();
      /**
       * <code>optional float x = 1;</code>
       * @return The x.
       */
      float getX();

      /**
       * <code>optional float y = 2;</code>
       * @return Whether the y field is set.
       */
      boolean hasY();
      /**
       * <code>optional float y = 2;</code>
       * @return The y.
       */
      float getY();

      /**
       * <code>optional float z = 3;</code>
       * @return Whether the z field is set.
       */
      boolean hasZ();
      /**
       * <code>optional float z = 3;</code>
       * @return The z.
       */
      float getZ();
    }
    /**
     * <pre>
     * Gravitational Vector Data
     * </pre>
     *
     * Protobuf type {@code Imu.GravitationalVector}
     */
    public static final class GravitationalVector extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:Imu.GravitationalVector)
        GravitationalVectorOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use GravitationalVector.newBuilder() to construct.
      private GravitationalVector(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private GravitationalVector() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new GravitationalVector();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_GravitationalVector_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_GravitationalVector_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.class, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.Builder.class);
      }

      private int bitField0_;
      public static final int X_FIELD_NUMBER = 1;
      private float x_ = 0F;
      /**
       * <code>optional float x = 1;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional float x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public float getX() {
        return x_;
      }

      public static final int Y_FIELD_NUMBER = 2;
      private float y_ = 0F;
      /**
       * <code>optional float y = 2;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional float y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public float getY() {
        return y_;
      }

      public static final int Z_FIELD_NUMBER = 3;
      private float z_ = 0F;
      /**
       * <code>optional float z = 3;</code>
       * @return Whether the z field is set.
       */
      @java.lang.Override
      public boolean hasZ() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional float z = 3;</code>
       * @return The z.
       */
      @java.lang.Override
      public float getZ() {
        return z_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeFloat(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeFloat(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeFloat(3, z_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeFloatSize(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeFloatSize(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeFloatSize(3, z_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector)) {
          return super.equals(obj);
        }
        com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector other = (com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector) obj;

        if (hasX() != other.hasX()) return false;
        if (hasX()) {
          if (java.lang.Float.floatToIntBits(getX())
              != java.lang.Float.floatToIntBits(
                  other.getX())) return false;
        }
        if (hasY() != other.hasY()) return false;
        if (hasY()) {
          if (java.lang.Float.floatToIntBits(getY())
              != java.lang.Float.floatToIntBits(
                  other.getY())) return false;
        }
        if (hasZ() != other.hasZ()) return false;
        if (hasZ()) {
          if (java.lang.Float.floatToIntBits(getZ())
              != java.lang.Float.floatToIntBits(
                  other.getZ())) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasX()) {
          hash = (37 * hash) + X_FIELD_NUMBER;
          hash = (53 * hash) + java.lang.Float.floatToIntBits(
              getX());
        }
        if (hasY()) {
          hash = (37 * hash) + Y_FIELD_NUMBER;
          hash = (53 * hash) + java.lang.Float.floatToIntBits(
              getY());
        }
        if (hasZ()) {
          hash = (37 * hash) + Z_FIELD_NUMBER;
          hash = (53 * hash) + java.lang.Float.floatToIntBits(
              getZ());
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * Gravitational Vector Data
       * </pre>
       *
       * Protobuf type {@code Imu.GravitationalVector}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Imu.GravitationalVector)
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVectorOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_GravitationalVector_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_GravitationalVector_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.class, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.Builder.class);
        }

        // Construct using com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          x_ = 0F;
          y_ = 0F;
          z_ = 0F;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_GravitationalVector_descriptor;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector getDefaultInstanceForType() {
          return com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.getDefaultInstance();
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector build() {
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector buildPartial() {
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector result = new com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.x_ = x_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.y_ = y_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.z_ = z_;
            to_bitField0_ |= 0x00000004;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector) {
            return mergeFrom((com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector other) {
          if (other == com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.getDefaultInstance()) return this;
          if (other.hasX()) {
            setX(other.getX());
          }
          if (other.hasY()) {
            setY(other.getY());
          }
          if (other.hasZ()) {
            setZ(other.getZ());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 13: {
                  x_ = input.readFloat();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 13
                case 21: {
                  y_ = input.readFloat();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 21
                case 29: {
                  z_ = input.readFloat();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 29
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private float x_ ;
        /**
         * <code>optional float x = 1;</code>
         * @return Whether the x field is set.
         */
        @java.lang.Override
        public boolean hasX() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional float x = 1;</code>
         * @return The x.
         */
        @java.lang.Override
        public float getX() {
          return x_;
        }
        /**
         * <code>optional float x = 1;</code>
         * @param value The x to set.
         * @return This builder for chaining.
         */
        public Builder setX(float value) {

          x_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>optional float x = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearX() {
          bitField0_ = (bitField0_ & ~0x00000001);
          x_ = 0F;
          onChanged();
          return this;
        }

        private float y_ ;
        /**
         * <code>optional float y = 2;</code>
         * @return Whether the y field is set.
         */
        @java.lang.Override
        public boolean hasY() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional float y = 2;</code>
         * @return The y.
         */
        @java.lang.Override
        public float getY() {
          return y_;
        }
        /**
         * <code>optional float y = 2;</code>
         * @param value The y to set.
         * @return This builder for chaining.
         */
        public Builder setY(float value) {

          y_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>optional float y = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearY() {
          bitField0_ = (bitField0_ & ~0x00000002);
          y_ = 0F;
          onChanged();
          return this;
        }

        private float z_ ;
        /**
         * <code>optional float z = 3;</code>
         * @return Whether the z field is set.
         */
        @java.lang.Override
        public boolean hasZ() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <code>optional float z = 3;</code>
         * @return The z.
         */
        @java.lang.Override
        public float getZ() {
          return z_;
        }
        /**
         * <code>optional float z = 3;</code>
         * @param value The z to set.
         * @return This builder for chaining.
         */
        public Builder setZ(float value) {

          z_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>optional float z = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearZ() {
          bitField0_ = (bitField0_ & ~0x00000004);
          z_ = 0F;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:Imu.GravitationalVector)
      }

      // @@protoc_insertion_point(class_scope:Imu.GravitationalVector)
      private static final com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector();
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<GravitationalVector>
          PARSER = new com.google.protobuf.AbstractParser<GravitationalVector>() {
        @java.lang.Override
        public GravitationalVector parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<GravitationalVector> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<GravitationalVector> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface DigitalInputOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Imu.DigitalInput)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional bool motion = 1;</code>
       * @return Whether the motion field is set.
       */
      boolean hasMotion();
      /**
       * <code>optional bool motion = 1;</code>
       * @return The motion.
       */
      boolean getMotion();

      /**
       * <code>optional bool ignition = 2;</code>
       * @return Whether the ignition field is set.
       */
      boolean hasIgnition();
      /**
       * <code>optional bool ignition = 2;</code>
       * @return The ignition.
       */
      boolean getIgnition();
    }
    /**
     * <pre>
     * Digital Input Data
     * </pre>
     *
     * Protobuf type {@code Imu.DigitalInput}
     */
    public static final class DigitalInput extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:Imu.DigitalInput)
        DigitalInputOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use DigitalInput.newBuilder() to construct.
      private DigitalInput(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private DigitalInput() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new DigitalInput();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_DigitalInput_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_DigitalInput_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.class, com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.Builder.class);
      }

      private int bitField0_;
      public static final int MOTION_FIELD_NUMBER = 1;
      private boolean motion_ = false;
      /**
       * <code>optional bool motion = 1;</code>
       * @return Whether the motion field is set.
       */
      @java.lang.Override
      public boolean hasMotion() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool motion = 1;</code>
       * @return The motion.
       */
      @java.lang.Override
      public boolean getMotion() {
        return motion_;
      }

      public static final int IGNITION_FIELD_NUMBER = 2;
      private boolean ignition_ = false;
      /**
       * <code>optional bool ignition = 2;</code>
       * @return Whether the ignition field is set.
       */
      @java.lang.Override
      public boolean hasIgnition() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bool ignition = 2;</code>
       * @return The ignition.
       */
      @java.lang.Override
      public boolean getIgnition() {
        return ignition_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeBool(1, motion_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeBool(2, ignition_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(1, motion_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(2, ignition_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput)) {
          return super.equals(obj);
        }
        com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput other = (com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput) obj;

        if (hasMotion() != other.hasMotion()) return false;
        if (hasMotion()) {
          if (getMotion()
              != other.getMotion()) return false;
        }
        if (hasIgnition() != other.hasIgnition()) return false;
        if (hasIgnition()) {
          if (getIgnition()
              != other.getIgnition()) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasMotion()) {
          hash = (37 * hash) + MOTION_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getMotion());
        }
        if (hasIgnition()) {
          hash = (37 * hash) + IGNITION_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getIgnition());
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * Digital Input Data
       * </pre>
       *
       * Protobuf type {@code Imu.DigitalInput}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Imu.DigitalInput)
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInputOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_DigitalInput_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_DigitalInput_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.class, com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.Builder.class);
        }

        // Construct using com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          motion_ = false;
          ignition_ = false;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_DigitalInput_descriptor;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput getDefaultInstanceForType() {
          return com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.getDefaultInstance();
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput build() {
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput buildPartial() {
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput result = new com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.motion_ = motion_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.ignition_ = ignition_;
            to_bitField0_ |= 0x00000002;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput) {
            return mergeFrom((com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput other) {
          if (other == com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.getDefaultInstance()) return this;
          if (other.hasMotion()) {
            setMotion(other.getMotion());
          }
          if (other.hasIgnition()) {
            setIgnition(other.getIgnition());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  motion_ = input.readBool();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 16: {
                  ignition_ = input.readBool();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private boolean motion_ ;
        /**
         * <code>optional bool motion = 1;</code>
         * @return Whether the motion field is set.
         */
        @java.lang.Override
        public boolean hasMotion() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional bool motion = 1;</code>
         * @return The motion.
         */
        @java.lang.Override
        public boolean getMotion() {
          return motion_;
        }
        /**
         * <code>optional bool motion = 1;</code>
         * @param value The motion to set.
         * @return This builder for chaining.
         */
        public Builder setMotion(boolean value) {

          motion_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>optional bool motion = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearMotion() {
          bitField0_ = (bitField0_ & ~0x00000001);
          motion_ = false;
          onChanged();
          return this;
        }

        private boolean ignition_ ;
        /**
         * <code>optional bool ignition = 2;</code>
         * @return Whether the ignition field is set.
         */
        @java.lang.Override
        public boolean hasIgnition() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional bool ignition = 2;</code>
         * @return The ignition.
         */
        @java.lang.Override
        public boolean getIgnition() {
          return ignition_;
        }
        /**
         * <code>optional bool ignition = 2;</code>
         * @param value The ignition to set.
         * @return This builder for chaining.
         */
        public Builder setIgnition(boolean value) {

          ignition_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>optional bool ignition = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearIgnition() {
          bitField0_ = (bitField0_ & ~0x00000002);
          ignition_ = false;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:Imu.DigitalInput)
      }

      // @@protoc_insertion_point(class_scope:Imu.DigitalInput)
      private static final com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput();
      }

      public static com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<DigitalInput>
          PARSER = new com.google.protobuf.AbstractParser<DigitalInput>() {
        @java.lang.Override
        public DigitalInput parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<DigitalInput> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<DigitalInput> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    private int bitField0_;
    public static final int METADATA_FIELD_NUMBER = 1;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 2;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }

    public static final int ACCELEROMETER_FIELD_NUMBER = 3;
    private com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer accelerometer_;
    /**
     * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
     * @return Whether the accelerometer field is set.
     */
    @java.lang.Override
    public boolean hasAccelerometer() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
     * @return The accelerometer.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer getAccelerometer() {
      return accelerometer_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.getDefaultInstance() : accelerometer_;
    }
    /**
     * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.ImuProto.Imu.AccelerometerOrBuilder getAccelerometerOrBuilder() {
      return accelerometer_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.getDefaultInstance() : accelerometer_;
    }

    public static final int GYROSCOPE_FIELD_NUMBER = 4;
    private com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope gyroscope_;
    /**
     * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
     * @return Whether the gyroscope field is set.
     */
    @java.lang.Override
    public boolean hasGyroscope() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
     * @return The gyroscope.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope getGyroscope() {
      return gyroscope_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.getDefaultInstance() : gyroscope_;
    }
    /**
     * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GyroscopeOrBuilder getGyroscopeOrBuilder() {
      return gyroscope_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.getDefaultInstance() : gyroscope_;
    }

    public static final int GRAVITATIONAL_VECTOR_FIELD_NUMBER = 5;
    private com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector gravitationalVector_;
    /**
     * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
     * @return Whether the gravitationalVector field is set.
     */
    @java.lang.Override
    public boolean hasGravitationalVector() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
     * @return The gravitationalVector.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector getGravitationalVector() {
      return gravitationalVector_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.getDefaultInstance() : gravitationalVector_;
    }
    /**
     * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVectorOrBuilder getGravitationalVectorOrBuilder() {
      return gravitationalVector_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.getDefaultInstance() : gravitationalVector_;
    }

    public static final int DIGITAL_INPUT_FIELD_NUMBER = 6;
    private com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput digitalInput_;
    /**
     * <code>optional .Imu.DigitalInput digital_input = 6;</code>
     * @return Whether the digitalInput field is set.
     */
    @java.lang.Override
    public boolean hasDigitalInput() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional .Imu.DigitalInput digital_input = 6;</code>
     * @return The digitalInput.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput getDigitalInput() {
      return digitalInput_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.getDefaultInstance() : digitalInput_;
    }
    /**
     * <code>optional .Imu.DigitalInput digital_input = 6;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInputOrBuilder getDigitalInputOrBuilder() {
      return digitalInput_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.getDefaultInstance() : digitalInput_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getMetadata());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getTimestamp());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getAccelerometer());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getGyroscope());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(5, getGravitationalVector());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeMessage(6, getDigitalInput());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getMetadata());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getTimestamp());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getAccelerometer());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getGyroscope());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getGravitationalVector());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getDigitalInput());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.ImuProto.Imu)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.ImuProto.Imu other = (com.nichesolv.nds.model.proto.model.ImuProto.Imu) obj;

      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasAccelerometer() != other.hasAccelerometer()) return false;
      if (hasAccelerometer()) {
        if (!getAccelerometer()
            .equals(other.getAccelerometer())) return false;
      }
      if (hasGyroscope() != other.hasGyroscope()) return false;
      if (hasGyroscope()) {
        if (!getGyroscope()
            .equals(other.getGyroscope())) return false;
      }
      if (hasGravitationalVector() != other.hasGravitationalVector()) return false;
      if (hasGravitationalVector()) {
        if (!getGravitationalVector()
            .equals(other.getGravitationalVector())) return false;
      }
      if (hasDigitalInput() != other.hasDigitalInput()) return false;
      if (hasDigitalInput()) {
        if (!getDigitalInput()
            .equals(other.getDigitalInput())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasAccelerometer()) {
        hash = (37 * hash) + ACCELEROMETER_FIELD_NUMBER;
        hash = (53 * hash) + getAccelerometer().hashCode();
      }
      if (hasGyroscope()) {
        hash = (37 * hash) + GYROSCOPE_FIELD_NUMBER;
        hash = (53 * hash) + getGyroscope().hashCode();
      }
      if (hasGravitationalVector()) {
        hash = (37 * hash) + GRAVITATIONAL_VECTOR_FIELD_NUMBER;
        hash = (53 * hash) + getGravitationalVector().hashCode();
      }
      if (hasDigitalInput()) {
        hash = (37 * hash) + DIGITAL_INPUT_FIELD_NUMBER;
        hash = (53 * hash) + getDigitalInput().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.ImuProto.Imu prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Imu}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Imu)
        com.nichesolv.nds.model.proto.model.ImuProto.ImuOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.ImuProto.Imu.class, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.ImuProto.Imu.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMetadataFieldBuilder();
          getTimestampFieldBuilder();
          getAccelerometerFieldBuilder();
          getGyroscopeFieldBuilder();
          getGravitationalVectorFieldBuilder();
          getDigitalInputFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        accelerometer_ = null;
        if (accelerometerBuilder_ != null) {
          accelerometerBuilder_.dispose();
          accelerometerBuilder_ = null;
        }
        gyroscope_ = null;
        if (gyroscopeBuilder_ != null) {
          gyroscopeBuilder_.dispose();
          gyroscopeBuilder_ = null;
        }
        gravitationalVector_ = null;
        if (gravitationalVectorBuilder_ != null) {
          gravitationalVectorBuilder_.dispose();
          gravitationalVectorBuilder_ = null;
        }
        digitalInput_ = null;
        if (digitalInputBuilder_ != null) {
          digitalInputBuilder_.dispose();
          digitalInputBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.ImuProto.internal_static_Imu_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.ImuProto.Imu.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu build() {
        com.nichesolv.nds.model.proto.model.ImuProto.Imu result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu buildPartial() {
        com.nichesolv.nds.model.proto.model.ImuProto.Imu result = new com.nichesolv.nds.model.proto.model.ImuProto.Imu(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.ImuProto.Imu result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.metadata_ = metadataBuilder_ == null
              ? metadata_
              : metadataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.timestamp_ = timestampBuilder_ == null
              ? timestamp_
              : timestampBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.accelerometer_ = accelerometerBuilder_ == null
              ? accelerometer_
              : accelerometerBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.gyroscope_ = gyroscopeBuilder_ == null
              ? gyroscope_
              : gyroscopeBuilder_.build();
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.gravitationalVector_ = gravitationalVectorBuilder_ == null
              ? gravitationalVector_
              : gravitationalVectorBuilder_.build();
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.digitalInput_ = digitalInputBuilder_ == null
              ? digitalInput_
              : digitalInputBuilder_.build();
          to_bitField0_ |= 0x00000020;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.ImuProto.Imu) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.ImuProto.Imu)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.ImuProto.Imu other) {
        if (other == com.nichesolv.nds.model.proto.model.ImuProto.Imu.getDefaultInstance()) return this;
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasAccelerometer()) {
          mergeAccelerometer(other.getAccelerometer());
        }
        if (other.hasGyroscope()) {
          mergeGyroscope(other.getGyroscope());
        }
        if (other.hasGravitationalVector()) {
          mergeGravitationalVector(other.getGravitationalVector());
        }
        if (other.hasDigitalInput()) {
          mergeDigitalInput(other.getDigitalInput());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getMetadataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getTimestampFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getAccelerometerFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                input.readMessage(
                    getGyroscopeFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                input.readMessage(
                    getGravitationalVectorFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                input.readMessage(
                    getDigitalInputFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
        } else {
          metadataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            metadata_ != null &&
            metadata_ != com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance()) {
            getMetadataBuilder().mergeFrom(value);
          } else {
            metadata_ = value;
          }
        } else {
          metadataBuilder_.mergeFrom(value);
        }
        if (metadata_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public Builder clearMetadata() {
        bitField0_ = (bitField0_ & ~0x00000001);
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
        } else {
          timestampBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            timestamp_ != null &&
            timestamp_ != com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance()) {
            getTimestampBuilder().mergeFrom(value);
          } else {
            timestamp_ = value;
          }
        } else {
          timestampBuilder_.mergeFrom(value);
        }
        if (timestamp_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer accelerometer_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.AccelerometerOrBuilder> accelerometerBuilder_;
      /**
       * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
       * @return Whether the accelerometer field is set.
       */
      public boolean hasAccelerometer() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
       * @return The accelerometer.
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer getAccelerometer() {
        if (accelerometerBuilder_ == null) {
          return accelerometer_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.getDefaultInstance() : accelerometer_;
        } else {
          return accelerometerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
       */
      public Builder setAccelerometer(com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer value) {
        if (accelerometerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          accelerometer_ = value;
        } else {
          accelerometerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
       */
      public Builder setAccelerometer(
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.Builder builderForValue) {
        if (accelerometerBuilder_ == null) {
          accelerometer_ = builderForValue.build();
        } else {
          accelerometerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
       */
      public Builder mergeAccelerometer(com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer value) {
        if (accelerometerBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            accelerometer_ != null &&
            accelerometer_ != com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.getDefaultInstance()) {
            getAccelerometerBuilder().mergeFrom(value);
          } else {
            accelerometer_ = value;
          }
        } else {
          accelerometerBuilder_.mergeFrom(value);
        }
        if (accelerometer_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
       */
      public Builder clearAccelerometer() {
        bitField0_ = (bitField0_ & ~0x00000004);
        accelerometer_ = null;
        if (accelerometerBuilder_ != null) {
          accelerometerBuilder_.dispose();
          accelerometerBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.Builder getAccelerometerBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getAccelerometerFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.AccelerometerOrBuilder getAccelerometerOrBuilder() {
        if (accelerometerBuilder_ != null) {
          return accelerometerBuilder_.getMessageOrBuilder();
        } else {
          return accelerometer_ == null ?
              com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.getDefaultInstance() : accelerometer_;
        }
      }
      /**
       * <code>optional .Imu.Accelerometer accelerometer = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.AccelerometerOrBuilder> 
          getAccelerometerFieldBuilder() {
        if (accelerometerBuilder_ == null) {
          accelerometerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Accelerometer.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.AccelerometerOrBuilder>(
                  getAccelerometer(),
                  getParentForChildren(),
                  isClean());
          accelerometer_ = null;
        }
        return accelerometerBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope gyroscope_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GyroscopeOrBuilder> gyroscopeBuilder_;
      /**
       * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
       * @return Whether the gyroscope field is set.
       */
      public boolean hasGyroscope() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
       * @return The gyroscope.
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope getGyroscope() {
        if (gyroscopeBuilder_ == null) {
          return gyroscope_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.getDefaultInstance() : gyroscope_;
        } else {
          return gyroscopeBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
       */
      public Builder setGyroscope(com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope value) {
        if (gyroscopeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          gyroscope_ = value;
        } else {
          gyroscopeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
       */
      public Builder setGyroscope(
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.Builder builderForValue) {
        if (gyroscopeBuilder_ == null) {
          gyroscope_ = builderForValue.build();
        } else {
          gyroscopeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
       */
      public Builder mergeGyroscope(com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope value) {
        if (gyroscopeBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            gyroscope_ != null &&
            gyroscope_ != com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.getDefaultInstance()) {
            getGyroscopeBuilder().mergeFrom(value);
          } else {
            gyroscope_ = value;
          }
        } else {
          gyroscopeBuilder_.mergeFrom(value);
        }
        if (gyroscope_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
       */
      public Builder clearGyroscope() {
        bitField0_ = (bitField0_ & ~0x00000008);
        gyroscope_ = null;
        if (gyroscopeBuilder_ != null) {
          gyroscopeBuilder_.dispose();
          gyroscopeBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.Builder getGyroscopeBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getGyroscopeFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GyroscopeOrBuilder getGyroscopeOrBuilder() {
        if (gyroscopeBuilder_ != null) {
          return gyroscopeBuilder_.getMessageOrBuilder();
        } else {
          return gyroscope_ == null ?
              com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.getDefaultInstance() : gyroscope_;
        }
      }
      /**
       * <code>optional .Imu.Gyroscope gyroscope = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GyroscopeOrBuilder> 
          getGyroscopeFieldBuilder() {
        if (gyroscopeBuilder_ == null) {
          gyroscopeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope, com.nichesolv.nds.model.proto.model.ImuProto.Imu.Gyroscope.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GyroscopeOrBuilder>(
                  getGyroscope(),
                  getParentForChildren(),
                  isClean());
          gyroscope_ = null;
        }
        return gyroscopeBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector gravitationalVector_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVectorOrBuilder> gravitationalVectorBuilder_;
      /**
       * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
       * @return Whether the gravitationalVector field is set.
       */
      public boolean hasGravitationalVector() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
       * @return The gravitationalVector.
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector getGravitationalVector() {
        if (gravitationalVectorBuilder_ == null) {
          return gravitationalVector_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.getDefaultInstance() : gravitationalVector_;
        } else {
          return gravitationalVectorBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
       */
      public Builder setGravitationalVector(com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector value) {
        if (gravitationalVectorBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          gravitationalVector_ = value;
        } else {
          gravitationalVectorBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
       */
      public Builder setGravitationalVector(
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.Builder builderForValue) {
        if (gravitationalVectorBuilder_ == null) {
          gravitationalVector_ = builderForValue.build();
        } else {
          gravitationalVectorBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
       */
      public Builder mergeGravitationalVector(com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector value) {
        if (gravitationalVectorBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
            gravitationalVector_ != null &&
            gravitationalVector_ != com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.getDefaultInstance()) {
            getGravitationalVectorBuilder().mergeFrom(value);
          } else {
            gravitationalVector_ = value;
          }
        } else {
          gravitationalVectorBuilder_.mergeFrom(value);
        }
        if (gravitationalVector_ != null) {
          bitField0_ |= 0x00000010;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
       */
      public Builder clearGravitationalVector() {
        bitField0_ = (bitField0_ & ~0x00000010);
        gravitationalVector_ = null;
        if (gravitationalVectorBuilder_ != null) {
          gravitationalVectorBuilder_.dispose();
          gravitationalVectorBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.Builder getGravitationalVectorBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getGravitationalVectorFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVectorOrBuilder getGravitationalVectorOrBuilder() {
        if (gravitationalVectorBuilder_ != null) {
          return gravitationalVectorBuilder_.getMessageOrBuilder();
        } else {
          return gravitationalVector_ == null ?
              com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.getDefaultInstance() : gravitationalVector_;
        }
      }
      /**
       * <code>optional .Imu.GravitationalVector gravitational_vector = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVectorOrBuilder> 
          getGravitationalVectorFieldBuilder() {
        if (gravitationalVectorBuilder_ == null) {
          gravitationalVectorBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVector.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.GravitationalVectorOrBuilder>(
                  getGravitationalVector(),
                  getParentForChildren(),
                  isClean());
          gravitationalVector_ = null;
        }
        return gravitationalVectorBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput digitalInput_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput, com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInputOrBuilder> digitalInputBuilder_;
      /**
       * <code>optional .Imu.DigitalInput digital_input = 6;</code>
       * @return Whether the digitalInput field is set.
       */
      public boolean hasDigitalInput() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional .Imu.DigitalInput digital_input = 6;</code>
       * @return The digitalInput.
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput getDigitalInput() {
        if (digitalInputBuilder_ == null) {
          return digitalInput_ == null ? com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.getDefaultInstance() : digitalInput_;
        } else {
          return digitalInputBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .Imu.DigitalInput digital_input = 6;</code>
       */
      public Builder setDigitalInput(com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput value) {
        if (digitalInputBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          digitalInput_ = value;
        } else {
          digitalInputBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.DigitalInput digital_input = 6;</code>
       */
      public Builder setDigitalInput(
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.Builder builderForValue) {
        if (digitalInputBuilder_ == null) {
          digitalInput_ = builderForValue.build();
        } else {
          digitalInputBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.DigitalInput digital_input = 6;</code>
       */
      public Builder mergeDigitalInput(com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput value) {
        if (digitalInputBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
            digitalInput_ != null &&
            digitalInput_ != com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.getDefaultInstance()) {
            getDigitalInputBuilder().mergeFrom(value);
          } else {
            digitalInput_ = value;
          }
        } else {
          digitalInputBuilder_.mergeFrom(value);
        }
        if (digitalInput_ != null) {
          bitField0_ |= 0x00000020;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional .Imu.DigitalInput digital_input = 6;</code>
       */
      public Builder clearDigitalInput() {
        bitField0_ = (bitField0_ & ~0x00000020);
        digitalInput_ = null;
        if (digitalInputBuilder_ != null) {
          digitalInputBuilder_.dispose();
          digitalInputBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>optional .Imu.DigitalInput digital_input = 6;</code>
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.Builder getDigitalInputBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getDigitalInputFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .Imu.DigitalInput digital_input = 6;</code>
       */
      public com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInputOrBuilder getDigitalInputOrBuilder() {
        if (digitalInputBuilder_ != null) {
          return digitalInputBuilder_.getMessageOrBuilder();
        } else {
          return digitalInput_ == null ?
              com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.getDefaultInstance() : digitalInput_;
        }
      }
      /**
       * <code>optional .Imu.DigitalInput digital_input = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput, com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInputOrBuilder> 
          getDigitalInputFieldBuilder() {
        if (digitalInputBuilder_ == null) {
          digitalInputBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput, com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInput.Builder, com.nichesolv.nds.model.proto.model.ImuProto.Imu.DigitalInputOrBuilder>(
                  getDigitalInput(),
                  getParentForChildren(),
                  isClean());
          digitalInput_ = null;
        }
        return digitalInputBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Imu)
    }

    // @@protoc_insertion_point(class_scope:Imu)
    private static final com.nichesolv.nds.model.proto.model.ImuProto.Imu DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.ImuProto.Imu();
    }

    public static com.nichesolv.nds.model.proto.model.ImuProto.Imu getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Imu>
        PARSER = new com.google.protobuf.AbstractParser<Imu>() {
      @java.lang.Override
      public Imu parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Imu> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Imu> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.ImuProto.Imu getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Imu_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Imu_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Imu_Accelerometer_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Imu_Accelerometer_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Imu_Gyroscope_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Imu_Gyroscope_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Imu_GravitationalVector_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Imu_GravitationalVector_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Imu_DigitalInput_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Imu_DigitalInput_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\'com/nichesolv/nds/model/proto/imu.prot" +
      "o\032-com/nichesolv/nds/model/proto/timesta" +
      "mp.proto\032,com/nichesolv/nds/model/proto/" +
      "metadata.proto\"\237\005\n\003Imu\022\033\n\010metadata\030\001 \001(\013" +
      "2\t.Metadata\022\035\n\ttimestamp\030\002 \001(\0132\n.Timesta" +
      "mp\022.\n\raccelerometer\030\003 \001(\0132\022.Imu.Accelero" +
      "meterH\000\210\001\001\022&\n\tgyroscope\030\004 \001(\0132\016.Imu.Gyro" +
      "scopeH\001\210\001\001\022;\n\024gravitational_vector\030\005 \001(\013" +
      "2\030.Imu.GravitationalVectorH\002\210\001\001\022-\n\rdigit" +
      "al_input\030\006 \001(\0132\021.Imu.DigitalInputH\003\210\001\001\032Q" +
      "\n\rAccelerometer\022\016\n\001x\030\001 \001(\021H\000\210\001\001\022\016\n\001y\030\002 \001" +
      "(\021H\001\210\001\001\022\016\n\001z\030\003 \001(\021H\002\210\001\001B\004\n\002_xB\004\n\002_yB\004\n\002_" +
      "z\032M\n\tGyroscope\022\016\n\001x\030\001 \001(\021H\000\210\001\001\022\016\n\001y\030\002 \001(" +
      "\021H\001\210\001\001\022\016\n\001z\030\003 \001(\021H\002\210\001\001B\004\n\002_xB\004\n\002_yB\004\n\002_z" +
      "\032W\n\023GravitationalVector\022\016\n\001x\030\001 \001(\002H\000\210\001\001\022" +
      "\016\n\001y\030\002 \001(\002H\001\210\001\001\022\016\n\001z\030\003 \001(\002H\002\210\001\001B\004\n\002_xB\004\n" +
      "\002_yB\004\n\002_z\032R\n\014DigitalInput\022\023\n\006motion\030\001 \001(" +
      "\010H\000\210\001\001\022\025\n\010ignition\030\002 \001(\010H\001\210\001\001B\t\n\007_motion" +
      "B\013\n\t_ignitionB\020\n\016_accelerometerB\014\n\n_gyro" +
      "scopeB\027\n\025_gravitational_vectorB\020\n\016_digit" +
      "al_inputB/\n#com.nichesolv.nds.model.prot" +
      "o.modelB\010ImuProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor(),
        });
    internal_static_Imu_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Imu_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Imu_descriptor,
        new java.lang.String[] { "Metadata", "Timestamp", "Accelerometer", "Gyroscope", "GravitationalVector", "DigitalInput", });
    internal_static_Imu_Accelerometer_descriptor =
      internal_static_Imu_descriptor.getNestedTypes().get(0);
    internal_static_Imu_Accelerometer_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Imu_Accelerometer_descriptor,
        new java.lang.String[] { "X", "Y", "Z", });
    internal_static_Imu_Gyroscope_descriptor =
      internal_static_Imu_descriptor.getNestedTypes().get(1);
    internal_static_Imu_Gyroscope_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Imu_Gyroscope_descriptor,
        new java.lang.String[] { "X", "Y", "Z", });
    internal_static_Imu_GravitationalVector_descriptor =
      internal_static_Imu_descriptor.getNestedTypes().get(2);
    internal_static_Imu_GravitationalVector_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Imu_GravitationalVector_descriptor,
        new java.lang.String[] { "X", "Y", "Z", });
    internal_static_Imu_DigitalInput_descriptor =
      internal_static_Imu_descriptor.getNestedTypes().get(3);
    internal_static_Imu_DigitalInput_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Imu_DigitalInput_descriptor,
        new java.lang.String[] { "Motion", "Ignition", });
    com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
