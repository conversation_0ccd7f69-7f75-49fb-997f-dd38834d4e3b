// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/location.proto

package com.nichesolv.nds.model.proto.model;

public final class LocationProto {
  private LocationProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface LocationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Location)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional double latitude = 1;</code>
     * @return Whether the latitude field is set.
     */
    boolean hasLatitude();
    /**
     * <code>optional double latitude = 1;</code>
     * @return The latitude.
     */
    double getLatitude();

    /**
     * <code>optional double longitude = 3;</code>
     * @return Whether the longitude field is set.
     */
    boolean hasLongitude();
    /**
     * <code>optional double longitude = 3;</code>
     * @return The longitude.
     */
    double getLongitude();

    /**
     * <code>optional float altitude = 5;</code>
     * @return Whether the altitude field is set.
     */
    boolean hasAltitude();
    /**
     * <code>optional float altitude = 5;</code>
     * @return The altitude.
     */
    float getAltitude();

    /**
     * <code>optional float speed = 7;</code>
     * @return Whether the speed field is set.
     */
    boolean hasSpeed();
    /**
     * <code>optional float speed = 7;</code>
     * @return The speed.
     */
    float getSpeed();

    /**
     * <code>optional float bearing = 9;</code>
     * @return Whether the bearing field is set.
     */
    boolean hasBearing();
    /**
     * <code>optional float bearing = 9;</code>
     * @return The bearing.
     */
    float getBearing();

    /**
     * <code>optional sint32 pdop = 11;</code>
     * @return Whether the pdop field is set.
     */
    boolean hasPdop();
    /**
     * <code>optional sint32 pdop = 11;</code>
     * @return The pdop.
     */
    int getPdop();

    /**
     * <code>optional sint32 hdop = 13;</code>
     * @return Whether the hdop field is set.
     */
    boolean hasHdop();
    /**
     * <code>optional sint32 hdop = 13;</code>
     * @return The hdop.
     */
    int getHdop();

    /**
     * <code>optional sint32 vdop = 15;</code>
     * @return Whether the vdop field is set.
     */
    boolean hasVdop();
    /**
     * <code>optional sint32 vdop = 15;</code>
     * @return The vdop.
     */
    int getVdop();

    /**
     * <code>optional sint32 view_sats = 17;</code>
     * @return Whether the viewSats field is set.
     */
    boolean hasViewSats();
    /**
     * <code>optional sint32 view_sats = 17;</code>
     * @return The viewSats.
     */
    int getViewSats();

    /**
     * <code>optional sint32 track_sats = 19;</code>
     * @return Whether the trackSats field is set.
     */
    boolean hasTrackSats();
    /**
     * <code>optional sint32 track_sats = 19;</code>
     * @return The trackSats.
     */
    int getTrackSats();

    /**
     * <code>.Timestamp timestamp = 20;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <code>.Timestamp timestamp = 20;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <code>.Timestamp timestamp = 20;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <code>.Metadata metadata = 21;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <code>.Metadata metadata = 21;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <code>.Metadata metadata = 21;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();
  }
  /**
   * <pre>
   * This message represents the location event.
   * </pre>
   *
   * Protobuf type {@code Location}
   */
  public static final class Location extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Location)
      LocationOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Location.newBuilder() to construct.
    private Location(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Location() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Location();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.LocationProto.internal_static_Location_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.LocationProto.internal_static_Location_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.LocationProto.Location.class, com.nichesolv.nds.model.proto.model.LocationProto.Location.Builder.class);
    }

    private int bitField0_;
    public static final int LATITUDE_FIELD_NUMBER = 1;
    private double latitude_ = 0D;
    /**
     * <code>optional double latitude = 1;</code>
     * @return Whether the latitude field is set.
     */
    @java.lang.Override
    public boolean hasLatitude() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional double latitude = 1;</code>
     * @return The latitude.
     */
    @java.lang.Override
    public double getLatitude() {
      return latitude_;
    }

    public static final int LONGITUDE_FIELD_NUMBER = 3;
    private double longitude_ = 0D;
    /**
     * <code>optional double longitude = 3;</code>
     * @return Whether the longitude field is set.
     */
    @java.lang.Override
    public boolean hasLongitude() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional double longitude = 3;</code>
     * @return The longitude.
     */
    @java.lang.Override
    public double getLongitude() {
      return longitude_;
    }

    public static final int ALTITUDE_FIELD_NUMBER = 5;
    private float altitude_ = 0F;
    /**
     * <code>optional float altitude = 5;</code>
     * @return Whether the altitude field is set.
     */
    @java.lang.Override
    public boolean hasAltitude() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional float altitude = 5;</code>
     * @return The altitude.
     */
    @java.lang.Override
    public float getAltitude() {
      return altitude_;
    }

    public static final int SPEED_FIELD_NUMBER = 7;
    private float speed_ = 0F;
    /**
     * <code>optional float speed = 7;</code>
     * @return Whether the speed field is set.
     */
    @java.lang.Override
    public boolean hasSpeed() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional float speed = 7;</code>
     * @return The speed.
     */
    @java.lang.Override
    public float getSpeed() {
      return speed_;
    }

    public static final int BEARING_FIELD_NUMBER = 9;
    private float bearing_ = 0F;
    /**
     * <code>optional float bearing = 9;</code>
     * @return Whether the bearing field is set.
     */
    @java.lang.Override
    public boolean hasBearing() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional float bearing = 9;</code>
     * @return The bearing.
     */
    @java.lang.Override
    public float getBearing() {
      return bearing_;
    }

    public static final int PDOP_FIELD_NUMBER = 11;
    private int pdop_ = 0;
    /**
     * <code>optional sint32 pdop = 11;</code>
     * @return Whether the pdop field is set.
     */
    @java.lang.Override
    public boolean hasPdop() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional sint32 pdop = 11;</code>
     * @return The pdop.
     */
    @java.lang.Override
    public int getPdop() {
      return pdop_;
    }

    public static final int HDOP_FIELD_NUMBER = 13;
    private int hdop_ = 0;
    /**
     * <code>optional sint32 hdop = 13;</code>
     * @return Whether the hdop field is set.
     */
    @java.lang.Override
    public boolean hasHdop() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional sint32 hdop = 13;</code>
     * @return The hdop.
     */
    @java.lang.Override
    public int getHdop() {
      return hdop_;
    }

    public static final int VDOP_FIELD_NUMBER = 15;
    private int vdop_ = 0;
    /**
     * <code>optional sint32 vdop = 15;</code>
     * @return Whether the vdop field is set.
     */
    @java.lang.Override
    public boolean hasVdop() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional sint32 vdop = 15;</code>
     * @return The vdop.
     */
    @java.lang.Override
    public int getVdop() {
      return vdop_;
    }

    public static final int VIEW_SATS_FIELD_NUMBER = 17;
    private int viewSats_ = 0;
    /**
     * <code>optional sint32 view_sats = 17;</code>
     * @return Whether the viewSats field is set.
     */
    @java.lang.Override
    public boolean hasViewSats() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional sint32 view_sats = 17;</code>
     * @return The viewSats.
     */
    @java.lang.Override
    public int getViewSats() {
      return viewSats_;
    }

    public static final int TRACK_SATS_FIELD_NUMBER = 19;
    private int trackSats_ = 0;
    /**
     * <code>optional sint32 track_sats = 19;</code>
     * @return Whether the trackSats field is set.
     */
    @java.lang.Override
    public boolean hasTrackSats() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional sint32 track_sats = 19;</code>
     * @return The trackSats.
     */
    @java.lang.Override
    public int getTrackSats() {
      return trackSats_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 20;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <code>.Timestamp timestamp = 20;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>.Timestamp timestamp = 20;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <code>.Timestamp timestamp = 20;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }

    public static final int METADATA_FIELD_NUMBER = 21;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <code>.Metadata metadata = 21;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>.Metadata metadata = 21;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <code>.Metadata metadata = 21;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeDouble(1, latitude_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeDouble(3, longitude_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeFloat(5, altitude_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeFloat(7, speed_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeFloat(9, bearing_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeSInt32(11, pdop_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeSInt32(13, hdop_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeSInt32(15, vdop_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeSInt32(17, viewSats_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeSInt32(19, trackSats_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeMessage(20, getTimestamp());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeMessage(21, getMetadata());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(1, latitude_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(3, longitude_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(5, altitude_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(7, speed_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(9, bearing_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(11, pdop_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(13, hdop_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(15, vdop_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(17, viewSats_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(19, trackSats_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(20, getTimestamp());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(21, getMetadata());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.LocationProto.Location)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.LocationProto.Location other = (com.nichesolv.nds.model.proto.model.LocationProto.Location) obj;

      if (hasLatitude() != other.hasLatitude()) return false;
      if (hasLatitude()) {
        if (java.lang.Double.doubleToLongBits(getLatitude())
            != java.lang.Double.doubleToLongBits(
                other.getLatitude())) return false;
      }
      if (hasLongitude() != other.hasLongitude()) return false;
      if (hasLongitude()) {
        if (java.lang.Double.doubleToLongBits(getLongitude())
            != java.lang.Double.doubleToLongBits(
                other.getLongitude())) return false;
      }
      if (hasAltitude() != other.hasAltitude()) return false;
      if (hasAltitude()) {
        if (java.lang.Float.floatToIntBits(getAltitude())
            != java.lang.Float.floatToIntBits(
                other.getAltitude())) return false;
      }
      if (hasSpeed() != other.hasSpeed()) return false;
      if (hasSpeed()) {
        if (java.lang.Float.floatToIntBits(getSpeed())
            != java.lang.Float.floatToIntBits(
                other.getSpeed())) return false;
      }
      if (hasBearing() != other.hasBearing()) return false;
      if (hasBearing()) {
        if (java.lang.Float.floatToIntBits(getBearing())
            != java.lang.Float.floatToIntBits(
                other.getBearing())) return false;
      }
      if (hasPdop() != other.hasPdop()) return false;
      if (hasPdop()) {
        if (getPdop()
            != other.getPdop()) return false;
      }
      if (hasHdop() != other.hasHdop()) return false;
      if (hasHdop()) {
        if (getHdop()
            != other.getHdop()) return false;
      }
      if (hasVdop() != other.hasVdop()) return false;
      if (hasVdop()) {
        if (getVdop()
            != other.getVdop()) return false;
      }
      if (hasViewSats() != other.hasViewSats()) return false;
      if (hasViewSats()) {
        if (getViewSats()
            != other.getViewSats()) return false;
      }
      if (hasTrackSats() != other.hasTrackSats()) return false;
      if (hasTrackSats()) {
        if (getTrackSats()
            != other.getTrackSats()) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasLatitude()) {
        hash = (37 * hash) + LATITUDE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            java.lang.Double.doubleToLongBits(getLatitude()));
      }
      if (hasLongitude()) {
        hash = (37 * hash) + LONGITUDE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            java.lang.Double.doubleToLongBits(getLongitude()));
      }
      if (hasAltitude()) {
        hash = (37 * hash) + ALTITUDE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getAltitude());
      }
      if (hasSpeed()) {
        hash = (37 * hash) + SPEED_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getSpeed());
      }
      if (hasBearing()) {
        hash = (37 * hash) + BEARING_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getBearing());
      }
      if (hasPdop()) {
        hash = (37 * hash) + PDOP_FIELD_NUMBER;
        hash = (53 * hash) + getPdop();
      }
      if (hasHdop()) {
        hash = (37 * hash) + HDOP_FIELD_NUMBER;
        hash = (53 * hash) + getHdop();
      }
      if (hasVdop()) {
        hash = (37 * hash) + VDOP_FIELD_NUMBER;
        hash = (53 * hash) + getVdop();
      }
      if (hasViewSats()) {
        hash = (37 * hash) + VIEW_SATS_FIELD_NUMBER;
        hash = (53 * hash) + getViewSats();
      }
      if (hasTrackSats()) {
        hash = (37 * hash) + TRACK_SATS_FIELD_NUMBER;
        hash = (53 * hash) + getTrackSats();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.LocationProto.Location parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.LocationProto.Location prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * This message represents the location event.
     * </pre>
     *
     * Protobuf type {@code Location}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Location)
        com.nichesolv.nds.model.proto.model.LocationProto.LocationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.LocationProto.internal_static_Location_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.LocationProto.internal_static_Location_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.LocationProto.Location.class, com.nichesolv.nds.model.proto.model.LocationProto.Location.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.LocationProto.Location.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTimestampFieldBuilder();
          getMetadataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        latitude_ = 0D;
        longitude_ = 0D;
        altitude_ = 0F;
        speed_ = 0F;
        bearing_ = 0F;
        pdop_ = 0;
        hdop_ = 0;
        vdop_ = 0;
        viewSats_ = 0;
        trackSats_ = 0;
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.LocationProto.internal_static_Location_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.LocationProto.Location getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.LocationProto.Location.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.LocationProto.Location build() {
        com.nichesolv.nds.model.proto.model.LocationProto.Location result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.LocationProto.Location buildPartial() {
        com.nichesolv.nds.model.proto.model.LocationProto.Location result = new com.nichesolv.nds.model.proto.model.LocationProto.Location(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.LocationProto.Location result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.latitude_ = latitude_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.longitude_ = longitude_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.altitude_ = altitude_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.speed_ = speed_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.bearing_ = bearing_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.pdop_ = pdop_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.hdop_ = hdop_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.vdop_ = vdop_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.viewSats_ = viewSats_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.trackSats_ = trackSats_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.timestamp_ = timestampBuilder_ == null
              ? timestamp_
              : timestampBuilder_.build();
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.metadata_ = metadataBuilder_ == null
              ? metadata_
              : metadataBuilder_.build();
          to_bitField0_ |= 0x00000800;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.LocationProto.Location) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.LocationProto.Location)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.LocationProto.Location other) {
        if (other == com.nichesolv.nds.model.proto.model.LocationProto.Location.getDefaultInstance()) return this;
        if (other.hasLatitude()) {
          setLatitude(other.getLatitude());
        }
        if (other.hasLongitude()) {
          setLongitude(other.getLongitude());
        }
        if (other.hasAltitude()) {
          setAltitude(other.getAltitude());
        }
        if (other.hasSpeed()) {
          setSpeed(other.getSpeed());
        }
        if (other.hasBearing()) {
          setBearing(other.getBearing());
        }
        if (other.hasPdop()) {
          setPdop(other.getPdop());
        }
        if (other.hasHdop()) {
          setHdop(other.getHdop());
        }
        if (other.hasVdop()) {
          setVdop(other.getVdop());
        }
        if (other.hasViewSats()) {
          setViewSats(other.getViewSats());
        }
        if (other.hasTrackSats()) {
          setTrackSats(other.getTrackSats());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 9: {
                latitude_ = input.readDouble();
                bitField0_ |= 0x00000001;
                break;
              } // case 9
              case 25: {
                longitude_ = input.readDouble();
                bitField0_ |= 0x00000002;
                break;
              } // case 25
              case 45: {
                altitude_ = input.readFloat();
                bitField0_ |= 0x00000004;
                break;
              } // case 45
              case 61: {
                speed_ = input.readFloat();
                bitField0_ |= 0x00000008;
                break;
              } // case 61
              case 77: {
                bearing_ = input.readFloat();
                bitField0_ |= 0x00000010;
                break;
              } // case 77
              case 88: {
                pdop_ = input.readSInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 88
              case 104: {
                hdop_ = input.readSInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 104
              case 120: {
                vdop_ = input.readSInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 120
              case 136: {
                viewSats_ = input.readSInt32();
                bitField0_ |= 0x00000100;
                break;
              } // case 136
              case 152: {
                trackSats_ = input.readSInt32();
                bitField0_ |= 0x00000200;
                break;
              } // case 152
              case 162: {
                input.readMessage(
                    getTimestampFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000400;
                break;
              } // case 162
              case 170: {
                input.readMessage(
                    getMetadataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000800;
                break;
              } // case 170
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private double latitude_ ;
      /**
       * <code>optional double latitude = 1;</code>
       * @return Whether the latitude field is set.
       */
      @java.lang.Override
      public boolean hasLatitude() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional double latitude = 1;</code>
       * @return The latitude.
       */
      @java.lang.Override
      public double getLatitude() {
        return latitude_;
      }
      /**
       * <code>optional double latitude = 1;</code>
       * @param value The latitude to set.
       * @return This builder for chaining.
       */
      public Builder setLatitude(double value) {

        latitude_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional double latitude = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLatitude() {
        bitField0_ = (bitField0_ & ~0x00000001);
        latitude_ = 0D;
        onChanged();
        return this;
      }

      private double longitude_ ;
      /**
       * <code>optional double longitude = 3;</code>
       * @return Whether the longitude field is set.
       */
      @java.lang.Override
      public boolean hasLongitude() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional double longitude = 3;</code>
       * @return The longitude.
       */
      @java.lang.Override
      public double getLongitude() {
        return longitude_;
      }
      /**
       * <code>optional double longitude = 3;</code>
       * @param value The longitude to set.
       * @return This builder for chaining.
       */
      public Builder setLongitude(double value) {

        longitude_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional double longitude = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLongitude() {
        bitField0_ = (bitField0_ & ~0x00000002);
        longitude_ = 0D;
        onChanged();
        return this;
      }

      private float altitude_ ;
      /**
       * <code>optional float altitude = 5;</code>
       * @return Whether the altitude field is set.
       */
      @java.lang.Override
      public boolean hasAltitude() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional float altitude = 5;</code>
       * @return The altitude.
       */
      @java.lang.Override
      public float getAltitude() {
        return altitude_;
      }
      /**
       * <code>optional float altitude = 5;</code>
       * @param value The altitude to set.
       * @return This builder for chaining.
       */
      public Builder setAltitude(float value) {

        altitude_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional float altitude = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearAltitude() {
        bitField0_ = (bitField0_ & ~0x00000004);
        altitude_ = 0F;
        onChanged();
        return this;
      }

      private float speed_ ;
      /**
       * <code>optional float speed = 7;</code>
       * @return Whether the speed field is set.
       */
      @java.lang.Override
      public boolean hasSpeed() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional float speed = 7;</code>
       * @return The speed.
       */
      @java.lang.Override
      public float getSpeed() {
        return speed_;
      }
      /**
       * <code>optional float speed = 7;</code>
       * @param value The speed to set.
       * @return This builder for chaining.
       */
      public Builder setSpeed(float value) {

        speed_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional float speed = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearSpeed() {
        bitField0_ = (bitField0_ & ~0x00000008);
        speed_ = 0F;
        onChanged();
        return this;
      }

      private float bearing_ ;
      /**
       * <code>optional float bearing = 9;</code>
       * @return Whether the bearing field is set.
       */
      @java.lang.Override
      public boolean hasBearing() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional float bearing = 9;</code>
       * @return The bearing.
       */
      @java.lang.Override
      public float getBearing() {
        return bearing_;
      }
      /**
       * <code>optional float bearing = 9;</code>
       * @param value The bearing to set.
       * @return This builder for chaining.
       */
      public Builder setBearing(float value) {

        bearing_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional float bearing = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearBearing() {
        bitField0_ = (bitField0_ & ~0x00000010);
        bearing_ = 0F;
        onChanged();
        return this;
      }

      private int pdop_ ;
      /**
       * <code>optional sint32 pdop = 11;</code>
       * @return Whether the pdop field is set.
       */
      @java.lang.Override
      public boolean hasPdop() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional sint32 pdop = 11;</code>
       * @return The pdop.
       */
      @java.lang.Override
      public int getPdop() {
        return pdop_;
      }
      /**
       * <code>optional sint32 pdop = 11;</code>
       * @param value The pdop to set.
       * @return This builder for chaining.
       */
      public Builder setPdop(int value) {

        pdop_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 pdop = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearPdop() {
        bitField0_ = (bitField0_ & ~0x00000020);
        pdop_ = 0;
        onChanged();
        return this;
      }

      private int hdop_ ;
      /**
       * <code>optional sint32 hdop = 13;</code>
       * @return Whether the hdop field is set.
       */
      @java.lang.Override
      public boolean hasHdop() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional sint32 hdop = 13;</code>
       * @return The hdop.
       */
      @java.lang.Override
      public int getHdop() {
        return hdop_;
      }
      /**
       * <code>optional sint32 hdop = 13;</code>
       * @param value The hdop to set.
       * @return This builder for chaining.
       */
      public Builder setHdop(int value) {

        hdop_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 hdop = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearHdop() {
        bitField0_ = (bitField0_ & ~0x00000040);
        hdop_ = 0;
        onChanged();
        return this;
      }

      private int vdop_ ;
      /**
       * <code>optional sint32 vdop = 15;</code>
       * @return Whether the vdop field is set.
       */
      @java.lang.Override
      public boolean hasVdop() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional sint32 vdop = 15;</code>
       * @return The vdop.
       */
      @java.lang.Override
      public int getVdop() {
        return vdop_;
      }
      /**
       * <code>optional sint32 vdop = 15;</code>
       * @param value The vdop to set.
       * @return This builder for chaining.
       */
      public Builder setVdop(int value) {

        vdop_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 vdop = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearVdop() {
        bitField0_ = (bitField0_ & ~0x00000080);
        vdop_ = 0;
        onChanged();
        return this;
      }

      private int viewSats_ ;
      /**
       * <code>optional sint32 view_sats = 17;</code>
       * @return Whether the viewSats field is set.
       */
      @java.lang.Override
      public boolean hasViewSats() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional sint32 view_sats = 17;</code>
       * @return The viewSats.
       */
      @java.lang.Override
      public int getViewSats() {
        return viewSats_;
      }
      /**
       * <code>optional sint32 view_sats = 17;</code>
       * @param value The viewSats to set.
       * @return This builder for chaining.
       */
      public Builder setViewSats(int value) {

        viewSats_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 view_sats = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearViewSats() {
        bitField0_ = (bitField0_ & ~0x00000100);
        viewSats_ = 0;
        onChanged();
        return this;
      }

      private int trackSats_ ;
      /**
       * <code>optional sint32 track_sats = 19;</code>
       * @return Whether the trackSats field is set.
       */
      @java.lang.Override
      public boolean hasTrackSats() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional sint32 track_sats = 19;</code>
       * @return The trackSats.
       */
      @java.lang.Override
      public int getTrackSats() {
        return trackSats_;
      }
      /**
       * <code>optional sint32 track_sats = 19;</code>
       * @param value The trackSats to set.
       * @return This builder for chaining.
       */
      public Builder setTrackSats(int value) {

        trackSats_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 track_sats = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearTrackSats() {
        bitField0_ = (bitField0_ & ~0x00000200);
        trackSats_ = 0;
        onChanged();
        return this;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <code>.Timestamp timestamp = 20;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>.Timestamp timestamp = 20;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <code>.Timestamp timestamp = 20;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
        } else {
          timestampBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 20;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 20;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (((bitField0_ & 0x00000400) != 0) &&
            timestamp_ != null &&
            timestamp_ != com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance()) {
            getTimestampBuilder().mergeFrom(value);
          } else {
            timestamp_ = value;
          }
        } else {
          timestampBuilder_.mergeFrom(value);
        }
        if (timestamp_ != null) {
          bitField0_ |= 0x00000400;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 20;</code>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000400);
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 20;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        bitField0_ |= 0x00000400;
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <code>.Timestamp timestamp = 20;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <code>.Timestamp timestamp = 20;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <code>.Metadata metadata = 21;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>.Metadata metadata = 21;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Metadata metadata = 21;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
        } else {
          metadataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 21;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 21;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (((bitField0_ & 0x00000800) != 0) &&
            metadata_ != null &&
            metadata_ != com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance()) {
            getMetadataBuilder().mergeFrom(value);
          } else {
            metadata_ = value;
          }
        } else {
          metadataBuilder_.mergeFrom(value);
        }
        if (metadata_ != null) {
          bitField0_ |= 0x00000800;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Metadata metadata = 21;</code>
       */
      public Builder clearMetadata() {
        bitField0_ = (bitField0_ & ~0x00000800);
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 21;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        bitField0_ |= 0x00000800;
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Metadata metadata = 21;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <code>.Metadata metadata = 21;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Location)
    }

    // @@protoc_insertion_point(class_scope:Location)
    private static final com.nichesolv.nds.model.proto.model.LocationProto.Location DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.LocationProto.Location();
    }

    public static com.nichesolv.nds.model.proto.model.LocationProto.Location getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Location>
        PARSER = new com.google.protobuf.AbstractParser<Location>() {
      @java.lang.Override
      public Location parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Location> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Location> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.LocationProto.Location getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Location_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Location_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n,com/nichesolv/nds/model/proto/location" +
      ".proto\032-com/nichesolv/nds/model/proto/ti" +
      "mestamp.proto\032,com/nichesolv/nds/model/p" +
      "roto/metadata.proto\"\226\003\n\010Location\022\025\n\010lati" +
      "tude\030\001 \001(\001H\000\210\001\001\022\026\n\tlongitude\030\003 \001(\001H\001\210\001\001\022" +
      "\025\n\010altitude\030\005 \001(\002H\002\210\001\001\022\022\n\005speed\030\007 \001(\002H\003\210" +
      "\001\001\022\024\n\007bearing\030\t \001(\002H\004\210\001\001\022\021\n\004pdop\030\013 \001(\021H\005" +
      "\210\001\001\022\021\n\004hdop\030\r \001(\021H\006\210\001\001\022\021\n\004vdop\030\017 \001(\021H\007\210\001" +
      "\001\022\026\n\tview_sats\030\021 \001(\021H\010\210\001\001\022\027\n\ntrack_sats\030" +
      "\023 \001(\021H\t\210\001\001\022\035\n\ttimestamp\030\024 \001(\0132\n.Timestam" +
      "p\022\033\n\010metadata\030\025 \001(\0132\t.MetadataB\013\n\t_latit" +
      "udeB\014\n\n_longitudeB\013\n\t_altitudeB\010\n\006_speed" +
      "B\n\n\010_bearingB\007\n\005_pdopB\007\n\005_hdopB\007\n\005_vdopB" +
      "\014\n\n_view_satsB\r\n\013_track_satsB4\n#com.nich" +
      "esolv.nds.model.proto.modelB\rLocationPro" +
      "tob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor(),
        });
    internal_static_Location_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Location_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Location_descriptor,
        new java.lang.String[] { "Latitude", "Longitude", "Altitude", "Speed", "Bearing", "Pdop", "Hdop", "Vdop", "ViewSats", "TrackSats", "Timestamp", "Metadata", "Latitude", "Longitude", "Altitude", "Speed", "Bearing", "Pdop", "Hdop", "Vdop", "ViewSats", "TrackSats", });
    com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
