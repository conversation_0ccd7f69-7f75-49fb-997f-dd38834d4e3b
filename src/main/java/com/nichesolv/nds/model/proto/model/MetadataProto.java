// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/metadata.proto

package com.nichesolv.nds.model.proto.model;

public final class MetadataProto {
  private MetadataProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MetadataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Metadata)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * Imei is the main identifier of the vehicle. This field is required.
     * </pre>
     *
     * <code>uint64 imei = 1;</code>
     * @return The imei.
     */
    long getImei();

    /**
     * <pre>
     * Correlation id is a unique identifier given to each payload.
     * </pre>
     *
     * <code>string correlation_id = 2;</code>
     * @return The correlationId.
     */
    java.lang.String getCorrelationId();
    /**
     * <pre>
     * Correlation id is a unique identifier given to each payload.
     * </pre>
     *
     * <code>string correlation_id = 2;</code>
     * @return The bytes for correlationId.
     */
    com.google.protobuf.ByteString
        getCorrelationIdBytes();

    /**
     * <pre>
     * This fields tells if the data is old or new.
     * </pre>
     *
     * <code>optional string magic = 3;</code>
     * @return Whether the magic field is set.
     */
    boolean hasMagic();
    /**
     * <pre>
     * This fields tells if the data is old or new.
     * </pre>
     *
     * <code>optional string magic = 3;</code>
     * @return The magic.
     */
    java.lang.String getMagic();
    /**
     * <pre>
     * This fields tells if the data is old or new.
     * </pre>
     *
     * <code>optional string magic = 3;</code>
     * @return The bytes for magic.
     */
    com.google.protobuf.ByteString
        getMagicBytes();

    /**
     * <pre>
     * Has no use.
     * </pre>
     *
     * <code>optional sint32 sqn = 4;</code>
     * @return Whether the sqn field is set.
     */
    boolean hasSqn();
    /**
     * <pre>
     * Has no use.
     * </pre>
     *
     * <code>optional sint32 sqn = 4;</code>
     * @return The sqn.
     */
    int getSqn();

    /**
     * <pre>
     * Has no use.
     * </pre>
     *
     * <code>optional int32 crc16 = 5;</code>
     * @return Whether the crc16 field is set.
     */
    boolean hasCrc16();
    /**
     * <pre>
     * Has no use.
     * </pre>
     *
     * <code>optional int32 crc16 = 5;</code>
     * @return The crc16.
     */
    int getCrc16();
  }
  /**
   * <pre>
   * This class represents the metadata associated with each payload.
   * </pre>
   *
   * Protobuf type {@code Metadata}
   */
  public static final class Metadata extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Metadata)
      MetadataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Metadata.newBuilder() to construct.
    private Metadata(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Metadata() {
      correlationId_ = "";
      magic_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Metadata();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.MetadataProto.internal_static_Metadata_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.MetadataProto.internal_static_Metadata_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.class, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder.class);
    }

    private int bitField0_;
    public static final int IMEI_FIELD_NUMBER = 1;
    private long imei_ = 0L;
    /**
     * <pre>
     * Imei is the main identifier of the vehicle. This field is required.
     * </pre>
     *
     * <code>uint64 imei = 1;</code>
     * @return The imei.
     */
    @java.lang.Override
    public long getImei() {
      return imei_;
    }

    public static final int CORRELATION_ID_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object correlationId_ = "";
    /**
     * <pre>
     * Correlation id is a unique identifier given to each payload.
     * </pre>
     *
     * <code>string correlation_id = 2;</code>
     * @return The correlationId.
     */
    @java.lang.Override
    public java.lang.String getCorrelationId() {
      java.lang.Object ref = correlationId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        correlationId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * Correlation id is a unique identifier given to each payload.
     * </pre>
     *
     * <code>string correlation_id = 2;</code>
     * @return The bytes for correlationId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCorrelationIdBytes() {
      java.lang.Object ref = correlationId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        correlationId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MAGIC_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object magic_ = "";
    /**
     * <pre>
     * This fields tells if the data is old or new.
     * </pre>
     *
     * <code>optional string magic = 3;</code>
     * @return Whether the magic field is set.
     */
    @java.lang.Override
    public boolean hasMagic() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * This fields tells if the data is old or new.
     * </pre>
     *
     * <code>optional string magic = 3;</code>
     * @return The magic.
     */
    @java.lang.Override
    public java.lang.String getMagic() {
      java.lang.Object ref = magic_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        magic_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * This fields tells if the data is old or new.
     * </pre>
     *
     * <code>optional string magic = 3;</code>
     * @return The bytes for magic.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMagicBytes() {
      java.lang.Object ref = magic_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        magic_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SQN_FIELD_NUMBER = 4;
    private int sqn_ = 0;
    /**
     * <pre>
     * Has no use.
     * </pre>
     *
     * <code>optional sint32 sqn = 4;</code>
     * @return Whether the sqn field is set.
     */
    @java.lang.Override
    public boolean hasSqn() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * Has no use.
     * </pre>
     *
     * <code>optional sint32 sqn = 4;</code>
     * @return The sqn.
     */
    @java.lang.Override
    public int getSqn() {
      return sqn_;
    }

    public static final int CRC16_FIELD_NUMBER = 5;
    private int crc16_ = 0;
    /**
     * <pre>
     * Has no use.
     * </pre>
     *
     * <code>optional int32 crc16 = 5;</code>
     * @return Whether the crc16 field is set.
     */
    @java.lang.Override
    public boolean hasCrc16() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * Has no use.
     * </pre>
     *
     * <code>optional int32 crc16 = 5;</code>
     * @return The crc16.
     */
    @java.lang.Override
    public int getCrc16() {
      return crc16_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (imei_ != 0L) {
        output.writeUInt64(1, imei_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(correlationId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, correlationId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, magic_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeSInt32(4, sqn_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(5, crc16_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (imei_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, imei_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(correlationId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, correlationId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, magic_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(4, sqn_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, crc16_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.MetadataProto.Metadata)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.MetadataProto.Metadata other = (com.nichesolv.nds.model.proto.model.MetadataProto.Metadata) obj;

      if (getImei()
          != other.getImei()) return false;
      if (!getCorrelationId()
          .equals(other.getCorrelationId())) return false;
      if (hasMagic() != other.hasMagic()) return false;
      if (hasMagic()) {
        if (!getMagic()
            .equals(other.getMagic())) return false;
      }
      if (hasSqn() != other.hasSqn()) return false;
      if (hasSqn()) {
        if (getSqn()
            != other.getSqn()) return false;
      }
      if (hasCrc16() != other.hasCrc16()) return false;
      if (hasCrc16()) {
        if (getCrc16()
            != other.getCrc16()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + IMEI_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getImei());
      hash = (37 * hash) + CORRELATION_ID_FIELD_NUMBER;
      hash = (53 * hash) + getCorrelationId().hashCode();
      if (hasMagic()) {
        hash = (37 * hash) + MAGIC_FIELD_NUMBER;
        hash = (53 * hash) + getMagic().hashCode();
      }
      if (hasSqn()) {
        hash = (37 * hash) + SQN_FIELD_NUMBER;
        hash = (53 * hash) + getSqn();
      }
      if (hasCrc16()) {
        hash = (37 * hash) + CRC16_FIELD_NUMBER;
        hash = (53 * hash) + getCrc16();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * This class represents the metadata associated with each payload.
     * </pre>
     *
     * Protobuf type {@code Metadata}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Metadata)
        com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.MetadataProto.internal_static_Metadata_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.MetadataProto.internal_static_Metadata_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.class, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        imei_ = 0L;
        correlationId_ = "";
        magic_ = "";
        sqn_ = 0;
        crc16_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.MetadataProto.internal_static_Metadata_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata build() {
        com.nichesolv.nds.model.proto.model.MetadataProto.Metadata result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata buildPartial() {
        com.nichesolv.nds.model.proto.model.MetadataProto.Metadata result = new com.nichesolv.nds.model.proto.model.MetadataProto.Metadata(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.imei_ = imei_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.correlationId_ = correlationId_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.magic_ = magic_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.sqn_ = sqn_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.crc16_ = crc16_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.MetadataProto.Metadata) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.MetadataProto.Metadata)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata other) {
        if (other == com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance()) return this;
        if (other.getImei() != 0L) {
          setImei(other.getImei());
        }
        if (!other.getCorrelationId().isEmpty()) {
          correlationId_ = other.correlationId_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.hasMagic()) {
          magic_ = other.magic_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.hasSqn()) {
          setSqn(other.getSqn());
        }
        if (other.hasCrc16()) {
          setCrc16(other.getCrc16());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                imei_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                correlationId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                magic_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 32: {
                sqn_ = input.readSInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                crc16_ = input.readInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long imei_ ;
      /**
       * <pre>
       * Imei is the main identifier of the vehicle. This field is required.
       * </pre>
       *
       * <code>uint64 imei = 1;</code>
       * @return The imei.
       */
      @java.lang.Override
      public long getImei() {
        return imei_;
      }
      /**
       * <pre>
       * Imei is the main identifier of the vehicle. This field is required.
       * </pre>
       *
       * <code>uint64 imei = 1;</code>
       * @param value The imei to set.
       * @return This builder for chaining.
       */
      public Builder setImei(long value) {

        imei_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Imei is the main identifier of the vehicle. This field is required.
       * </pre>
       *
       * <code>uint64 imei = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearImei() {
        bitField0_ = (bitField0_ & ~0x00000001);
        imei_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object correlationId_ = "";
      /**
       * <pre>
       * Correlation id is a unique identifier given to each payload.
       * </pre>
       *
       * <code>string correlation_id = 2;</code>
       * @return The correlationId.
       */
      public java.lang.String getCorrelationId() {
        java.lang.Object ref = correlationId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          correlationId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * Correlation id is a unique identifier given to each payload.
       * </pre>
       *
       * <code>string correlation_id = 2;</code>
       * @return The bytes for correlationId.
       */
      public com.google.protobuf.ByteString
          getCorrelationIdBytes() {
        java.lang.Object ref = correlationId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          correlationId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * Correlation id is a unique identifier given to each payload.
       * </pre>
       *
       * <code>string correlation_id = 2;</code>
       * @param value The correlationId to set.
       * @return This builder for chaining.
       */
      public Builder setCorrelationId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        correlationId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Correlation id is a unique identifier given to each payload.
       * </pre>
       *
       * <code>string correlation_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCorrelationId() {
        correlationId_ = getDefaultInstance().getCorrelationId();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Correlation id is a unique identifier given to each payload.
       * </pre>
       *
       * <code>string correlation_id = 2;</code>
       * @param value The bytes for correlationId to set.
       * @return This builder for chaining.
       */
      public Builder setCorrelationIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        correlationId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object magic_ = "";
      /**
       * <pre>
       * This fields tells if the data is old or new.
       * </pre>
       *
       * <code>optional string magic = 3;</code>
       * @return Whether the magic field is set.
       */
      public boolean hasMagic() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * This fields tells if the data is old or new.
       * </pre>
       *
       * <code>optional string magic = 3;</code>
       * @return The magic.
       */
      public java.lang.String getMagic() {
        java.lang.Object ref = magic_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          magic_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * This fields tells if the data is old or new.
       * </pre>
       *
       * <code>optional string magic = 3;</code>
       * @return The bytes for magic.
       */
      public com.google.protobuf.ByteString
          getMagicBytes() {
        java.lang.Object ref = magic_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          magic_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * This fields tells if the data is old or new.
       * </pre>
       *
       * <code>optional string magic = 3;</code>
       * @param value The magic to set.
       * @return This builder for chaining.
       */
      public Builder setMagic(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        magic_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * This fields tells if the data is old or new.
       * </pre>
       *
       * <code>optional string magic = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMagic() {
        magic_ = getDefaultInstance().getMagic();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * This fields tells if the data is old or new.
       * </pre>
       *
       * <code>optional string magic = 3;</code>
       * @param value The bytes for magic to set.
       * @return This builder for chaining.
       */
      public Builder setMagicBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        magic_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private int sqn_ ;
      /**
       * <pre>
       * Has no use.
       * </pre>
       *
       * <code>optional sint32 sqn = 4;</code>
       * @return Whether the sqn field is set.
       */
      @java.lang.Override
      public boolean hasSqn() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * Has no use.
       * </pre>
       *
       * <code>optional sint32 sqn = 4;</code>
       * @return The sqn.
       */
      @java.lang.Override
      public int getSqn() {
        return sqn_;
      }
      /**
       * <pre>
       * Has no use.
       * </pre>
       *
       * <code>optional sint32 sqn = 4;</code>
       * @param value The sqn to set.
       * @return This builder for chaining.
       */
      public Builder setSqn(int value) {

        sqn_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Has no use.
       * </pre>
       *
       * <code>optional sint32 sqn = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSqn() {
        bitField0_ = (bitField0_ & ~0x00000008);
        sqn_ = 0;
        onChanged();
        return this;
      }

      private int crc16_ ;
      /**
       * <pre>
       * Has no use.
       * </pre>
       *
       * <code>optional int32 crc16 = 5;</code>
       * @return Whether the crc16 field is set.
       */
      @java.lang.Override
      public boolean hasCrc16() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * Has no use.
       * </pre>
       *
       * <code>optional int32 crc16 = 5;</code>
       * @return The crc16.
       */
      @java.lang.Override
      public int getCrc16() {
        return crc16_;
      }
      /**
       * <pre>
       * Has no use.
       * </pre>
       *
       * <code>optional int32 crc16 = 5;</code>
       * @param value The crc16 to set.
       * @return This builder for chaining.
       */
      public Builder setCrc16(int value) {

        crc16_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Has no use.
       * </pre>
       *
       * <code>optional int32 crc16 = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCrc16() {
        bitField0_ = (bitField0_ & ~0x00000010);
        crc16_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Metadata)
    }

    // @@protoc_insertion_point(class_scope:Metadata)
    private static final com.nichesolv.nds.model.proto.model.MetadataProto.Metadata DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.MetadataProto.Metadata();
    }

    public static com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Metadata>
        PARSER = new com.google.protobuf.AbstractParser<Metadata>() {
      @java.lang.Override
      public Metadata parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Metadata> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Metadata> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Metadata_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Metadata_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n,com/nichesolv/nds/model/proto/metadata" +
      ".proto\"\206\001\n\010Metadata\022\014\n\004imei\030\001 \001(\004\022\026\n\016cor" +
      "relation_id\030\002 \001(\t\022\022\n\005magic\030\003 \001(\tH\000\210\001\001\022\020\n" +
      "\003sqn\030\004 \001(\021H\001\210\001\001\022\022\n\005crc16\030\005 \001(\005H\002\210\001\001B\010\n\006_" +
      "magicB\006\n\004_sqnB\010\n\006_crc16B4\n#com.nichesolv" +
      ".nds.model.proto.modelB\rMetadataProtob\006p" +
      "roto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_Metadata_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Metadata_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Metadata_descriptor,
        new java.lang.String[] { "Imei", "CorrelationId", "Magic", "Sqn", "Crc16", "Magic", "Sqn", "Crc16", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
