// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/motor_controller.proto

// Protobuf Java Version: 3.25.0
package com.nichesolv.nds.model.proto.model;

public final class MotorControllerProto {
  private MotorControllerProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code MotorStatus}
   */
  public enum MotorStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>WORKING_FINE = 0;</code>
     */
    WORKING_FINE(0),
    /**
     * <code>MOSFET_FAULT = 1;</code>
     */
    MOSFET_FAULT(1),
    /**
     * <code>DRIVE_FAULT = 2;</code>
     */
    DRIVE_FAULT(2),
    /**
     * <code>OVERLOAD_FAULT = 4;</code>
     */
    OVERLOAD_FAULT(4),
    /**
     * <code>OVER_VOLTAGE_FAULT = 8;</code>
     */
    OVER_VOLTAGE_FAULT(8),
    /**
     * <code>UNDER_VOLTAGE_FAULT = 12;</code>
     */
    UNDER_VOLTAGE_FAULT(12),
    /**
     * <code>OVERHEAT_FAULT = 16;</code>
     */
    OVERHEAT_FAULT(16),
    /**
     * <code>OVER_CURRENT_FAULT = 10;</code>
     */
    OVER_CURRENT_FAULT(10),
    /**
     * <code>LOW_VOLTAGE_FAULT = 32;</code>
     */
    LOW_VOLTAGE_FAULT(32),
    /**
     * <code>MOTOR_LOST_HALL_FAULT = 64;</code>
     */
    MOTOR_LOST_HALL_FAULT(64),
    /**
     * <code>HALL_SENSOR_FAULT = 128;</code>
     */
    HALL_SENSOR_FAULT(128),
    /**
     * <code>MOTOR_OVERHEAT_FAULT = 256;</code>
     */
    MOTOR_OVERHEAT_FAULT(256),
    /**
     * <code>MOTOR_STUCK_FAULT = 512;</code>
     */
    MOTOR_STUCK_FAULT(512),
    /**
     * <code>THROTTLE_FAULT = 1024;</code>
     */
    THROTTLE_FAULT(1024),
    /**
     * <code>SPEED_OVERSHOOT_FAULT = 14;</code>
     */
    SPEED_OVERSHOOT_FAULT(14),
    /**
     * <code>NO_STATUS = 15;</code>
     */
    NO_STATUS(15),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>WORKING_FINE = 0;</code>
     */
    public static final int WORKING_FINE_VALUE = 0;
    /**
     * <code>MOSFET_FAULT = 1;</code>
     */
    public static final int MOSFET_FAULT_VALUE = 1;
    /**
     * <code>DRIVE_FAULT = 2;</code>
     */
    public static final int DRIVE_FAULT_VALUE = 2;
    /**
     * <code>OVERLOAD_FAULT = 4;</code>
     */
    public static final int OVERLOAD_FAULT_VALUE = 4;
    /**
     * <code>OVER_VOLTAGE_FAULT = 8;</code>
     */
    public static final int OVER_VOLTAGE_FAULT_VALUE = 8;
    /**
     * <code>UNDER_VOLTAGE_FAULT = 12;</code>
     */
    public static final int UNDER_VOLTAGE_FAULT_VALUE = 12;
    /**
     * <code>OVERHEAT_FAULT = 16;</code>
     */
    public static final int OVERHEAT_FAULT_VALUE = 16;
    /**
     * <code>OVER_CURRENT_FAULT = 10;</code>
     */
    public static final int OVER_CURRENT_FAULT_VALUE = 10;
    /**
     * <code>LOW_VOLTAGE_FAULT = 32;</code>
     */
    public static final int LOW_VOLTAGE_FAULT_VALUE = 32;
    /**
     * <code>MOTOR_LOST_HALL_FAULT = 64;</code>
     */
    public static final int MOTOR_LOST_HALL_FAULT_VALUE = 64;
    /**
     * <code>HALL_SENSOR_FAULT = 128;</code>
     */
    public static final int HALL_SENSOR_FAULT_VALUE = 128;
    /**
     * <code>MOTOR_OVERHEAT_FAULT = 256;</code>
     */
    public static final int MOTOR_OVERHEAT_FAULT_VALUE = 256;
    /**
     * <code>MOTOR_STUCK_FAULT = 512;</code>
     */
    public static final int MOTOR_STUCK_FAULT_VALUE = 512;
    /**
     * <code>THROTTLE_FAULT = 1024;</code>
     */
    public static final int THROTTLE_FAULT_VALUE = 1024;
    /**
     * <code>SPEED_OVERSHOOT_FAULT = 14;</code>
     */
    public static final int SPEED_OVERSHOOT_FAULT_VALUE = 14;
    /**
     * <code>NO_STATUS = 15;</code>
     */
    public static final int NO_STATUS_VALUE = 15;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static MotorStatus valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static MotorStatus forNumber(int value) {
      switch (value) {
        case 0: return WORKING_FINE;
        case 1: return MOSFET_FAULT;
        case 2: return DRIVE_FAULT;
        case 4: return OVERLOAD_FAULT;
        case 8: return OVER_VOLTAGE_FAULT;
        case 12: return UNDER_VOLTAGE_FAULT;
        case 16: return OVERHEAT_FAULT;
        case 10: return OVER_CURRENT_FAULT;
        case 32: return LOW_VOLTAGE_FAULT;
        case 64: return MOTOR_LOST_HALL_FAULT;
        case 128: return HALL_SENSOR_FAULT;
        case 256: return MOTOR_OVERHEAT_FAULT;
        case 512: return MOTOR_STUCK_FAULT;
        case 1024: return THROTTLE_FAULT;
        case 14: return SPEED_OVERSHOOT_FAULT;
        case 15: return NO_STATUS;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MotorStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        MotorStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MotorStatus>() {
            public MotorStatus findValueByNumber(int number) {
              return MotorStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.MotorControllerProto.getDescriptor().getEnumTypes().get(0);
    }

    private static final MotorStatus[] VALUES = values();

    public static MotorStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private MotorStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:MotorStatus)
  }

  public interface MotorControllerOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MotorController)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.MotorControllerData data = 1;</code>
     * @return Whether the data field is set.
     */
    boolean hasData();
    /**
     * <code>.MotorControllerData data = 1;</code>
     * @return The data.
     */
    com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData getData();
    /**
     * <code>.MotorControllerData data = 1;</code>
     */
    com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerDataOrBuilder getDataOrBuilder();

    /**
     * <code>.MotorControllerStatus status = 2;</code>
     * @return Whether the status field is set.
     */
    boolean hasStatus();
    /**
     * <code>.MotorControllerStatus status = 2;</code>
     * @return The status.
     */
    com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus getStatus();
    /**
     * <code>.MotorControllerStatus status = 2;</code>
     */
    com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatusOrBuilder getStatusOrBuilder();

    /**
     * <code>.Timestamp timestamp = 3;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <code>.Timestamp timestamp = 3;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <code>.Timestamp timestamp = 3;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <code>.Metadata metadata = 4;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <code>.Metadata metadata = 4;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <code>.Metadata metadata = 4;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();

    /**
     * <code>optional bool di_motion = 5;</code>
     * @return Whether the diMotion field is set.
     */
    boolean hasDiMotion();
    /**
     * <code>optional bool di_motion = 5;</code>
     * @return The diMotion.
     */
    boolean getDiMotion();

    /**
     * <code>optional bool di_ignition = 6;</code>
     * @return Whether the diIgnition field is set.
     */
    boolean hasDiIgnition();
    /**
     * <code>optional bool di_ignition = 6;</code>
     * @return The diIgnition.
     */
    boolean getDiIgnition();
  }
  /**
   * Protobuf type {@code MotorController}
   */
  public static final class MotorController extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MotorController)
      MotorControllerOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MotorController.newBuilder() to construct.
    private MotorController(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MotorController() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MotorController();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorController_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorController_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController.class, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController.Builder.class);
    }

    private int bitField0_;
    public static final int DATA_FIELD_NUMBER = 1;
    private com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData data_;
    /**
     * <code>.MotorControllerData data = 1;</code>
     * @return Whether the data field is set.
     */
    @java.lang.Override
    public boolean hasData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.MotorControllerData data = 1;</code>
     * @return The data.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData getData() {
      return data_ == null ? com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.getDefaultInstance() : data_;
    }
    /**
     * <code>.MotorControllerData data = 1;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerDataOrBuilder getDataOrBuilder() {
      return data_ == null ? com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.getDefaultInstance() : data_;
    }

    public static final int STATUS_FIELD_NUMBER = 2;
    private com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus status_;
    /**
     * <code>.MotorControllerStatus status = 2;</code>
     * @return Whether the status field is set.
     */
    @java.lang.Override
    public boolean hasStatus() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.MotorControllerStatus status = 2;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus getStatus() {
      return status_ == null ? com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.getDefaultInstance() : status_;
    }
    /**
     * <code>.MotorControllerStatus status = 2;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatusOrBuilder getStatusOrBuilder() {
      return status_ == null ? com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.getDefaultInstance() : status_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 3;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <code>.Timestamp timestamp = 3;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.Timestamp timestamp = 3;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <code>.Timestamp timestamp = 3;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }

    public static final int METADATA_FIELD_NUMBER = 4;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <code>.Metadata metadata = 4;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>.Metadata metadata = 4;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <code>.Metadata metadata = 4;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }

    public static final int DI_MOTION_FIELD_NUMBER = 5;
    private boolean diMotion_ = false;
    /**
     * <code>optional bool di_motion = 5;</code>
     * @return Whether the diMotion field is set.
     */
    @java.lang.Override
    public boolean hasDiMotion() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool di_motion = 5;</code>
     * @return The diMotion.
     */
    @java.lang.Override
    public boolean getDiMotion() {
      return diMotion_;
    }

    public static final int DI_IGNITION_FIELD_NUMBER = 6;
    private boolean diIgnition_ = false;
    /**
     * <code>optional bool di_ignition = 6;</code>
     * @return Whether the diIgnition field is set.
     */
    @java.lang.Override
    public boolean hasDiIgnition() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bool di_ignition = 6;</code>
     * @return The diIgnition.
     */
    @java.lang.Override
    public boolean getDiIgnition() {
      return diIgnition_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getStatus());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getTimestamp());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getMetadata());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBool(5, diMotion_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBool(6, diIgnition_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getStatus());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getTimestamp());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getMetadata());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, diMotion_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(6, diIgnition_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController other = (com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController) obj;

      if (hasData() != other.hasData()) return false;
      if (hasData()) {
        if (!getData()
            .equals(other.getData())) return false;
      }
      if (hasStatus() != other.hasStatus()) return false;
      if (hasStatus()) {
        if (!getStatus()
            .equals(other.getStatus())) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (hasDiMotion() != other.hasDiMotion()) return false;
      if (hasDiMotion()) {
        if (getDiMotion()
            != other.getDiMotion()) return false;
      }
      if (hasDiIgnition() != other.hasDiIgnition()) return false;
      if (hasDiIgnition()) {
        if (getDiIgnition()
            != other.getDiIgnition()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasData()) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + getData().hashCode();
      }
      if (hasStatus()) {
        hash = (37 * hash) + STATUS_FIELD_NUMBER;
        hash = (53 * hash) + getStatus().hashCode();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      if (hasDiMotion()) {
        hash = (37 * hash) + DI_MOTION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getDiMotion());
      }
      if (hasDiIgnition()) {
        hash = (37 * hash) + DI_IGNITION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getDiIgnition());
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code MotorController}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MotorController)
        com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorController_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorController_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController.class, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDataFieldBuilder();
          getStatusFieldBuilder();
          getTimestampFieldBuilder();
          getMetadataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        data_ = null;
        if (dataBuilder_ != null) {
          dataBuilder_.dispose();
          dataBuilder_ = null;
        }
        status_ = null;
        if (statusBuilder_ != null) {
          statusBuilder_.dispose();
          statusBuilder_ = null;
        }
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        diMotion_ = false;
        diIgnition_ = false;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorController_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController build() {
        com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController buildPartial() {
        com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController result = new com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.data_ = dataBuilder_ == null
              ? data_
              : dataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.status_ = statusBuilder_ == null
              ? status_
              : statusBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.timestamp_ = timestampBuilder_ == null
              ? timestamp_
              : timestampBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.metadata_ = metadataBuilder_ == null
              ? metadata_
              : metadataBuilder_.build();
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.diMotion_ = diMotion_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.diIgnition_ = diIgnition_;
          to_bitField0_ |= 0x00000020;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController other) {
        if (other == com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController.getDefaultInstance()) return this;
        if (other.hasData()) {
          mergeData(other.getData());
        }
        if (other.hasStatus()) {
          mergeStatus(other.getStatus());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        if (other.hasDiMotion()) {
          setDiMotion(other.getDiMotion());
        }
        if (other.hasDiIgnition()) {
          setDiIgnition(other.getDiIgnition());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getStatusFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getTimestampFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                input.readMessage(
                    getMetadataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 40: {
                diMotion_ = input.readBool();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                diIgnition_ = input.readBool();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData data_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.Builder, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerDataOrBuilder> dataBuilder_;
      /**
       * <code>.MotorControllerData data = 1;</code>
       * @return Whether the data field is set.
       */
      public boolean hasData() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.MotorControllerData data = 1;</code>
       * @return The data.
       */
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData getData() {
        if (dataBuilder_ == null) {
          return data_ == null ? com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.getDefaultInstance() : data_;
        } else {
          return dataBuilder_.getMessage();
        }
      }
      /**
       * <code>.MotorControllerData data = 1;</code>
       */
      public Builder setData(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          data_ = value;
        } else {
          dataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.MotorControllerData data = 1;</code>
       */
      public Builder setData(
          com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.Builder builderForValue) {
        if (dataBuilder_ == null) {
          data_ = builderForValue.build();
        } else {
          dataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.MotorControllerData data = 1;</code>
       */
      public Builder mergeData(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData value) {
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            data_ != null &&
            data_ != com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.getDefaultInstance()) {
            getDataBuilder().mergeFrom(value);
          } else {
            data_ = value;
          }
        } else {
          dataBuilder_.mergeFrom(value);
        }
        if (data_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.MotorControllerData data = 1;</code>
       */
      public Builder clearData() {
        bitField0_ = (bitField0_ & ~0x00000001);
        data_ = null;
        if (dataBuilder_ != null) {
          dataBuilder_.dispose();
          dataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.MotorControllerData data = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.Builder getDataBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.MotorControllerData data = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerDataOrBuilder getDataOrBuilder() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilder();
        } else {
          return data_ == null ?
              com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.getDefaultInstance() : data_;
        }
      }
      /**
       * <code>.MotorControllerData data = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.Builder, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerDataOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.Builder, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerDataOrBuilder>(
                  getData(),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus status_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.Builder, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatusOrBuilder> statusBuilder_;
      /**
       * <code>.MotorControllerStatus status = 2;</code>
       * @return Whether the status field is set.
       */
      public boolean hasStatus() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.MotorControllerStatus status = 2;</code>
       * @return The status.
       */
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus getStatus() {
        if (statusBuilder_ == null) {
          return status_ == null ? com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.getDefaultInstance() : status_;
        } else {
          return statusBuilder_.getMessage();
        }
      }
      /**
       * <code>.MotorControllerStatus status = 2;</code>
       */
      public Builder setStatus(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus value) {
        if (statusBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          status_ = value;
        } else {
          statusBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.MotorControllerStatus status = 2;</code>
       */
      public Builder setStatus(
          com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.Builder builderForValue) {
        if (statusBuilder_ == null) {
          status_ = builderForValue.build();
        } else {
          statusBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.MotorControllerStatus status = 2;</code>
       */
      public Builder mergeStatus(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus value) {
        if (statusBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            status_ != null &&
            status_ != com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.getDefaultInstance()) {
            getStatusBuilder().mergeFrom(value);
          } else {
            status_ = value;
          }
        } else {
          statusBuilder_.mergeFrom(value);
        }
        if (status_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.MotorControllerStatus status = 2;</code>
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x00000002);
        status_ = null;
        if (statusBuilder_ != null) {
          statusBuilder_.dispose();
          statusBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.MotorControllerStatus status = 2;</code>
       */
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.Builder getStatusBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getStatusFieldBuilder().getBuilder();
      }
      /**
       * <code>.MotorControllerStatus status = 2;</code>
       */
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatusOrBuilder getStatusOrBuilder() {
        if (statusBuilder_ != null) {
          return statusBuilder_.getMessageOrBuilder();
        } else {
          return status_ == null ?
              com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.getDefaultInstance() : status_;
        }
      }
      /**
       * <code>.MotorControllerStatus status = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.Builder, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatusOrBuilder> 
          getStatusFieldBuilder() {
        if (statusBuilder_ == null) {
          statusBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.Builder, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatusOrBuilder>(
                  getStatus(),
                  getParentForChildren(),
                  isClean());
          status_ = null;
        }
        return statusBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <code>.Timestamp timestamp = 3;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.Timestamp timestamp = 3;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <code>.Timestamp timestamp = 3;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
        } else {
          timestampBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 3;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 3;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            timestamp_ != null &&
            timestamp_ != com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance()) {
            getTimestampBuilder().mergeFrom(value);
          } else {
            timestamp_ = value;
          }
        } else {
          timestampBuilder_.mergeFrom(value);
        }
        if (timestamp_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 3;</code>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Timestamp timestamp = 3;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <code>.Timestamp timestamp = 3;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <code>.Timestamp timestamp = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <code>.Metadata metadata = 4;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>.Metadata metadata = 4;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Metadata metadata = 4;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
        } else {
          metadataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 4;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 4;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            metadata_ != null &&
            metadata_ != com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance()) {
            getMetadataBuilder().mergeFrom(value);
          } else {
            metadata_ = value;
          }
        } else {
          metadataBuilder_.mergeFrom(value);
        }
        if (metadata_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Metadata metadata = 4;</code>
       */
      public Builder clearMetadata() {
        bitField0_ = (bitField0_ & ~0x00000008);
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Metadata metadata = 4;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Metadata metadata = 4;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <code>.Metadata metadata = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }

      private boolean diMotion_ ;
      /**
       * <code>optional bool di_motion = 5;</code>
       * @return Whether the diMotion field is set.
       */
      @java.lang.Override
      public boolean hasDiMotion() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bool di_motion = 5;</code>
       * @return The diMotion.
       */
      @java.lang.Override
      public boolean getDiMotion() {
        return diMotion_;
      }
      /**
       * <code>optional bool di_motion = 5;</code>
       * @param value The diMotion to set.
       * @return This builder for chaining.
       */
      public Builder setDiMotion(boolean value) {

        diMotion_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool di_motion = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDiMotion() {
        bitField0_ = (bitField0_ & ~0x00000010);
        diMotion_ = false;
        onChanged();
        return this;
      }

      private boolean diIgnition_ ;
      /**
       * <code>optional bool di_ignition = 6;</code>
       * @return Whether the diIgnition field is set.
       */
      @java.lang.Override
      public boolean hasDiIgnition() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bool di_ignition = 6;</code>
       * @return The diIgnition.
       */
      @java.lang.Override
      public boolean getDiIgnition() {
        return diIgnition_;
      }
      /**
       * <code>optional bool di_ignition = 6;</code>
       * @param value The diIgnition to set.
       * @return This builder for chaining.
       */
      public Builder setDiIgnition(boolean value) {

        diIgnition_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool di_ignition = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearDiIgnition() {
        bitField0_ = (bitField0_ & ~0x00000020);
        diIgnition_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MotorController)
    }

    // @@protoc_insertion_point(class_scope:MotorController)
    private static final com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController();
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MotorController>
        PARSER = new com.google.protobuf.AbstractParser<MotorController>() {
      @java.lang.Override
      public MotorController parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MotorController> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MotorController> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorController getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MotorControllerDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MotorControllerData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional float dcVoltage = 1;</code>
     * @return Whether the dcVoltage field is set.
     */
    boolean hasDcVoltage();
    /**
     * <code>optional float dcVoltage = 1;</code>
     * @return The dcVoltage.
     */
    float getDcVoltage();

    /**
     * <code>optional sint32 motorSpeed = 3;</code>
     * @return Whether the motorSpeed field is set.
     */
    boolean hasMotorSpeed();
    /**
     * <code>optional sint32 motorSpeed = 3;</code>
     * @return The motorSpeed.
     */
    int getMotorSpeed();

    /**
     * <code>optional float dcCurrent = 5;</code>
     * @return Whether the dcCurrent field is set.
     */
    boolean hasDcCurrent();
    /**
     * <code>optional float dcCurrent = 5;</code>
     * @return The dcCurrent.
     */
    float getDcCurrent();

    /**
     * <code>optional float motorTemperature = 7;</code>
     * @return Whether the motorTemperature field is set.
     */
    boolean hasMotorTemperature();
    /**
     * <code>optional float motorTemperature = 7;</code>
     * @return The motorTemperature.
     */
    float getMotorTemperature();

    /**
     * <code>optional float mcsTemperature = 9;</code>
     * @return Whether the mcsTemperature field is set.
     */
    boolean hasMcsTemperature();
    /**
     * <code>optional float mcsTemperature = 9;</code>
     * @return The mcsTemperature.
     */
    float getMcsTemperature();
  }
  /**
   * <pre>
   * Motor controller data.
   * </pre>
   *
   * Protobuf type {@code MotorControllerData}
   */
  public static final class MotorControllerData extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MotorControllerData)
      MotorControllerDataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MotorControllerData.newBuilder() to construct.
    private MotorControllerData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MotorControllerData() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MotorControllerData();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorControllerData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorControllerData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.class, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.Builder.class);
    }

    private int bitField0_;
    public static final int DCVOLTAGE_FIELD_NUMBER = 1;
    private float dcVoltage_ = 0F;
    /**
     * <code>optional float dcVoltage = 1;</code>
     * @return Whether the dcVoltage field is set.
     */
    @java.lang.Override
    public boolean hasDcVoltage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional float dcVoltage = 1;</code>
     * @return The dcVoltage.
     */
    @java.lang.Override
    public float getDcVoltage() {
      return dcVoltage_;
    }

    public static final int MOTORSPEED_FIELD_NUMBER = 3;
    private int motorSpeed_ = 0;
    /**
     * <code>optional sint32 motorSpeed = 3;</code>
     * @return Whether the motorSpeed field is set.
     */
    @java.lang.Override
    public boolean hasMotorSpeed() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional sint32 motorSpeed = 3;</code>
     * @return The motorSpeed.
     */
    @java.lang.Override
    public int getMotorSpeed() {
      return motorSpeed_;
    }

    public static final int DCCURRENT_FIELD_NUMBER = 5;
    private float dcCurrent_ = 0F;
    /**
     * <code>optional float dcCurrent = 5;</code>
     * @return Whether the dcCurrent field is set.
     */
    @java.lang.Override
    public boolean hasDcCurrent() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional float dcCurrent = 5;</code>
     * @return The dcCurrent.
     */
    @java.lang.Override
    public float getDcCurrent() {
      return dcCurrent_;
    }

    public static final int MOTORTEMPERATURE_FIELD_NUMBER = 7;
    private float motorTemperature_ = 0F;
    /**
     * <code>optional float motorTemperature = 7;</code>
     * @return Whether the motorTemperature field is set.
     */
    @java.lang.Override
    public boolean hasMotorTemperature() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional float motorTemperature = 7;</code>
     * @return The motorTemperature.
     */
    @java.lang.Override
    public float getMotorTemperature() {
      return motorTemperature_;
    }

    public static final int MCSTEMPERATURE_FIELD_NUMBER = 9;
    private float mcsTemperature_ = 0F;
    /**
     * <code>optional float mcsTemperature = 9;</code>
     * @return Whether the mcsTemperature field is set.
     */
    @java.lang.Override
    public boolean hasMcsTemperature() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional float mcsTemperature = 9;</code>
     * @return The mcsTemperature.
     */
    @java.lang.Override
    public float getMcsTemperature() {
      return mcsTemperature_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeFloat(1, dcVoltage_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeSInt32(3, motorSpeed_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeFloat(5, dcCurrent_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeFloat(7, motorTemperature_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeFloat(9, mcsTemperature_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(1, dcVoltage_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(3, motorSpeed_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(5, dcCurrent_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(7, motorTemperature_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(9, mcsTemperature_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData other = (com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData) obj;

      if (hasDcVoltage() != other.hasDcVoltage()) return false;
      if (hasDcVoltage()) {
        if (java.lang.Float.floatToIntBits(getDcVoltage())
            != java.lang.Float.floatToIntBits(
                other.getDcVoltage())) return false;
      }
      if (hasMotorSpeed() != other.hasMotorSpeed()) return false;
      if (hasMotorSpeed()) {
        if (getMotorSpeed()
            != other.getMotorSpeed()) return false;
      }
      if (hasDcCurrent() != other.hasDcCurrent()) return false;
      if (hasDcCurrent()) {
        if (java.lang.Float.floatToIntBits(getDcCurrent())
            != java.lang.Float.floatToIntBits(
                other.getDcCurrent())) return false;
      }
      if (hasMotorTemperature() != other.hasMotorTemperature()) return false;
      if (hasMotorTemperature()) {
        if (java.lang.Float.floatToIntBits(getMotorTemperature())
            != java.lang.Float.floatToIntBits(
                other.getMotorTemperature())) return false;
      }
      if (hasMcsTemperature() != other.hasMcsTemperature()) return false;
      if (hasMcsTemperature()) {
        if (java.lang.Float.floatToIntBits(getMcsTemperature())
            != java.lang.Float.floatToIntBits(
                other.getMcsTemperature())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDcVoltage()) {
        hash = (37 * hash) + DCVOLTAGE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getDcVoltage());
      }
      if (hasMotorSpeed()) {
        hash = (37 * hash) + MOTORSPEED_FIELD_NUMBER;
        hash = (53 * hash) + getMotorSpeed();
      }
      if (hasDcCurrent()) {
        hash = (37 * hash) + DCCURRENT_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getDcCurrent());
      }
      if (hasMotorTemperature()) {
        hash = (37 * hash) + MOTORTEMPERATURE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getMotorTemperature());
      }
      if (hasMcsTemperature()) {
        hash = (37 * hash) + MCSTEMPERATURE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getMcsTemperature());
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Motor controller data.
     * </pre>
     *
     * Protobuf type {@code MotorControllerData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MotorControllerData)
        com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorControllerData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorControllerData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.class, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        dcVoltage_ = 0F;
        motorSpeed_ = 0;
        dcCurrent_ = 0F;
        motorTemperature_ = 0F;
        mcsTemperature_ = 0F;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorControllerData_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData build() {
        com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData buildPartial() {
        com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData result = new com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.dcVoltage_ = dcVoltage_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.motorSpeed_ = motorSpeed_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.dcCurrent_ = dcCurrent_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.motorTemperature_ = motorTemperature_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.mcsTemperature_ = mcsTemperature_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData other) {
        if (other == com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData.getDefaultInstance()) return this;
        if (other.hasDcVoltage()) {
          setDcVoltage(other.getDcVoltage());
        }
        if (other.hasMotorSpeed()) {
          setMotorSpeed(other.getMotorSpeed());
        }
        if (other.hasDcCurrent()) {
          setDcCurrent(other.getDcCurrent());
        }
        if (other.hasMotorTemperature()) {
          setMotorTemperature(other.getMotorTemperature());
        }
        if (other.hasMcsTemperature()) {
          setMcsTemperature(other.getMcsTemperature());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 13: {
                dcVoltage_ = input.readFloat();
                bitField0_ |= 0x00000001;
                break;
              } // case 13
              case 24: {
                motorSpeed_ = input.readSInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 24
              case 45: {
                dcCurrent_ = input.readFloat();
                bitField0_ |= 0x00000004;
                break;
              } // case 45
              case 61: {
                motorTemperature_ = input.readFloat();
                bitField0_ |= 0x00000008;
                break;
              } // case 61
              case 77: {
                mcsTemperature_ = input.readFloat();
                bitField0_ |= 0x00000010;
                break;
              } // case 77
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private float dcVoltage_ ;
      /**
       * <code>optional float dcVoltage = 1;</code>
       * @return Whether the dcVoltage field is set.
       */
      @java.lang.Override
      public boolean hasDcVoltage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional float dcVoltage = 1;</code>
       * @return The dcVoltage.
       */
      @java.lang.Override
      public float getDcVoltage() {
        return dcVoltage_;
      }
      /**
       * <code>optional float dcVoltage = 1;</code>
       * @param value The dcVoltage to set.
       * @return This builder for chaining.
       */
      public Builder setDcVoltage(float value) {

        dcVoltage_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional float dcVoltage = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDcVoltage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        dcVoltage_ = 0F;
        onChanged();
        return this;
      }

      private int motorSpeed_ ;
      /**
       * <code>optional sint32 motorSpeed = 3;</code>
       * @return Whether the motorSpeed field is set.
       */
      @java.lang.Override
      public boolean hasMotorSpeed() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional sint32 motorSpeed = 3;</code>
       * @return The motorSpeed.
       */
      @java.lang.Override
      public int getMotorSpeed() {
        return motorSpeed_;
      }
      /**
       * <code>optional sint32 motorSpeed = 3;</code>
       * @param value The motorSpeed to set.
       * @return This builder for chaining.
       */
      public Builder setMotorSpeed(int value) {

        motorSpeed_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 motorSpeed = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMotorSpeed() {
        bitField0_ = (bitField0_ & ~0x00000002);
        motorSpeed_ = 0;
        onChanged();
        return this;
      }

      private float dcCurrent_ ;
      /**
       * <code>optional float dcCurrent = 5;</code>
       * @return Whether the dcCurrent field is set.
       */
      @java.lang.Override
      public boolean hasDcCurrent() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional float dcCurrent = 5;</code>
       * @return The dcCurrent.
       */
      @java.lang.Override
      public float getDcCurrent() {
        return dcCurrent_;
      }
      /**
       * <code>optional float dcCurrent = 5;</code>
       * @param value The dcCurrent to set.
       * @return This builder for chaining.
       */
      public Builder setDcCurrent(float value) {

        dcCurrent_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional float dcCurrent = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDcCurrent() {
        bitField0_ = (bitField0_ & ~0x00000004);
        dcCurrent_ = 0F;
        onChanged();
        return this;
      }

      private float motorTemperature_ ;
      /**
       * <code>optional float motorTemperature = 7;</code>
       * @return Whether the motorTemperature field is set.
       */
      @java.lang.Override
      public boolean hasMotorTemperature() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional float motorTemperature = 7;</code>
       * @return The motorTemperature.
       */
      @java.lang.Override
      public float getMotorTemperature() {
        return motorTemperature_;
      }
      /**
       * <code>optional float motorTemperature = 7;</code>
       * @param value The motorTemperature to set.
       * @return This builder for chaining.
       */
      public Builder setMotorTemperature(float value) {

        motorTemperature_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional float motorTemperature = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearMotorTemperature() {
        bitField0_ = (bitField0_ & ~0x00000008);
        motorTemperature_ = 0F;
        onChanged();
        return this;
      }

      private float mcsTemperature_ ;
      /**
       * <code>optional float mcsTemperature = 9;</code>
       * @return Whether the mcsTemperature field is set.
       */
      @java.lang.Override
      public boolean hasMcsTemperature() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional float mcsTemperature = 9;</code>
       * @return The mcsTemperature.
       */
      @java.lang.Override
      public float getMcsTemperature() {
        return mcsTemperature_;
      }
      /**
       * <code>optional float mcsTemperature = 9;</code>
       * @param value The mcsTemperature to set.
       * @return This builder for chaining.
       */
      public Builder setMcsTemperature(float value) {

        mcsTemperature_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional float mcsTemperature = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearMcsTemperature() {
        bitField0_ = (bitField0_ & ~0x00000010);
        mcsTemperature_ = 0F;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MotorControllerData)
    }

    // @@protoc_insertion_point(class_scope:MotorControllerData)
    private static final com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData();
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MotorControllerData>
        PARSER = new com.google.protobuf.AbstractParser<MotorControllerData>() {
      @java.lang.Override
      public MotorControllerData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MotorControllerData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MotorControllerData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MotorControllerStatusOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MotorControllerStatus)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string driveSelection = 1;</code>
     * @return Whether the driveSelection field is set.
     */
    boolean hasDriveSelection();
    /**
     * <code>optional string driveSelection = 1;</code>
     * @return The driveSelection.
     */
    java.lang.String getDriveSelection();
    /**
     * <code>optional string driveSelection = 1;</code>
     * @return The bytes for driveSelection.
     */
    com.google.protobuf.ByteString
        getDriveSelectionBytes();

    /**
     * <code>optional bool regeneration = 2;</code>
     * @return Whether the regeneration field is set.
     */
    boolean hasRegeneration();
    /**
     * <code>optional bool regeneration = 2;</code>
     * @return The regeneration.
     */
    boolean getRegeneration();

    /**
     * <code>optional bool readySign = 3;</code>
     * @return Whether the readySign field is set.
     */
    boolean hasReadySign();
    /**
     * <code>optional bool readySign = 3;</code>
     * @return The readySign.
     */
    boolean getReadySign();

    /**
     * <code>optional bool pLight = 4;</code>
     * @return Whether the pLight field is set.
     */
    boolean hasPLight();
    /**
     * <code>optional bool pLight = 4;</code>
     * @return The pLight.
     */
    boolean getPLight();

    /**
     * <code>optional bool reverse = 5;</code>
     * @return Whether the reverse field is set.
     */
    boolean hasReverse();
    /**
     * <code>optional bool reverse = 5;</code>
     * @return The reverse.
     */
    boolean getReverse();

    /**
     * <code>optional bool cruise = 6;</code>
     * @return Whether the cruise field is set.
     */
    boolean hasCruise();
    /**
     * <code>optional bool cruise = 6;</code>
     * @return The cruise.
     */
    boolean getCruise();

    /**
     * <code>optional bool vehicleBrake = 7;</code>
     * @return Whether the vehicleBrake field is set.
     */
    boolean hasVehicleBrake();
    /**
     * <code>optional bool vehicleBrake = 7;</code>
     * @return The vehicleBrake.
     */
    boolean getVehicleBrake();

    /**
     * <code>optional bool sideStand = 8;</code>
     * @return Whether the sideStand field is set.
     */
    boolean hasSideStand();
    /**
     * <code>optional bool sideStand = 8;</code>
     * @return The sideStand.
     */
    boolean getSideStand();

    /**
     * <code>optional sint32 throttlePercentage = 9;</code>
     * @return Whether the throttlePercentage field is set.
     */
    boolean hasThrottlePercentage();
    /**
     * <code>optional sint32 throttlePercentage = 9;</code>
     * @return The throttlePercentage.
     */
    int getThrottlePercentage();

    /**
     * <code>repeated .MotorStatus motor_status = 10;</code>
     * @return A list containing the motorStatus.
     */
    java.util.List<com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus> getMotorStatusList();
    /**
     * <code>repeated .MotorStatus motor_status = 10;</code>
     * @return The count of motorStatus.
     */
    int getMotorStatusCount();
    /**
     * <code>repeated .MotorStatus motor_status = 10;</code>
     * @param index The index of the element to return.
     * @return The motorStatus at the given index.
     */
    com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus getMotorStatus(int index);
    /**
     * <code>repeated .MotorStatus motor_status = 10;</code>
     * @return A list containing the enum numeric values on the wire for motorStatus.
     */
    java.util.List<java.lang.Integer>
    getMotorStatusValueList();
    /**
     * <code>repeated .MotorStatus motor_status = 10;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of motorStatus at the given index.
     */
    int getMotorStatusValue(int index);
  }
  /**
   * <pre>
   * Motor controller status.
   * </pre>
   *
   * Protobuf type {@code MotorControllerStatus}
   */
  public static final class MotorControllerStatus extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MotorControllerStatus)
      MotorControllerStatusOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MotorControllerStatus.newBuilder() to construct.
    private MotorControllerStatus(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MotorControllerStatus() {
      driveSelection_ = "";
      motorStatus_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MotorControllerStatus();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorControllerStatus_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorControllerStatus_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.class, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.Builder.class);
    }

    private int bitField0_;
    public static final int DRIVESELECTION_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object driveSelection_ = "";
    /**
     * <code>optional string driveSelection = 1;</code>
     * @return Whether the driveSelection field is set.
     */
    @java.lang.Override
    public boolean hasDriveSelection() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string driveSelection = 1;</code>
     * @return The driveSelection.
     */
    @java.lang.Override
    public java.lang.String getDriveSelection() {
      java.lang.Object ref = driveSelection_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        driveSelection_ = s;
        return s;
      }
    }
    /**
     * <code>optional string driveSelection = 1;</code>
     * @return The bytes for driveSelection.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDriveSelectionBytes() {
      java.lang.Object ref = driveSelection_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        driveSelection_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int REGENERATION_FIELD_NUMBER = 2;
    private boolean regeneration_ = false;
    /**
     * <code>optional bool regeneration = 2;</code>
     * @return Whether the regeneration field is set.
     */
    @java.lang.Override
    public boolean hasRegeneration() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool regeneration = 2;</code>
     * @return The regeneration.
     */
    @java.lang.Override
    public boolean getRegeneration() {
      return regeneration_;
    }

    public static final int READYSIGN_FIELD_NUMBER = 3;
    private boolean readySign_ = false;
    /**
     * <code>optional bool readySign = 3;</code>
     * @return Whether the readySign field is set.
     */
    @java.lang.Override
    public boolean hasReadySign() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool readySign = 3;</code>
     * @return The readySign.
     */
    @java.lang.Override
    public boolean getReadySign() {
      return readySign_;
    }

    public static final int PLIGHT_FIELD_NUMBER = 4;
    private boolean pLight_ = false;
    /**
     * <code>optional bool pLight = 4;</code>
     * @return Whether the pLight field is set.
     */
    @java.lang.Override
    public boolean hasPLight() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bool pLight = 4;</code>
     * @return The pLight.
     */
    @java.lang.Override
    public boolean getPLight() {
      return pLight_;
    }

    public static final int REVERSE_FIELD_NUMBER = 5;
    private boolean reverse_ = false;
    /**
     * <code>optional bool reverse = 5;</code>
     * @return Whether the reverse field is set.
     */
    @java.lang.Override
    public boolean hasReverse() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool reverse = 5;</code>
     * @return The reverse.
     */
    @java.lang.Override
    public boolean getReverse() {
      return reverse_;
    }

    public static final int CRUISE_FIELD_NUMBER = 6;
    private boolean cruise_ = false;
    /**
     * <code>optional bool cruise = 6;</code>
     * @return Whether the cruise field is set.
     */
    @java.lang.Override
    public boolean hasCruise() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bool cruise = 6;</code>
     * @return The cruise.
     */
    @java.lang.Override
    public boolean getCruise() {
      return cruise_;
    }

    public static final int VEHICLEBRAKE_FIELD_NUMBER = 7;
    private boolean vehicleBrake_ = false;
    /**
     * <code>optional bool vehicleBrake = 7;</code>
     * @return Whether the vehicleBrake field is set.
     */
    @java.lang.Override
    public boolean hasVehicleBrake() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bool vehicleBrake = 7;</code>
     * @return The vehicleBrake.
     */
    @java.lang.Override
    public boolean getVehicleBrake() {
      return vehicleBrake_;
    }

    public static final int SIDESTAND_FIELD_NUMBER = 8;
    private boolean sideStand_ = false;
    /**
     * <code>optional bool sideStand = 8;</code>
     * @return Whether the sideStand field is set.
     */
    @java.lang.Override
    public boolean hasSideStand() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bool sideStand = 8;</code>
     * @return The sideStand.
     */
    @java.lang.Override
    public boolean getSideStand() {
      return sideStand_;
    }

    public static final int THROTTLEPERCENTAGE_FIELD_NUMBER = 9;
    private int throttlePercentage_ = 0;
    /**
     * <code>optional sint32 throttlePercentage = 9;</code>
     * @return Whether the throttlePercentage field is set.
     */
    @java.lang.Override
    public boolean hasThrottlePercentage() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional sint32 throttlePercentage = 9;</code>
     * @return The throttlePercentage.
     */
    @java.lang.Override
    public int getThrottlePercentage() {
      return throttlePercentage_;
    }

    public static final int MOTOR_STATUS_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private java.util.List<java.lang.Integer> motorStatus_;
    private static final com.google.protobuf.Internal.ListAdapter.Converter<
        java.lang.Integer, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus> motorStatus_converter_ =
            new com.google.protobuf.Internal.ListAdapter.Converter<
                java.lang.Integer, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus>() {
              public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus convert(java.lang.Integer from) {
                com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus result = com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus.forNumber(from);
                return result == null ? com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus.UNRECOGNIZED : result;
              }
            };
    /**
     * <code>repeated .MotorStatus motor_status = 10;</code>
     * @return A list containing the motorStatus.
     */
    @java.lang.Override
    public java.util.List<com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus> getMotorStatusList() {
      return new com.google.protobuf.Internal.ListAdapter<
          java.lang.Integer, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus>(motorStatus_, motorStatus_converter_);
    }
    /**
     * <code>repeated .MotorStatus motor_status = 10;</code>
     * @return The count of motorStatus.
     */
    @java.lang.Override
    public int getMotorStatusCount() {
      return motorStatus_.size();
    }
    /**
     * <code>repeated .MotorStatus motor_status = 10;</code>
     * @param index The index of the element to return.
     * @return The motorStatus at the given index.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus getMotorStatus(int index) {
      return motorStatus_converter_.convert(motorStatus_.get(index));
    }
    /**
     * <code>repeated .MotorStatus motor_status = 10;</code>
     * @return A list containing the enum numeric values on the wire for motorStatus.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
    getMotorStatusValueList() {
      return motorStatus_;
    }
    /**
     * <code>repeated .MotorStatus motor_status = 10;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of motorStatus at the given index.
     */
    @java.lang.Override
    public int getMotorStatusValue(int index) {
      return motorStatus_.get(index);
    }
    private int motorStatusMemoizedSerializedSize;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, driveSelection_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, regeneration_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBool(3, readySign_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBool(4, pLight_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBool(5, reverse_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBool(6, cruise_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeBool(7, vehicleBrake_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeBool(8, sideStand_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeSInt32(9, throttlePercentage_);
      }
      if (getMotorStatusList().size() > 0) {
        output.writeUInt32NoTag(82);
        output.writeUInt32NoTag(motorStatusMemoizedSerializedSize);
      }
      for (int i = 0; i < motorStatus_.size(); i++) {
        output.writeEnumNoTag(motorStatus_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, driveSelection_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, regeneration_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, readySign_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, pLight_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, reverse_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(6, cruise_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(7, vehicleBrake_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(8, sideStand_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(9, throttlePercentage_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < motorStatus_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeEnumSizeNoTag(motorStatus_.get(i));
        }
        size += dataSize;
        if (!getMotorStatusList().isEmpty()) {  size += 1;
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(dataSize);
        }motorStatusMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus other = (com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus) obj;

      if (hasDriveSelection() != other.hasDriveSelection()) return false;
      if (hasDriveSelection()) {
        if (!getDriveSelection()
            .equals(other.getDriveSelection())) return false;
      }
      if (hasRegeneration() != other.hasRegeneration()) return false;
      if (hasRegeneration()) {
        if (getRegeneration()
            != other.getRegeneration()) return false;
      }
      if (hasReadySign() != other.hasReadySign()) return false;
      if (hasReadySign()) {
        if (getReadySign()
            != other.getReadySign()) return false;
      }
      if (hasPLight() != other.hasPLight()) return false;
      if (hasPLight()) {
        if (getPLight()
            != other.getPLight()) return false;
      }
      if (hasReverse() != other.hasReverse()) return false;
      if (hasReverse()) {
        if (getReverse()
            != other.getReverse()) return false;
      }
      if (hasCruise() != other.hasCruise()) return false;
      if (hasCruise()) {
        if (getCruise()
            != other.getCruise()) return false;
      }
      if (hasVehicleBrake() != other.hasVehicleBrake()) return false;
      if (hasVehicleBrake()) {
        if (getVehicleBrake()
            != other.getVehicleBrake()) return false;
      }
      if (hasSideStand() != other.hasSideStand()) return false;
      if (hasSideStand()) {
        if (getSideStand()
            != other.getSideStand()) return false;
      }
      if (hasThrottlePercentage() != other.hasThrottlePercentage()) return false;
      if (hasThrottlePercentage()) {
        if (getThrottlePercentage()
            != other.getThrottlePercentage()) return false;
      }
      if (!motorStatus_.equals(other.motorStatus_)) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDriveSelection()) {
        hash = (37 * hash) + DRIVESELECTION_FIELD_NUMBER;
        hash = (53 * hash) + getDriveSelection().hashCode();
      }
      if (hasRegeneration()) {
        hash = (37 * hash) + REGENERATION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getRegeneration());
      }
      if (hasReadySign()) {
        hash = (37 * hash) + READYSIGN_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getReadySign());
      }
      if (hasPLight()) {
        hash = (37 * hash) + PLIGHT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getPLight());
      }
      if (hasReverse()) {
        hash = (37 * hash) + REVERSE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getReverse());
      }
      if (hasCruise()) {
        hash = (37 * hash) + CRUISE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getCruise());
      }
      if (hasVehicleBrake()) {
        hash = (37 * hash) + VEHICLEBRAKE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getVehicleBrake());
      }
      if (hasSideStand()) {
        hash = (37 * hash) + SIDESTAND_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getSideStand());
      }
      if (hasThrottlePercentage()) {
        hash = (37 * hash) + THROTTLEPERCENTAGE_FIELD_NUMBER;
        hash = (53 * hash) + getThrottlePercentage();
      }
      if (getMotorStatusCount() > 0) {
        hash = (37 * hash) + MOTOR_STATUS_FIELD_NUMBER;
        hash = (53 * hash) + motorStatus_.hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Motor controller status.
     * </pre>
     *
     * Protobuf type {@code MotorControllerStatus}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MotorControllerStatus)
        com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatusOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorControllerStatus_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorControllerStatus_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.class, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        driveSelection_ = "";
        regeneration_ = false;
        readySign_ = false;
        pLight_ = false;
        reverse_ = false;
        cruise_ = false;
        vehicleBrake_ = false;
        sideStand_ = false;
        throttlePercentage_ = 0;
        motorStatus_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.internal_static_MotorControllerStatus_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus build() {
        com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus buildPartial() {
        com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus result = new com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus result) {
        if (((bitField0_ & 0x00000200) != 0)) {
          motorStatus_ = java.util.Collections.unmodifiableList(motorStatus_);
          bitField0_ = (bitField0_ & ~0x00000200);
        }
        result.motorStatus_ = motorStatus_;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.driveSelection_ = driveSelection_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.regeneration_ = regeneration_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.readySign_ = readySign_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.pLight_ = pLight_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.reverse_ = reverse_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.cruise_ = cruise_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.vehicleBrake_ = vehicleBrake_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.sideStand_ = sideStand_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.throttlePercentage_ = throttlePercentage_;
          to_bitField0_ |= 0x00000100;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus other) {
        if (other == com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus.getDefaultInstance()) return this;
        if (other.hasDriveSelection()) {
          driveSelection_ = other.driveSelection_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasRegeneration()) {
          setRegeneration(other.getRegeneration());
        }
        if (other.hasReadySign()) {
          setReadySign(other.getReadySign());
        }
        if (other.hasPLight()) {
          setPLight(other.getPLight());
        }
        if (other.hasReverse()) {
          setReverse(other.getReverse());
        }
        if (other.hasCruise()) {
          setCruise(other.getCruise());
        }
        if (other.hasVehicleBrake()) {
          setVehicleBrake(other.getVehicleBrake());
        }
        if (other.hasSideStand()) {
          setSideStand(other.getSideStand());
        }
        if (other.hasThrottlePercentage()) {
          setThrottlePercentage(other.getThrottlePercentage());
        }
        if (!other.motorStatus_.isEmpty()) {
          if (motorStatus_.isEmpty()) {
            motorStatus_ = other.motorStatus_;
            bitField0_ = (bitField0_ & ~0x00000200);
          } else {
            ensureMotorStatusIsMutable();
            motorStatus_.addAll(other.motorStatus_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                driveSelection_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                regeneration_ = input.readBool();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                readySign_ = input.readBool();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                pLight_ = input.readBool();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                reverse_ = input.readBool();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                cruise_ = input.readBool();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                vehicleBrake_ = input.readBool();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                sideStand_ = input.readBool();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 72: {
                throttlePercentage_ = input.readSInt32();
                bitField0_ |= 0x00000100;
                break;
              } // case 72
              case 80: {
                int tmpRaw = input.readEnum();
                ensureMotorStatusIsMutable();
                motorStatus_.add(tmpRaw);
                break;
              } // case 80
              case 82: {
                int length = input.readRawVarint32();
                int oldLimit = input.pushLimit(length);
                while(input.getBytesUntilLimit() > 0) {
                  int tmpRaw = input.readEnum();
                  ensureMotorStatusIsMutable();
                  motorStatus_.add(tmpRaw);
                }
                input.popLimit(oldLimit);
                break;
              } // case 82
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object driveSelection_ = "";
      /**
       * <code>optional string driveSelection = 1;</code>
       * @return Whether the driveSelection field is set.
       */
      public boolean hasDriveSelection() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string driveSelection = 1;</code>
       * @return The driveSelection.
       */
      public java.lang.String getDriveSelection() {
        java.lang.Object ref = driveSelection_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          driveSelection_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string driveSelection = 1;</code>
       * @return The bytes for driveSelection.
       */
      public com.google.protobuf.ByteString
          getDriveSelectionBytes() {
        java.lang.Object ref = driveSelection_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          driveSelection_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string driveSelection = 1;</code>
       * @param value The driveSelection to set.
       * @return This builder for chaining.
       */
      public Builder setDriveSelection(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        driveSelection_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional string driveSelection = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDriveSelection() {
        driveSelection_ = getDefaultInstance().getDriveSelection();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>optional string driveSelection = 1;</code>
       * @param value The bytes for driveSelection to set.
       * @return This builder for chaining.
       */
      public Builder setDriveSelectionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        driveSelection_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private boolean regeneration_ ;
      /**
       * <code>optional bool regeneration = 2;</code>
       * @return Whether the regeneration field is set.
       */
      @java.lang.Override
      public boolean hasRegeneration() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bool regeneration = 2;</code>
       * @return The regeneration.
       */
      @java.lang.Override
      public boolean getRegeneration() {
        return regeneration_;
      }
      /**
       * <code>optional bool regeneration = 2;</code>
       * @param value The regeneration to set.
       * @return This builder for chaining.
       */
      public Builder setRegeneration(boolean value) {

        regeneration_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool regeneration = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRegeneration() {
        bitField0_ = (bitField0_ & ~0x00000002);
        regeneration_ = false;
        onChanged();
        return this;
      }

      private boolean readySign_ ;
      /**
       * <code>optional bool readySign = 3;</code>
       * @return Whether the readySign field is set.
       */
      @java.lang.Override
      public boolean hasReadySign() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool readySign = 3;</code>
       * @return The readySign.
       */
      @java.lang.Override
      public boolean getReadySign() {
        return readySign_;
      }
      /**
       * <code>optional bool readySign = 3;</code>
       * @param value The readySign to set.
       * @return This builder for chaining.
       */
      public Builder setReadySign(boolean value) {

        readySign_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool readySign = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearReadySign() {
        bitField0_ = (bitField0_ & ~0x00000004);
        readySign_ = false;
        onChanged();
        return this;
      }

      private boolean pLight_ ;
      /**
       * <code>optional bool pLight = 4;</code>
       * @return Whether the pLight field is set.
       */
      @java.lang.Override
      public boolean hasPLight() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bool pLight = 4;</code>
       * @return The pLight.
       */
      @java.lang.Override
      public boolean getPLight() {
        return pLight_;
      }
      /**
       * <code>optional bool pLight = 4;</code>
       * @param value The pLight to set.
       * @return This builder for chaining.
       */
      public Builder setPLight(boolean value) {

        pLight_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool pLight = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPLight() {
        bitField0_ = (bitField0_ & ~0x00000008);
        pLight_ = false;
        onChanged();
        return this;
      }

      private boolean reverse_ ;
      /**
       * <code>optional bool reverse = 5;</code>
       * @return Whether the reverse field is set.
       */
      @java.lang.Override
      public boolean hasReverse() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bool reverse = 5;</code>
       * @return The reverse.
       */
      @java.lang.Override
      public boolean getReverse() {
        return reverse_;
      }
      /**
       * <code>optional bool reverse = 5;</code>
       * @param value The reverse to set.
       * @return This builder for chaining.
       */
      public Builder setReverse(boolean value) {

        reverse_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool reverse = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearReverse() {
        bitField0_ = (bitField0_ & ~0x00000010);
        reverse_ = false;
        onChanged();
        return this;
      }

      private boolean cruise_ ;
      /**
       * <code>optional bool cruise = 6;</code>
       * @return Whether the cruise field is set.
       */
      @java.lang.Override
      public boolean hasCruise() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bool cruise = 6;</code>
       * @return The cruise.
       */
      @java.lang.Override
      public boolean getCruise() {
        return cruise_;
      }
      /**
       * <code>optional bool cruise = 6;</code>
       * @param value The cruise to set.
       * @return This builder for chaining.
       */
      public Builder setCruise(boolean value) {

        cruise_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool cruise = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCruise() {
        bitField0_ = (bitField0_ & ~0x00000020);
        cruise_ = false;
        onChanged();
        return this;
      }

      private boolean vehicleBrake_ ;
      /**
       * <code>optional bool vehicleBrake = 7;</code>
       * @return Whether the vehicleBrake field is set.
       */
      @java.lang.Override
      public boolean hasVehicleBrake() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional bool vehicleBrake = 7;</code>
       * @return The vehicleBrake.
       */
      @java.lang.Override
      public boolean getVehicleBrake() {
        return vehicleBrake_;
      }
      /**
       * <code>optional bool vehicleBrake = 7;</code>
       * @param value The vehicleBrake to set.
       * @return This builder for chaining.
       */
      public Builder setVehicleBrake(boolean value) {

        vehicleBrake_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool vehicleBrake = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearVehicleBrake() {
        bitField0_ = (bitField0_ & ~0x00000040);
        vehicleBrake_ = false;
        onChanged();
        return this;
      }

      private boolean sideStand_ ;
      /**
       * <code>optional bool sideStand = 8;</code>
       * @return Whether the sideStand field is set.
       */
      @java.lang.Override
      public boolean hasSideStand() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bool sideStand = 8;</code>
       * @return The sideStand.
       */
      @java.lang.Override
      public boolean getSideStand() {
        return sideStand_;
      }
      /**
       * <code>optional bool sideStand = 8;</code>
       * @param value The sideStand to set.
       * @return This builder for chaining.
       */
      public Builder setSideStand(boolean value) {

        sideStand_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool sideStand = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearSideStand() {
        bitField0_ = (bitField0_ & ~0x00000080);
        sideStand_ = false;
        onChanged();
        return this;
      }

      private int throttlePercentage_ ;
      /**
       * <code>optional sint32 throttlePercentage = 9;</code>
       * @return Whether the throttlePercentage field is set.
       */
      @java.lang.Override
      public boolean hasThrottlePercentage() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional sint32 throttlePercentage = 9;</code>
       * @return The throttlePercentage.
       */
      @java.lang.Override
      public int getThrottlePercentage() {
        return throttlePercentage_;
      }
      /**
       * <code>optional sint32 throttlePercentage = 9;</code>
       * @param value The throttlePercentage to set.
       * @return This builder for chaining.
       */
      public Builder setThrottlePercentage(int value) {

        throttlePercentage_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional sint32 throttlePercentage = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearThrottlePercentage() {
        bitField0_ = (bitField0_ & ~0x00000100);
        throttlePercentage_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> motorStatus_ =
        java.util.Collections.emptyList();
      private void ensureMotorStatusIsMutable() {
        if (!((bitField0_ & 0x00000200) != 0)) {
          motorStatus_ = new java.util.ArrayList<java.lang.Integer>(motorStatus_);
          bitField0_ |= 0x00000200;
        }
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @return A list containing the motorStatus.
       */
      public java.util.List<com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus> getMotorStatusList() {
        return new com.google.protobuf.Internal.ListAdapter<
            java.lang.Integer, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus>(motorStatus_, motorStatus_converter_);
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @return The count of motorStatus.
       */
      public int getMotorStatusCount() {
        return motorStatus_.size();
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @param index The index of the element to return.
       * @return The motorStatus at the given index.
       */
      public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus getMotorStatus(int index) {
        return motorStatus_converter_.convert(motorStatus_.get(index));
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @param index The index to set the value at.
       * @param value The motorStatus to set.
       * @return This builder for chaining.
       */
      public Builder setMotorStatus(
          int index, com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMotorStatusIsMutable();
        motorStatus_.set(index, value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @param value The motorStatus to add.
       * @return This builder for chaining.
       */
      public Builder addMotorStatus(com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMotorStatusIsMutable();
        motorStatus_.add(value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @param values The motorStatus to add.
       * @return This builder for chaining.
       */
      public Builder addAllMotorStatus(
          java.lang.Iterable<? extends com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus> values) {
        ensureMotorStatusIsMutable();
        for (com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorStatus value : values) {
          motorStatus_.add(value.getNumber());
        }
        onChanged();
        return this;
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearMotorStatus() {
        motorStatus_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @return A list containing the enum numeric values on the wire for motorStatus.
       */
      public java.util.List<java.lang.Integer>
      getMotorStatusValueList() {
        return java.util.Collections.unmodifiableList(motorStatus_);
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @param index The index of the value to return.
       * @return The enum numeric value on the wire of motorStatus at the given index.
       */
      public int getMotorStatusValue(int index) {
        return motorStatus_.get(index);
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @param index The index to set the value at.
       * @param value The enum numeric value on the wire for motorStatus to set.
       * @return This builder for chaining.
       */
      public Builder setMotorStatusValue(
          int index, int value) {
        ensureMotorStatusIsMutable();
        motorStatus_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @param value The enum numeric value on the wire for motorStatus to add.
       * @return This builder for chaining.
       */
      public Builder addMotorStatusValue(int value) {
        ensureMotorStatusIsMutable();
        motorStatus_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated .MotorStatus motor_status = 10;</code>
       * @param values The enum numeric values on the wire for motorStatus to add.
       * @return This builder for chaining.
       */
      public Builder addAllMotorStatusValue(
          java.lang.Iterable<java.lang.Integer> values) {
        ensureMotorStatusIsMutable();
        for (int value : values) {
          motorStatus_.add(value);
        }
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MotorControllerStatus)
    }

    // @@protoc_insertion_point(class_scope:MotorControllerStatus)
    private static final com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus();
    }

    public static com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MotorControllerStatus>
        PARSER = new com.google.protobuf.AbstractParser<MotorControllerStatus>() {
      @java.lang.Override
      public MotorControllerStatus parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MotorControllerStatus> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MotorControllerStatus> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MotorControllerProto.MotorControllerStatus getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MotorController_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MotorController_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MotorControllerData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MotorControllerData_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MotorControllerStatus_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MotorControllerStatus_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n4com/nichesolv/nds/model/proto/motor_co" +
      "ntroller.proto\032-com/nichesolv/nds/model/" +
      "proto/timestamp.proto\032,com/nichesolv/nds" +
      "/model/proto/metadata.proto\"\351\001\n\017MotorCon" +
      "troller\022\"\n\004data\030\001 \001(\0132\024.MotorControllerD" +
      "ata\022&\n\006status\030\002 \001(\0132\026.MotorControllerSta" +
      "tus\022\035\n\ttimestamp\030\003 \001(\0132\n.Timestamp\022\033\n\010me" +
      "tadata\030\004 \001(\0132\t.Metadata\022\026\n\tdi_motion\030\005 \001" +
      "(\010H\000\210\001\001\022\030\n\013di_ignition\030\006 \001(\010H\001\210\001\001B\014\n\n_di" +
      "_motionB\016\n\014_di_ignition\"\355\001\n\023MotorControl" +
      "lerData\022\026\n\tdcVoltage\030\001 \001(\002H\000\210\001\001\022\027\n\nmotor" +
      "Speed\030\003 \001(\021H\001\210\001\001\022\026\n\tdcCurrent\030\005 \001(\002H\002\210\001\001" +
      "\022\035\n\020motorTemperature\030\007 \001(\002H\003\210\001\001\022\033\n\016mcsTe" +
      "mperature\030\t \001(\002H\004\210\001\001B\014\n\n_dcVoltageB\r\n\013_m" +
      "otorSpeedB\014\n\n_dcCurrentB\023\n\021_motorTempera" +
      "tureB\021\n\017_mcsTemperature\"\251\003\n\025MotorControl" +
      "lerStatus\022\033\n\016driveSelection\030\001 \001(\tH\000\210\001\001\022\031" +
      "\n\014regeneration\030\002 \001(\010H\001\210\001\001\022\026\n\treadySign\030\003" +
      " \001(\010H\002\210\001\001\022\023\n\006pLight\030\004 \001(\010H\003\210\001\001\022\024\n\007revers" +
      "e\030\005 \001(\010H\004\210\001\001\022\023\n\006cruise\030\006 \001(\010H\005\210\001\001\022\031\n\014veh" +
      "icleBrake\030\007 \001(\010H\006\210\001\001\022\026\n\tsideStand\030\010 \001(\010H" +
      "\007\210\001\001\022\037\n\022throttlePercentage\030\t \001(\021H\010\210\001\001\022\"\n" +
      "\014motor_status\030\n \003(\0162\014.MotorStatusB\021\n\017_dr" +
      "iveSelectionB\017\n\r_regenerationB\014\n\n_readyS" +
      "ignB\t\n\007_pLightB\n\n\010_reverseB\t\n\007_cruiseB\017\n" +
      "\r_vehicleBrakeB\014\n\n_sideStandB\025\n\023_throttl" +
      "ePercentage*\357\002\n\013MotorStatus\022\020\n\014WORKING_F" +
      "INE\020\000\022\020\n\014MOSFET_FAULT\020\001\022\017\n\013DRIVE_FAULT\020\002" +
      "\022\022\n\016OVERLOAD_FAULT\020\004\022\026\n\022OVER_VOLTAGE_FAU" +
      "LT\020\010\022\027\n\023UNDER_VOLTAGE_FAULT\020\014\022\022\n\016OVERHEA" +
      "T_FAULT\020\020\022\026\n\022OVER_CURRENT_FAULT\020\n\022\025\n\021LOW" +
      "_VOLTAGE_FAULT\020 \022\031\n\025MOTOR_LOST_HALL_FAUL" +
      "T\020@\022\026\n\021HALL_SENSOR_FAULT\020\200\001\022\031\n\024MOTOR_OVE" +
      "RHEAT_FAULT\020\200\002\022\026\n\021MOTOR_STUCK_FAULT\020\200\004\022\023" +
      "\n\016THROTTLE_FAULT\020\200\010\022\031\n\025SPEED_OVERSHOOT_F" +
      "AULT\020\016\022\r\n\tNO_STATUS\020\017B;\n#com.nichesolv.n" +
      "ds.model.proto.modelB\024MotorControllerPro" +
      "tob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor(),
        });
    internal_static_MotorController_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_MotorController_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MotorController_descriptor,
        new java.lang.String[] { "Data", "Status", "Timestamp", "Metadata", "DiMotion", "DiIgnition", });
    internal_static_MotorControllerData_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_MotorControllerData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MotorControllerData_descriptor,
        new java.lang.String[] { "DcVoltage", "MotorSpeed", "DcCurrent", "MotorTemperature", "McsTemperature", });
    internal_static_MotorControllerStatus_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_MotorControllerStatus_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MotorControllerStatus_descriptor,
        new java.lang.String[] { "DriveSelection", "Regeneration", "ReadySign", "PLight", "Reverse", "Cruise", "VehicleBrake", "SideStand", "ThrottlePercentage", "MotorStatus", });
    com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
