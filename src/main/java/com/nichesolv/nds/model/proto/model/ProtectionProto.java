// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/protection.proto

package com.nichesolv.nds.model.proto.model;

public final class ProtectionProto {
  private ProtectionProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code Protection}
   */
  public enum Protection
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>CELL_UNDER_VOLTAGE_PROTECTION = 0;</code>
     */
    CELL_UNDER_VOLTAGE_PROTECTION(0),
    /**
     * <code>CELL_OVER_VOLTAGE_PROTECTION = 1;</code>
     */
    CELL_OVER_VOLTAGE_PROTECTION(1),
    /**
     * <code>PACK_UNDER_VOLTAGE_PROTECTION = 2;</code>
     */
    PACK_UNDER_VOLTAGE_PROTECTION(2),
    /**
     * <code>PACK_OVER_VOLTAGE_PROTECTION = 3;</code>
     */
    PACK_OVER_VOLTAGE_PROTECTION(3),
    /**
     * <code>CELL_UNDER_TEMPERATURE_PROTECTION = 4;</code>
     */
    CELL_UNDER_TEMPERATURE_PROTECTION(4),
    /**
     * <code>CELL_OVER_TEMPERATURE_PROTECTION = 5;</code>
     */
    CELL_OVER_TEMPERATURE_PROTECTION(5),
    /**
     * <code>AMBIENT_UNDER_TEMPERATURE_PROTECTION = 6;</code>
     */
    AMBIENT_UNDER_TEMPERATURE_PROTECTION(6),
    /**
     * <code>AMBIENT_OVER_TEMPERATURE_PROTECTION = 7;</code>
     */
    AMBIENT_OVER_TEMPERATURE_PROTECTION(7),
    /**
     * <code>MOSFET_UNDER_TEMPERATURE_PROTECTION = 8;</code>
     */
    MOSFET_UNDER_TEMPERATURE_PROTECTION(8),
    /**
     * <code>MOSFET_OVER_TEMPERATURE_PROTECTION = 9;</code>
     */
    MOSFET_OVER_TEMPERATURE_PROTECTION(9),
    /**
     * <code>THERMAL_RUNWAY_PROTECTION = 10;</code>
     */
    THERMAL_RUNWAY_PROTECTION(10),
    /**
     * <code>BUZZER_OR_LED_PROTECTION = 11;</code>
     */
    BUZZER_OR_LED_PROTECTION(11),
    /**
     * <code>CGH_OVER_CURRENT_PROTECTION = 12;</code>
     */
    CGH_OVER_CURRENT_PROTECTION(12),
    /**
     * <code>DSG_OVER_CURRENT_PROTECTION = 13;</code>
     */
    DSG_OVER_CURRENT_PROTECTION(13),
    /**
     * <code>NO_PROTECTION = -1;</code>
     */
    NO_PROTECTION(-1),
    /**
     * <code>SINGLE_OVERVOLTAGE_PROTECTION = 15;</code>
     */
    SINGLE_OVERVOLTAGE_PROTECTION(15),
    /**
     * <code>SINGLE_UNDERVOLTAGE_PROTECTION = 16;</code>
     */
    SINGLE_UNDERVOLTAGE_PROTECTION(16),
    /**
     * <code>PACK_OVERVOLTAGE_PROTECTION = 17;</code>
     */
    PACK_OVERVOLTAGE_PROTECTION(17),
    /**
     * <code>PACK_UNDERVOLTAGE_PROTECTION = 18;</code>
     */
    PACK_UNDERVOLTAGE_PROTECTION(18),
    /**
     * <code>CHARGE_OVER_TEMPERATURE_PROTECTION = 19;</code>
     */
    CHARGE_OVER_TEMPERATURE_PROTECTION(19),
    /**
     * <code>CHARGE_LOW_TEMPERATURE_PROTECTION = 20;</code>
     */
    CHARGE_LOW_TEMPERATURE_PROTECTION(20),
    /**
     * <code>DISCHARGE_OVER_TEMPERATURE_PROTECTION = 21;</code>
     */
    DISCHARGE_OVER_TEMPERATURE_PROTECTION(21),
    /**
     * <code>DISCHARGE_LOW_TEMPERATURE_PROTECTION = 22;</code>
     */
    DISCHARGE_LOW_TEMPERATURE_PROTECTION(22),
    /**
     * <code>CHARGE_OVERCURRENT_PROTECTION = 23;</code>
     */
    CHARGE_OVERCURRENT_PROTECTION(23),
    /**
     * <code>DISCHARGE_OVERCURRENT_PROTECTION = 24;</code>
     */
    DISCHARGE_OVERCURRENT_PROTECTION(24),
    /**
     * <code>SHORT_CIRCUIT_PROTECTION = 25;</code>
     */
    SHORT_CIRCUIT_PROTECTION(25),
    /**
     * <code>FRONT_DETECTION_IC_ERROR = 26;</code>
     */
    FRONT_DETECTION_IC_ERROR(26),
    /**
     * <code>SOFTWARE_LOCK_MOS = 27;</code>
     */
    SOFTWARE_LOCK_MOS(27),
    /**
     * <code>CHARGE_MOS_OFF = 28;</code>
     */
    CHARGE_MOS_OFF(28),
    /**
     * <code>DISCHARGE_MOS_OFF = 29;</code>
     */
    DISCHARGE_MOS_OFF(29),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>CELL_UNDER_VOLTAGE_PROTECTION = 0;</code>
     */
    public static final int CELL_UNDER_VOLTAGE_PROTECTION_VALUE = 0;
    /**
     * <code>CELL_OVER_VOLTAGE_PROTECTION = 1;</code>
     */
    public static final int CELL_OVER_VOLTAGE_PROTECTION_VALUE = 1;
    /**
     * <code>PACK_UNDER_VOLTAGE_PROTECTION = 2;</code>
     */
    public static final int PACK_UNDER_VOLTAGE_PROTECTION_VALUE = 2;
    /**
     * <code>PACK_OVER_VOLTAGE_PROTECTION = 3;</code>
     */
    public static final int PACK_OVER_VOLTAGE_PROTECTION_VALUE = 3;
    /**
     * <code>CELL_UNDER_TEMPERATURE_PROTECTION = 4;</code>
     */
    public static final int CELL_UNDER_TEMPERATURE_PROTECTION_VALUE = 4;
    /**
     * <code>CELL_OVER_TEMPERATURE_PROTECTION = 5;</code>
     */
    public static final int CELL_OVER_TEMPERATURE_PROTECTION_VALUE = 5;
    /**
     * <code>AMBIENT_UNDER_TEMPERATURE_PROTECTION = 6;</code>
     */
    public static final int AMBIENT_UNDER_TEMPERATURE_PROTECTION_VALUE = 6;
    /**
     * <code>AMBIENT_OVER_TEMPERATURE_PROTECTION = 7;</code>
     */
    public static final int AMBIENT_OVER_TEMPERATURE_PROTECTION_VALUE = 7;
    /**
     * <code>MOSFET_UNDER_TEMPERATURE_PROTECTION = 8;</code>
     */
    public static final int MOSFET_UNDER_TEMPERATURE_PROTECTION_VALUE = 8;
    /**
     * <code>MOSFET_OVER_TEMPERATURE_PROTECTION = 9;</code>
     */
    public static final int MOSFET_OVER_TEMPERATURE_PROTECTION_VALUE = 9;
    /**
     * <code>THERMAL_RUNWAY_PROTECTION = 10;</code>
     */
    public static final int THERMAL_RUNWAY_PROTECTION_VALUE = 10;
    /**
     * <code>BUZZER_OR_LED_PROTECTION = 11;</code>
     */
    public static final int BUZZER_OR_LED_PROTECTION_VALUE = 11;
    /**
     * <code>CGH_OVER_CURRENT_PROTECTION = 12;</code>
     */
    public static final int CGH_OVER_CURRENT_PROTECTION_VALUE = 12;
    /**
     * <code>DSG_OVER_CURRENT_PROTECTION = 13;</code>
     */
    public static final int DSG_OVER_CURRENT_PROTECTION_VALUE = 13;
    /**
     * <code>NO_PROTECTION = -1;</code>
     */
    public static final int NO_PROTECTION_VALUE = -1;
    /**
     * <code>SINGLE_OVERVOLTAGE_PROTECTION = 15;</code>
     */
    public static final int SINGLE_OVERVOLTAGE_PROTECTION_VALUE = 15;
    /**
     * <code>SINGLE_UNDERVOLTAGE_PROTECTION = 16;</code>
     */
    public static final int SINGLE_UNDERVOLTAGE_PROTECTION_VALUE = 16;
    /**
     * <code>PACK_OVERVOLTAGE_PROTECTION = 17;</code>
     */
    public static final int PACK_OVERVOLTAGE_PROTECTION_VALUE = 17;
    /**
     * <code>PACK_UNDERVOLTAGE_PROTECTION = 18;</code>
     */
    public static final int PACK_UNDERVOLTAGE_PROTECTION_VALUE = 18;
    /**
     * <code>CHARGE_OVER_TEMPERATURE_PROTECTION = 19;</code>
     */
    public static final int CHARGE_OVER_TEMPERATURE_PROTECTION_VALUE = 19;
    /**
     * <code>CHARGE_LOW_TEMPERATURE_PROTECTION = 20;</code>
     */
    public static final int CHARGE_LOW_TEMPERATURE_PROTECTION_VALUE = 20;
    /**
     * <code>DISCHARGE_OVER_TEMPERATURE_PROTECTION = 21;</code>
     */
    public static final int DISCHARGE_OVER_TEMPERATURE_PROTECTION_VALUE = 21;
    /**
     * <code>DISCHARGE_LOW_TEMPERATURE_PROTECTION = 22;</code>
     */
    public static final int DISCHARGE_LOW_TEMPERATURE_PROTECTION_VALUE = 22;
    /**
     * <code>CHARGE_OVERCURRENT_PROTECTION = 23;</code>
     */
    public static final int CHARGE_OVERCURRENT_PROTECTION_VALUE = 23;
    /**
     * <code>DISCHARGE_OVERCURRENT_PROTECTION = 24;</code>
     */
    public static final int DISCHARGE_OVERCURRENT_PROTECTION_VALUE = 24;
    /**
     * <code>SHORT_CIRCUIT_PROTECTION = 25;</code>
     */
    public static final int SHORT_CIRCUIT_PROTECTION_VALUE = 25;
    /**
     * <code>FRONT_DETECTION_IC_ERROR = 26;</code>
     */
    public static final int FRONT_DETECTION_IC_ERROR_VALUE = 26;
    /**
     * <code>SOFTWARE_LOCK_MOS = 27;</code>
     */
    public static final int SOFTWARE_LOCK_MOS_VALUE = 27;
    /**
     * <code>CHARGE_MOS_OFF = 28;</code>
     */
    public static final int CHARGE_MOS_OFF_VALUE = 28;
    /**
     * <code>DISCHARGE_MOS_OFF = 29;</code>
     */
    public static final int DISCHARGE_MOS_OFF_VALUE = 29;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static Protection valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static Protection forNumber(int value) {
      switch (value) {
        case 0: return CELL_UNDER_VOLTAGE_PROTECTION;
        case 1: return CELL_OVER_VOLTAGE_PROTECTION;
        case 2: return PACK_UNDER_VOLTAGE_PROTECTION;
        case 3: return PACK_OVER_VOLTAGE_PROTECTION;
        case 4: return CELL_UNDER_TEMPERATURE_PROTECTION;
        case 5: return CELL_OVER_TEMPERATURE_PROTECTION;
        case 6: return AMBIENT_UNDER_TEMPERATURE_PROTECTION;
        case 7: return AMBIENT_OVER_TEMPERATURE_PROTECTION;
        case 8: return MOSFET_UNDER_TEMPERATURE_PROTECTION;
        case 9: return MOSFET_OVER_TEMPERATURE_PROTECTION;
        case 10: return THERMAL_RUNWAY_PROTECTION;
        case 11: return BUZZER_OR_LED_PROTECTION;
        case 12: return CGH_OVER_CURRENT_PROTECTION;
        case 13: return DSG_OVER_CURRENT_PROTECTION;
        case -1: return NO_PROTECTION;
        case 15: return SINGLE_OVERVOLTAGE_PROTECTION;
        case 16: return SINGLE_UNDERVOLTAGE_PROTECTION;
        case 17: return PACK_OVERVOLTAGE_PROTECTION;
        case 18: return PACK_UNDERVOLTAGE_PROTECTION;
        case 19: return CHARGE_OVER_TEMPERATURE_PROTECTION;
        case 20: return CHARGE_LOW_TEMPERATURE_PROTECTION;
        case 21: return DISCHARGE_OVER_TEMPERATURE_PROTECTION;
        case 22: return DISCHARGE_LOW_TEMPERATURE_PROTECTION;
        case 23: return CHARGE_OVERCURRENT_PROTECTION;
        case 24: return DISCHARGE_OVERCURRENT_PROTECTION;
        case 25: return SHORT_CIRCUIT_PROTECTION;
        case 26: return FRONT_DETECTION_IC_ERROR;
        case 27: return SOFTWARE_LOCK_MOS;
        case 28: return CHARGE_MOS_OFF;
        case 29: return DISCHARGE_MOS_OFF;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<Protection>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        Protection> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<Protection>() {
            public Protection findValueByNumber(int number) {
              return Protection.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.ProtectionProto.getDescriptor().getEnumTypes().get(0);
    }

    private static final Protection[] VALUES = values();

    public static Protection valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private Protection(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:Protection)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.com/nichesolv/nds/model/proto/protecti" +
      "on.proto*\227\010\n\nProtection\022!\n\035CELL_UNDER_VO" +
      "LTAGE_PROTECTION\020\000\022 \n\034CELL_OVER_VOLTAGE_" +
      "PROTECTION\020\001\022!\n\035PACK_UNDER_VOLTAGE_PROTE" +
      "CTION\020\002\022 \n\034PACK_OVER_VOLTAGE_PROTECTION\020" +
      "\003\022%\n!CELL_UNDER_TEMPERATURE_PROTECTION\020\004" +
      "\022$\n CELL_OVER_TEMPERATURE_PROTECTION\020\005\022(" +
      "\n$AMBIENT_UNDER_TEMPERATURE_PROTECTION\020\006" +
      "\022\'\n#AMBIENT_OVER_TEMPERATURE_PROTECTION\020" +
      "\007\022\'\n#MOSFET_UNDER_TEMPERATURE_PROTECTION" +
      "\020\010\022&\n\"MOSFET_OVER_TEMPERATURE_PROTECTION" +
      "\020\t\022\035\n\031THERMAL_RUNWAY_PROTECTION\020\n\022\034\n\030BUZ" +
      "ZER_OR_LED_PROTECTION\020\013\022\037\n\033CGH_OVER_CURR" +
      "ENT_PROTECTION\020\014\022\037\n\033DSG_OVER_CURRENT_PRO" +
      "TECTION\020\r\022\032\n\rNO_PROTECTION\020\377\377\377\377\377\377\377\377\377\001\022!\n" +
      "\035SINGLE_OVERVOLTAGE_PROTECTION\020\017\022\"\n\036SING" +
      "LE_UNDERVOLTAGE_PROTECTION\020\020\022\037\n\033PACK_OVE" +
      "RVOLTAGE_PROTECTION\020\021\022 \n\034PACK_UNDERVOLTA" +
      "GE_PROTECTION\020\022\022&\n\"CHARGE_OVER_TEMPERATU" +
      "RE_PROTECTION\020\023\022%\n!CHARGE_LOW_TEMPERATUR" +
      "E_PROTECTION\020\024\022)\n%DISCHARGE_OVER_TEMPERA" +
      "TURE_PROTECTION\020\025\022(\n$DISCHARGE_LOW_TEMPE" +
      "RATURE_PROTECTION\020\026\022!\n\035CHARGE_OVERCURREN" +
      "T_PROTECTION\020\027\022$\n DISCHARGE_OVERCURRENT_" +
      "PROTECTION\020\030\022\034\n\030SHORT_CIRCUIT_PROTECTION" +
      "\020\031\022\034\n\030FRONT_DETECTION_IC_ERROR\020\032\022\025\n\021SOFT" +
      "WARE_LOCK_MOS\020\033\022\022\n\016CHARGE_MOS_OFF\020\034\022\025\n\021D" +
      "ISCHARGE_MOS_OFF\020\035B6\n#com.nichesolv.nds." +
      "model.proto.modelB\017ProtectionProtob\006prot" +
      "o3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
