// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/telemetry.proto

// Protobuf Java Version: 3.25.0
package com.nichesolv.nds.model.proto.model;

public final class TelemetryProto {
  private TelemetryProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TelemetryOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Telemetry)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     * @return The metadata.
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata();
    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     */
    com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder();

    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return The timestamp.
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp();
    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     */
    com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder();

    /**
     * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
     * @return Whether the accelerometer field is set.
     */
    boolean hasAccelerometer();
    /**
     * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
     * @return The accelerometer.
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer getAccelerometer();
    /**
     * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AccelerometerOrBuilder getAccelerometerOrBuilder();

    /**
     * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
     * @return Whether the gyroscope field is set.
     */
    boolean hasGyroscope();
    /**
     * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
     * @return The gyroscope.
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope getGyroscope();
    /**
     * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GyroscopeOrBuilder getGyroscopeOrBuilder();

    /**
     * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
     * @return Whether the gravitationalVector field is set.
     */
    boolean hasGravitationalVector();
    /**
     * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
     * @return The gravitationalVector.
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector getGravitationalVector();
    /**
     * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVectorOrBuilder getGravitationalVectorOrBuilder();

    /**
     * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
     * @return Whether the analogInput field is set.
     */
    boolean hasAnalogInput();
    /**
     * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
     * @return The analogInput.
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput getAnalogInput();
    /**
     * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInputOrBuilder getAnalogInputOrBuilder();

    /**
     * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
     * @return Whether the digitalInput field is set.
     */
    boolean hasDigitalInput();
    /**
     * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
     * @return The digitalInput.
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput getDigitalInput();
    /**
     * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInputOrBuilder getDigitalInputOrBuilder();

    /**
     * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
     * @return Whether the digitalOutput field is set.
     */
    boolean hasDigitalOutput();
    /**
     * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
     * @return The digitalOutput.
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput getDigitalOutput();
    /**
     * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
     */
    com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutputOrBuilder getDigitalOutputOrBuilder();
  }
  /**
   * Protobuf type {@code Telemetry}
   */
  public static final class Telemetry extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Telemetry)
      TelemetryOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Telemetry.newBuilder() to construct.
    private Telemetry(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Telemetry() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Telemetry();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Builder.class);
    }

    public interface AccelerometerOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Telemetry.Accelerometer)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional sint32 x = 1;</code>
       * @return Whether the x field is set.
       */
      boolean hasX();
      /**
       * <code>optional sint32 x = 1;</code>
       * @return The x.
       */
      int getX();

      /**
       * <code>optional sint32 y = 2;</code>
       * @return Whether the y field is set.
       */
      boolean hasY();
      /**
       * <code>optional sint32 y = 2;</code>
       * @return The y.
       */
      int getY();

      /**
       * <code>optional sint32 z = 3;</code>
       * @return Whether the z field is set.
       */
      boolean hasZ();
      /**
       * <code>optional sint32 z = 3;</code>
       * @return The z.
       */
      int getZ();
    }
    /**
     * <pre>
     * Accelerometer Data
     * </pre>
     *
     * Protobuf type {@code Telemetry.Accelerometer}
     */
    public static final class Accelerometer extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:Telemetry.Accelerometer)
        AccelerometerOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Accelerometer.newBuilder() to construct.
      private Accelerometer(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Accelerometer() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Accelerometer();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_Accelerometer_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_Accelerometer_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.Builder.class);
      }

      private int bitField0_;
      public static final int X_FIELD_NUMBER = 1;
      private int x_ = 0;
      /**
       * <code>optional sint32 x = 1;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional sint32 x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return x_;
      }

      public static final int Y_FIELD_NUMBER = 2;
      private int y_ = 0;
      /**
       * <code>optional sint32 y = 2;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional sint32 y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return y_;
      }

      public static final int Z_FIELD_NUMBER = 3;
      private int z_ = 0;
      /**
       * <code>optional sint32 z = 3;</code>
       * @return Whether the z field is set.
       */
      @java.lang.Override
      public boolean hasZ() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional sint32 z = 3;</code>
       * @return The z.
       */
      @java.lang.Override
      public int getZ() {
        return z_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeSInt32(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeSInt32(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeSInt32(3, z_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(3, z_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer)) {
          return super.equals(obj);
        }
        com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer other = (com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer) obj;

        if (hasX() != other.hasX()) return false;
        if (hasX()) {
          if (getX()
              != other.getX()) return false;
        }
        if (hasY() != other.hasY()) return false;
        if (hasY()) {
          if (getY()
              != other.getY()) return false;
        }
        if (hasZ() != other.hasZ()) return false;
        if (hasZ()) {
          if (getZ()
              != other.getZ()) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasX()) {
          hash = (37 * hash) + X_FIELD_NUMBER;
          hash = (53 * hash) + getX();
        }
        if (hasY()) {
          hash = (37 * hash) + Y_FIELD_NUMBER;
          hash = (53 * hash) + getY();
        }
        if (hasZ()) {
          hash = (37 * hash) + Z_FIELD_NUMBER;
          hash = (53 * hash) + getZ();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * Accelerometer Data
       * </pre>
       *
       * Protobuf type {@code Telemetry.Accelerometer}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Telemetry.Accelerometer)
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AccelerometerOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_Accelerometer_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_Accelerometer_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.Builder.class);
        }

        // Construct using com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          x_ = 0;
          y_ = 0;
          z_ = 0;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_Accelerometer_descriptor;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer getDefaultInstanceForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.getDefaultInstance();
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer build() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer buildPartial() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer result = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.x_ = x_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.y_ = y_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.z_ = z_;
            to_bitField0_ |= 0x00000004;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer) {
            return mergeFrom((com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer other) {
          if (other == com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.getDefaultInstance()) return this;
          if (other.hasX()) {
            setX(other.getX());
          }
          if (other.hasY()) {
            setY(other.getY());
          }
          if (other.hasZ()) {
            setZ(other.getZ());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  x_ = input.readSInt32();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 16: {
                  y_ = input.readSInt32();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                case 24: {
                  z_ = input.readSInt32();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private int x_ ;
        /**
         * <code>optional sint32 x = 1;</code>
         * @return Whether the x field is set.
         */
        @java.lang.Override
        public boolean hasX() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @return The x.
         */
        @java.lang.Override
        public int getX() {
          return x_;
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @param value The x to set.
         * @return This builder for chaining.
         */
        public Builder setX(int value) {

          x_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearX() {
          bitField0_ = (bitField0_ & ~0x00000001);
          x_ = 0;
          onChanged();
          return this;
        }

        private int y_ ;
        /**
         * <code>optional sint32 y = 2;</code>
         * @return Whether the y field is set.
         */
        @java.lang.Override
        public boolean hasY() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @return The y.
         */
        @java.lang.Override
        public int getY() {
          return y_;
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @param value The y to set.
         * @return This builder for chaining.
         */
        public Builder setY(int value) {

          y_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearY() {
          bitField0_ = (bitField0_ & ~0x00000002);
          y_ = 0;
          onChanged();
          return this;
        }

        private int z_ ;
        /**
         * <code>optional sint32 z = 3;</code>
         * @return Whether the z field is set.
         */
        @java.lang.Override
        public boolean hasZ() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @return The z.
         */
        @java.lang.Override
        public int getZ() {
          return z_;
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @param value The z to set.
         * @return This builder for chaining.
         */
        public Builder setZ(int value) {

          z_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearZ() {
          bitField0_ = (bitField0_ & ~0x00000004);
          z_ = 0;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:Telemetry.Accelerometer)
      }

      // @@protoc_insertion_point(class_scope:Telemetry.Accelerometer)
      private static final com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer();
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Accelerometer>
          PARSER = new com.google.protobuf.AbstractParser<Accelerometer>() {
        @java.lang.Override
        public Accelerometer parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Accelerometer> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Accelerometer> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface GyroscopeOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Telemetry.Gyroscope)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional sint32 x = 1;</code>
       * @return Whether the x field is set.
       */
      boolean hasX();
      /**
       * <code>optional sint32 x = 1;</code>
       * @return The x.
       */
      int getX();

      /**
       * <code>optional sint32 y = 2;</code>
       * @return Whether the y field is set.
       */
      boolean hasY();
      /**
       * <code>optional sint32 y = 2;</code>
       * @return The y.
       */
      int getY();

      /**
       * <code>optional sint32 z = 3;</code>
       * @return Whether the z field is set.
       */
      boolean hasZ();
      /**
       * <code>optional sint32 z = 3;</code>
       * @return The z.
       */
      int getZ();
    }
    /**
     * <pre>
     * Gyroscope Data
     * </pre>
     *
     * Protobuf type {@code Telemetry.Gyroscope}
     */
    public static final class Gyroscope extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:Telemetry.Gyroscope)
        GyroscopeOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Gyroscope.newBuilder() to construct.
      private Gyroscope(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Gyroscope() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Gyroscope();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_Gyroscope_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_Gyroscope_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.Builder.class);
      }

      private int bitField0_;
      public static final int X_FIELD_NUMBER = 1;
      private int x_ = 0;
      /**
       * <code>optional sint32 x = 1;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional sint32 x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return x_;
      }

      public static final int Y_FIELD_NUMBER = 2;
      private int y_ = 0;
      /**
       * <code>optional sint32 y = 2;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional sint32 y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return y_;
      }

      public static final int Z_FIELD_NUMBER = 3;
      private int z_ = 0;
      /**
       * <code>optional sint32 z = 3;</code>
       * @return Whether the z field is set.
       */
      @java.lang.Override
      public boolean hasZ() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional sint32 z = 3;</code>
       * @return The z.
       */
      @java.lang.Override
      public int getZ() {
        return z_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeSInt32(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeSInt32(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeSInt32(3, z_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(3, z_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope)) {
          return super.equals(obj);
        }
        com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope other = (com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope) obj;

        if (hasX() != other.hasX()) return false;
        if (hasX()) {
          if (getX()
              != other.getX()) return false;
        }
        if (hasY() != other.hasY()) return false;
        if (hasY()) {
          if (getY()
              != other.getY()) return false;
        }
        if (hasZ() != other.hasZ()) return false;
        if (hasZ()) {
          if (getZ()
              != other.getZ()) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasX()) {
          hash = (37 * hash) + X_FIELD_NUMBER;
          hash = (53 * hash) + getX();
        }
        if (hasY()) {
          hash = (37 * hash) + Y_FIELD_NUMBER;
          hash = (53 * hash) + getY();
        }
        if (hasZ()) {
          hash = (37 * hash) + Z_FIELD_NUMBER;
          hash = (53 * hash) + getZ();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * Gyroscope Data
       * </pre>
       *
       * Protobuf type {@code Telemetry.Gyroscope}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Telemetry.Gyroscope)
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GyroscopeOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_Gyroscope_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_Gyroscope_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.Builder.class);
        }

        // Construct using com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          x_ = 0;
          y_ = 0;
          z_ = 0;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_Gyroscope_descriptor;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope getDefaultInstanceForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.getDefaultInstance();
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope build() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope buildPartial() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope result = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.x_ = x_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.y_ = y_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.z_ = z_;
            to_bitField0_ |= 0x00000004;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope) {
            return mergeFrom((com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope other) {
          if (other == com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.getDefaultInstance()) return this;
          if (other.hasX()) {
            setX(other.getX());
          }
          if (other.hasY()) {
            setY(other.getY());
          }
          if (other.hasZ()) {
            setZ(other.getZ());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  x_ = input.readSInt32();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 16: {
                  y_ = input.readSInt32();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                case 24: {
                  z_ = input.readSInt32();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private int x_ ;
        /**
         * <code>optional sint32 x = 1;</code>
         * @return Whether the x field is set.
         */
        @java.lang.Override
        public boolean hasX() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @return The x.
         */
        @java.lang.Override
        public int getX() {
          return x_;
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @param value The x to set.
         * @return This builder for chaining.
         */
        public Builder setX(int value) {

          x_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 x = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearX() {
          bitField0_ = (bitField0_ & ~0x00000001);
          x_ = 0;
          onChanged();
          return this;
        }

        private int y_ ;
        /**
         * <code>optional sint32 y = 2;</code>
         * @return Whether the y field is set.
         */
        @java.lang.Override
        public boolean hasY() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @return The y.
         */
        @java.lang.Override
        public int getY() {
          return y_;
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @param value The y to set.
         * @return This builder for chaining.
         */
        public Builder setY(int value) {

          y_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 y = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearY() {
          bitField0_ = (bitField0_ & ~0x00000002);
          y_ = 0;
          onChanged();
          return this;
        }

        private int z_ ;
        /**
         * <code>optional sint32 z = 3;</code>
         * @return Whether the z field is set.
         */
        @java.lang.Override
        public boolean hasZ() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @return The z.
         */
        @java.lang.Override
        public int getZ() {
          return z_;
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @param value The z to set.
         * @return This builder for chaining.
         */
        public Builder setZ(int value) {

          z_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 z = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearZ() {
          bitField0_ = (bitField0_ & ~0x00000004);
          z_ = 0;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:Telemetry.Gyroscope)
      }

      // @@protoc_insertion_point(class_scope:Telemetry.Gyroscope)
      private static final com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope();
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Gyroscope>
          PARSER = new com.google.protobuf.AbstractParser<Gyroscope>() {
        @java.lang.Override
        public Gyroscope parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Gyroscope> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Gyroscope> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface GravitationalVectorOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Telemetry.GravitationalVector)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional float x = 1;</code>
       * @return Whether the x field is set.
       */
      boolean hasX();
      /**
       * <code>optional float x = 1;</code>
       * @return The x.
       */
      float getX();

      /**
       * <code>optional float y = 2;</code>
       * @return Whether the y field is set.
       */
      boolean hasY();
      /**
       * <code>optional float y = 2;</code>
       * @return The y.
       */
      float getY();

      /**
       * <code>optional float z = 3;</code>
       * @return Whether the z field is set.
       */
      boolean hasZ();
      /**
       * <code>optional float z = 3;</code>
       * @return The z.
       */
      float getZ();
    }
    /**
     * <pre>
     * Gravitational Vector Data
     * </pre>
     *
     * Protobuf type {@code Telemetry.GravitationalVector}
     */
    public static final class GravitationalVector extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:Telemetry.GravitationalVector)
        GravitationalVectorOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use GravitationalVector.newBuilder() to construct.
      private GravitationalVector(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private GravitationalVector() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new GravitationalVector();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_GravitationalVector_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_GravitationalVector_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.Builder.class);
      }

      private int bitField0_;
      public static final int X_FIELD_NUMBER = 1;
      private float x_ = 0F;
      /**
       * <code>optional float x = 1;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional float x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public float getX() {
        return x_;
      }

      public static final int Y_FIELD_NUMBER = 2;
      private float y_ = 0F;
      /**
       * <code>optional float y = 2;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional float y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public float getY() {
        return y_;
      }

      public static final int Z_FIELD_NUMBER = 3;
      private float z_ = 0F;
      /**
       * <code>optional float z = 3;</code>
       * @return Whether the z field is set.
       */
      @java.lang.Override
      public boolean hasZ() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional float z = 3;</code>
       * @return The z.
       */
      @java.lang.Override
      public float getZ() {
        return z_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeFloat(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeFloat(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeFloat(3, z_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeFloatSize(1, x_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeFloatSize(2, y_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeFloatSize(3, z_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector)) {
          return super.equals(obj);
        }
        com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector other = (com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector) obj;

        if (hasX() != other.hasX()) return false;
        if (hasX()) {
          if (java.lang.Float.floatToIntBits(getX())
              != java.lang.Float.floatToIntBits(
                  other.getX())) return false;
        }
        if (hasY() != other.hasY()) return false;
        if (hasY()) {
          if (java.lang.Float.floatToIntBits(getY())
              != java.lang.Float.floatToIntBits(
                  other.getY())) return false;
        }
        if (hasZ() != other.hasZ()) return false;
        if (hasZ()) {
          if (java.lang.Float.floatToIntBits(getZ())
              != java.lang.Float.floatToIntBits(
                  other.getZ())) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasX()) {
          hash = (37 * hash) + X_FIELD_NUMBER;
          hash = (53 * hash) + java.lang.Float.floatToIntBits(
              getX());
        }
        if (hasY()) {
          hash = (37 * hash) + Y_FIELD_NUMBER;
          hash = (53 * hash) + java.lang.Float.floatToIntBits(
              getY());
        }
        if (hasZ()) {
          hash = (37 * hash) + Z_FIELD_NUMBER;
          hash = (53 * hash) + java.lang.Float.floatToIntBits(
              getZ());
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * Gravitational Vector Data
       * </pre>
       *
       * Protobuf type {@code Telemetry.GravitationalVector}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Telemetry.GravitationalVector)
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVectorOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_GravitationalVector_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_GravitationalVector_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.Builder.class);
        }

        // Construct using com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          x_ = 0F;
          y_ = 0F;
          z_ = 0F;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_GravitationalVector_descriptor;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector getDefaultInstanceForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.getDefaultInstance();
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector build() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector buildPartial() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector result = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.x_ = x_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.y_ = y_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.z_ = z_;
            to_bitField0_ |= 0x00000004;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector) {
            return mergeFrom((com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector other) {
          if (other == com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.getDefaultInstance()) return this;
          if (other.hasX()) {
            setX(other.getX());
          }
          if (other.hasY()) {
            setY(other.getY());
          }
          if (other.hasZ()) {
            setZ(other.getZ());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 13: {
                  x_ = input.readFloat();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 13
                case 21: {
                  y_ = input.readFloat();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 21
                case 29: {
                  z_ = input.readFloat();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 29
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private float x_ ;
        /**
         * <code>optional float x = 1;</code>
         * @return Whether the x field is set.
         */
        @java.lang.Override
        public boolean hasX() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional float x = 1;</code>
         * @return The x.
         */
        @java.lang.Override
        public float getX() {
          return x_;
        }
        /**
         * <code>optional float x = 1;</code>
         * @param value The x to set.
         * @return This builder for chaining.
         */
        public Builder setX(float value) {

          x_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>optional float x = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearX() {
          bitField0_ = (bitField0_ & ~0x00000001);
          x_ = 0F;
          onChanged();
          return this;
        }

        private float y_ ;
        /**
         * <code>optional float y = 2;</code>
         * @return Whether the y field is set.
         */
        @java.lang.Override
        public boolean hasY() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional float y = 2;</code>
         * @return The y.
         */
        @java.lang.Override
        public float getY() {
          return y_;
        }
        /**
         * <code>optional float y = 2;</code>
         * @param value The y to set.
         * @return This builder for chaining.
         */
        public Builder setY(float value) {

          y_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>optional float y = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearY() {
          bitField0_ = (bitField0_ & ~0x00000002);
          y_ = 0F;
          onChanged();
          return this;
        }

        private float z_ ;
        /**
         * <code>optional float z = 3;</code>
         * @return Whether the z field is set.
         */
        @java.lang.Override
        public boolean hasZ() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <code>optional float z = 3;</code>
         * @return The z.
         */
        @java.lang.Override
        public float getZ() {
          return z_;
        }
        /**
         * <code>optional float z = 3;</code>
         * @param value The z to set.
         * @return This builder for chaining.
         */
        public Builder setZ(float value) {

          z_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>optional float z = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearZ() {
          bitField0_ = (bitField0_ & ~0x00000004);
          z_ = 0F;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:Telemetry.GravitationalVector)
      }

      // @@protoc_insertion_point(class_scope:Telemetry.GravitationalVector)
      private static final com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector();
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<GravitationalVector>
          PARSER = new com.google.protobuf.AbstractParser<GravitationalVector>() {
        @java.lang.Override
        public GravitationalVector parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<GravitationalVector> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<GravitationalVector> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface AnalogInputOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Telemetry.AnalogInput)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional sint32 temp = 1;</code>
       * @return Whether the temp field is set.
       */
      boolean hasTemp();
      /**
       * <code>optional sint32 temp = 1;</code>
       * @return The temp.
       */
      int getTemp();

      /**
       * <code>optional uint32 vin = 2;</code>
       * @return Whether the vin field is set.
       */
      boolean hasVin();
      /**
       * <code>optional uint32 vin = 2;</code>
       * @return The vin.
       */
      int getVin();

      /**
       * <code>optional uint32 vsys = 3;</code>
       * @return Whether the vsys field is set.
       */
      boolean hasVsys();
      /**
       * <code>optional uint32 vsys = 3;</code>
       * @return The vsys.
       */
      int getVsys();

      /**
       * <code>optional uint32 vbuck = 4;</code>
       * @return Whether the vbuck field is set.
       */
      boolean hasVbuck();
      /**
       * <code>optional uint32 vbuck = 4;</code>
       * @return The vbuck.
       */
      int getVbuck();

      /**
       * <code>optional uint32 vusr_1 = 5;</code>
       * @return Whether the vusr1 field is set.
       */
      boolean hasVusr1();
      /**
       * <code>optional uint32 vusr_1 = 5;</code>
       * @return The vusr1.
       */
      int getVusr1();

      /**
       * <code>optional uint32 vusr_2 = 6;</code>
       * @return Whether the vusr2 field is set.
       */
      boolean hasVusr2();
      /**
       * <code>optional uint32 vusr_2 = 6;</code>
       * @return The vusr2.
       */
      int getVusr2();

      /**
       * <code>optional uint32 lean_angle = 7;</code>
       * @return Whether the leanAngle field is set.
       */
      boolean hasLeanAngle();
      /**
       * <code>optional uint32 lean_angle = 7;</code>
       * @return The leanAngle.
       */
      int getLeanAngle();
    }
    /**
     * <pre>
     * Analog Input Data
     * </pre>
     *
     * Protobuf type {@code Telemetry.AnalogInput}
     */
    public static final class AnalogInput extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:Telemetry.AnalogInput)
        AnalogInputOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use AnalogInput.newBuilder() to construct.
      private AnalogInput(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private AnalogInput() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new AnalogInput();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_AnalogInput_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_AnalogInput_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.Builder.class);
      }

      private int bitField0_;
      public static final int TEMP_FIELD_NUMBER = 1;
      private int temp_ = 0;
      /**
       * <code>optional sint32 temp = 1;</code>
       * @return Whether the temp field is set.
       */
      @java.lang.Override
      public boolean hasTemp() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional sint32 temp = 1;</code>
       * @return The temp.
       */
      @java.lang.Override
      public int getTemp() {
        return temp_;
      }

      public static final int VIN_FIELD_NUMBER = 2;
      private int vin_ = 0;
      /**
       * <code>optional uint32 vin = 2;</code>
       * @return Whether the vin field is set.
       */
      @java.lang.Override
      public boolean hasVin() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 vin = 2;</code>
       * @return The vin.
       */
      @java.lang.Override
      public int getVin() {
        return vin_;
      }

      public static final int VSYS_FIELD_NUMBER = 3;
      private int vsys_ = 0;
      /**
       * <code>optional uint32 vsys = 3;</code>
       * @return Whether the vsys field is set.
       */
      @java.lang.Override
      public boolean hasVsys() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint32 vsys = 3;</code>
       * @return The vsys.
       */
      @java.lang.Override
      public int getVsys() {
        return vsys_;
      }

      public static final int VBUCK_FIELD_NUMBER = 4;
      private int vbuck_ = 0;
      /**
       * <code>optional uint32 vbuck = 4;</code>
       * @return Whether the vbuck field is set.
       */
      @java.lang.Override
      public boolean hasVbuck() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 vbuck = 4;</code>
       * @return The vbuck.
       */
      @java.lang.Override
      public int getVbuck() {
        return vbuck_;
      }

      public static final int VUSR_1_FIELD_NUMBER = 5;
      private int vusr1_ = 0;
      /**
       * <code>optional uint32 vusr_1 = 5;</code>
       * @return Whether the vusr1 field is set.
       */
      @java.lang.Override
      public boolean hasVusr1() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint32 vusr_1 = 5;</code>
       * @return The vusr1.
       */
      @java.lang.Override
      public int getVusr1() {
        return vusr1_;
      }

      public static final int VUSR_2_FIELD_NUMBER = 6;
      private int vusr2_ = 0;
      /**
       * <code>optional uint32 vusr_2 = 6;</code>
       * @return Whether the vusr2 field is set.
       */
      @java.lang.Override
      public boolean hasVusr2() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint32 vusr_2 = 6;</code>
       * @return The vusr2.
       */
      @java.lang.Override
      public int getVusr2() {
        return vusr2_;
      }

      public static final int LEAN_ANGLE_FIELD_NUMBER = 7;
      private int leanAngle_ = 0;
      /**
       * <code>optional uint32 lean_angle = 7;</code>
       * @return Whether the leanAngle field is set.
       */
      @java.lang.Override
      public boolean hasLeanAngle() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 lean_angle = 7;</code>
       * @return The leanAngle.
       */
      @java.lang.Override
      public int getLeanAngle() {
        return leanAngle_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeSInt32(1, temp_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeUInt32(2, vin_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeUInt32(3, vsys_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          output.writeUInt32(4, vbuck_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          output.writeUInt32(5, vusr1_);
        }
        if (((bitField0_ & 0x00000020) != 0)) {
          output.writeUInt32(6, vusr2_);
        }
        if (((bitField0_ & 0x00000040) != 0)) {
          output.writeUInt32(7, leanAngle_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(1, temp_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(2, vin_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(3, vsys_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(4, vbuck_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(5, vusr1_);
        }
        if (((bitField0_ & 0x00000020) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(6, vusr2_);
        }
        if (((bitField0_ & 0x00000040) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(7, leanAngle_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput)) {
          return super.equals(obj);
        }
        com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput other = (com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput) obj;

        if (hasTemp() != other.hasTemp()) return false;
        if (hasTemp()) {
          if (getTemp()
              != other.getTemp()) return false;
        }
        if (hasVin() != other.hasVin()) return false;
        if (hasVin()) {
          if (getVin()
              != other.getVin()) return false;
        }
        if (hasVsys() != other.hasVsys()) return false;
        if (hasVsys()) {
          if (getVsys()
              != other.getVsys()) return false;
        }
        if (hasVbuck() != other.hasVbuck()) return false;
        if (hasVbuck()) {
          if (getVbuck()
              != other.getVbuck()) return false;
        }
        if (hasVusr1() != other.hasVusr1()) return false;
        if (hasVusr1()) {
          if (getVusr1()
              != other.getVusr1()) return false;
        }
        if (hasVusr2() != other.hasVusr2()) return false;
        if (hasVusr2()) {
          if (getVusr2()
              != other.getVusr2()) return false;
        }
        if (hasLeanAngle() != other.hasLeanAngle()) return false;
        if (hasLeanAngle()) {
          if (getLeanAngle()
              != other.getLeanAngle()) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasTemp()) {
          hash = (37 * hash) + TEMP_FIELD_NUMBER;
          hash = (53 * hash) + getTemp();
        }
        if (hasVin()) {
          hash = (37 * hash) + VIN_FIELD_NUMBER;
          hash = (53 * hash) + getVin();
        }
        if (hasVsys()) {
          hash = (37 * hash) + VSYS_FIELD_NUMBER;
          hash = (53 * hash) + getVsys();
        }
        if (hasVbuck()) {
          hash = (37 * hash) + VBUCK_FIELD_NUMBER;
          hash = (53 * hash) + getVbuck();
        }
        if (hasVusr1()) {
          hash = (37 * hash) + VUSR_1_FIELD_NUMBER;
          hash = (53 * hash) + getVusr1();
        }
        if (hasVusr2()) {
          hash = (37 * hash) + VUSR_2_FIELD_NUMBER;
          hash = (53 * hash) + getVusr2();
        }
        if (hasLeanAngle()) {
          hash = (37 * hash) + LEAN_ANGLE_FIELD_NUMBER;
          hash = (53 * hash) + getLeanAngle();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * Analog Input Data
       * </pre>
       *
       * Protobuf type {@code Telemetry.AnalogInput}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Telemetry.AnalogInput)
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInputOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_AnalogInput_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_AnalogInput_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.Builder.class);
        }

        // Construct using com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          temp_ = 0;
          vin_ = 0;
          vsys_ = 0;
          vbuck_ = 0;
          vusr1_ = 0;
          vusr2_ = 0;
          leanAngle_ = 0;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_AnalogInput_descriptor;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput getDefaultInstanceForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.getDefaultInstance();
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput build() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput buildPartial() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput result = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.temp_ = temp_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.vin_ = vin_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.vsys_ = vsys_;
            to_bitField0_ |= 0x00000004;
          }
          if (((from_bitField0_ & 0x00000008) != 0)) {
            result.vbuck_ = vbuck_;
            to_bitField0_ |= 0x00000008;
          }
          if (((from_bitField0_ & 0x00000010) != 0)) {
            result.vusr1_ = vusr1_;
            to_bitField0_ |= 0x00000010;
          }
          if (((from_bitField0_ & 0x00000020) != 0)) {
            result.vusr2_ = vusr2_;
            to_bitField0_ |= 0x00000020;
          }
          if (((from_bitField0_ & 0x00000040) != 0)) {
            result.leanAngle_ = leanAngle_;
            to_bitField0_ |= 0x00000040;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput) {
            return mergeFrom((com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput other) {
          if (other == com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.getDefaultInstance()) return this;
          if (other.hasTemp()) {
            setTemp(other.getTemp());
          }
          if (other.hasVin()) {
            setVin(other.getVin());
          }
          if (other.hasVsys()) {
            setVsys(other.getVsys());
          }
          if (other.hasVbuck()) {
            setVbuck(other.getVbuck());
          }
          if (other.hasVusr1()) {
            setVusr1(other.getVusr1());
          }
          if (other.hasVusr2()) {
            setVusr2(other.getVusr2());
          }
          if (other.hasLeanAngle()) {
            setLeanAngle(other.getLeanAngle());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  temp_ = input.readSInt32();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 16: {
                  vin_ = input.readUInt32();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                case 24: {
                  vsys_ = input.readUInt32();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                case 32: {
                  vbuck_ = input.readUInt32();
                  bitField0_ |= 0x00000008;
                  break;
                } // case 32
                case 40: {
                  vusr1_ = input.readUInt32();
                  bitField0_ |= 0x00000010;
                  break;
                } // case 40
                case 48: {
                  vusr2_ = input.readUInt32();
                  bitField0_ |= 0x00000020;
                  break;
                } // case 48
                case 56: {
                  leanAngle_ = input.readUInt32();
                  bitField0_ |= 0x00000040;
                  break;
                } // case 56
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private int temp_ ;
        /**
         * <code>optional sint32 temp = 1;</code>
         * @return Whether the temp field is set.
         */
        @java.lang.Override
        public boolean hasTemp() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional sint32 temp = 1;</code>
         * @return The temp.
         */
        @java.lang.Override
        public int getTemp() {
          return temp_;
        }
        /**
         * <code>optional sint32 temp = 1;</code>
         * @param value The temp to set.
         * @return This builder for chaining.
         */
        public Builder setTemp(int value) {

          temp_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>optional sint32 temp = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearTemp() {
          bitField0_ = (bitField0_ & ~0x00000001);
          temp_ = 0;
          onChanged();
          return this;
        }

        private int vin_ ;
        /**
         * <code>optional uint32 vin = 2;</code>
         * @return Whether the vin field is set.
         */
        @java.lang.Override
        public boolean hasVin() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional uint32 vin = 2;</code>
         * @return The vin.
         */
        @java.lang.Override
        public int getVin() {
          return vin_;
        }
        /**
         * <code>optional uint32 vin = 2;</code>
         * @param value The vin to set.
         * @return This builder for chaining.
         */
        public Builder setVin(int value) {

          vin_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>optional uint32 vin = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearVin() {
          bitField0_ = (bitField0_ & ~0x00000002);
          vin_ = 0;
          onChanged();
          return this;
        }

        private int vsys_ ;
        /**
         * <code>optional uint32 vsys = 3;</code>
         * @return Whether the vsys field is set.
         */
        @java.lang.Override
        public boolean hasVsys() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <code>optional uint32 vsys = 3;</code>
         * @return The vsys.
         */
        @java.lang.Override
        public int getVsys() {
          return vsys_;
        }
        /**
         * <code>optional uint32 vsys = 3;</code>
         * @param value The vsys to set.
         * @return This builder for chaining.
         */
        public Builder setVsys(int value) {

          vsys_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>optional uint32 vsys = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearVsys() {
          bitField0_ = (bitField0_ & ~0x00000004);
          vsys_ = 0;
          onChanged();
          return this;
        }

        private int vbuck_ ;
        /**
         * <code>optional uint32 vbuck = 4;</code>
         * @return Whether the vbuck field is set.
         */
        @java.lang.Override
        public boolean hasVbuck() {
          return ((bitField0_ & 0x00000008) != 0);
        }
        /**
         * <code>optional uint32 vbuck = 4;</code>
         * @return The vbuck.
         */
        @java.lang.Override
        public int getVbuck() {
          return vbuck_;
        }
        /**
         * <code>optional uint32 vbuck = 4;</code>
         * @param value The vbuck to set.
         * @return This builder for chaining.
         */
        public Builder setVbuck(int value) {

          vbuck_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <code>optional uint32 vbuck = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearVbuck() {
          bitField0_ = (bitField0_ & ~0x00000008);
          vbuck_ = 0;
          onChanged();
          return this;
        }

        private int vusr1_ ;
        /**
         * <code>optional uint32 vusr_1 = 5;</code>
         * @return Whether the vusr1 field is set.
         */
        @java.lang.Override
        public boolean hasVusr1() {
          return ((bitField0_ & 0x00000010) != 0);
        }
        /**
         * <code>optional uint32 vusr_1 = 5;</code>
         * @return The vusr1.
         */
        @java.lang.Override
        public int getVusr1() {
          return vusr1_;
        }
        /**
         * <code>optional uint32 vusr_1 = 5;</code>
         * @param value The vusr1 to set.
         * @return This builder for chaining.
         */
        public Builder setVusr1(int value) {

          vusr1_ = value;
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <code>optional uint32 vusr_1 = 5;</code>
         * @return This builder for chaining.
         */
        public Builder clearVusr1() {
          bitField0_ = (bitField0_ & ~0x00000010);
          vusr1_ = 0;
          onChanged();
          return this;
        }

        private int vusr2_ ;
        /**
         * <code>optional uint32 vusr_2 = 6;</code>
         * @return Whether the vusr2 field is set.
         */
        @java.lang.Override
        public boolean hasVusr2() {
          return ((bitField0_ & 0x00000020) != 0);
        }
        /**
         * <code>optional uint32 vusr_2 = 6;</code>
         * @return The vusr2.
         */
        @java.lang.Override
        public int getVusr2() {
          return vusr2_;
        }
        /**
         * <code>optional uint32 vusr_2 = 6;</code>
         * @param value The vusr2 to set.
         * @return This builder for chaining.
         */
        public Builder setVusr2(int value) {

          vusr2_ = value;
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>optional uint32 vusr_2 = 6;</code>
         * @return This builder for chaining.
         */
        public Builder clearVusr2() {
          bitField0_ = (bitField0_ & ~0x00000020);
          vusr2_ = 0;
          onChanged();
          return this;
        }

        private int leanAngle_ ;
        /**
         * <code>optional uint32 lean_angle = 7;</code>
         * @return Whether the leanAngle field is set.
         */
        @java.lang.Override
        public boolean hasLeanAngle() {
          return ((bitField0_ & 0x00000040) != 0);
        }
        /**
         * <code>optional uint32 lean_angle = 7;</code>
         * @return The leanAngle.
         */
        @java.lang.Override
        public int getLeanAngle() {
          return leanAngle_;
        }
        /**
         * <code>optional uint32 lean_angle = 7;</code>
         * @param value The leanAngle to set.
         * @return This builder for chaining.
         */
        public Builder setLeanAngle(int value) {

          leanAngle_ = value;
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }
        /**
         * <code>optional uint32 lean_angle = 7;</code>
         * @return This builder for chaining.
         */
        public Builder clearLeanAngle() {
          bitField0_ = (bitField0_ & ~0x00000040);
          leanAngle_ = 0;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:Telemetry.AnalogInput)
      }

      // @@protoc_insertion_point(class_scope:Telemetry.AnalogInput)
      private static final com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput();
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<AnalogInput>
          PARSER = new com.google.protobuf.AbstractParser<AnalogInput>() {
        @java.lang.Override
        public AnalogInput parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<AnalogInput> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<AnalogInput> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface DigitalInputOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Telemetry.DigitalInput)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional bool usr1 = 1;</code>
       * @return Whether the usr1 field is set.
       */
      boolean hasUsr1();
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return The usr1.
       */
      boolean getUsr1();

      /**
       * <code>optional bool usr2 = 2;</code>
       * @return Whether the usr2 field is set.
       */
      boolean hasUsr2();
      /**
       * <code>optional bool usr2 = 2;</code>
       * @return The usr2.
       */
      boolean getUsr2();

      /**
       * <code>optional bool motion = 3;</code>
       * @return Whether the motion field is set.
       */
      boolean hasMotion();
      /**
       * <code>optional bool motion = 3;</code>
       * @return The motion.
       */
      boolean getMotion();

      /**
       * <code>optional bool tamper = 4;</code>
       * @return Whether the tamper field is set.
       */
      boolean hasTamper();
      /**
       * <code>optional bool tamper = 4;</code>
       * @return The tamper.
       */
      boolean getTamper();

      /**
       * <code>optional bool main_power = 5;</code>
       * @return Whether the mainPower field is set.
       */
      boolean hasMainPower();
      /**
       * <code>optional bool main_power = 5;</code>
       * @return The mainPower.
       */
      boolean getMainPower();

      /**
       * <code>optional bool ignition = 6;</code>
       * @return Whether the ignition field is set.
       */
      boolean hasIgnition();
      /**
       * <code>optional bool ignition = 6;</code>
       * @return The ignition.
       */
      boolean getIgnition();
    }
    /**
     * <pre>
     * Digital Input Data
     * </pre>
     *
     * Protobuf type {@code Telemetry.DigitalInput}
     */
    public static final class DigitalInput extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:Telemetry.DigitalInput)
        DigitalInputOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use DigitalInput.newBuilder() to construct.
      private DigitalInput(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private DigitalInput() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new DigitalInput();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_DigitalInput_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_DigitalInput_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.Builder.class);
      }

      private int bitField0_;
      public static final int USR1_FIELD_NUMBER = 1;
      private boolean usr1_ = false;
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return Whether the usr1 field is set.
       */
      @java.lang.Override
      public boolean hasUsr1() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return The usr1.
       */
      @java.lang.Override
      public boolean getUsr1() {
        return usr1_;
      }

      public static final int USR2_FIELD_NUMBER = 2;
      private boolean usr2_ = false;
      /**
       * <code>optional bool usr2 = 2;</code>
       * @return Whether the usr2 field is set.
       */
      @java.lang.Override
      public boolean hasUsr2() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bool usr2 = 2;</code>
       * @return The usr2.
       */
      @java.lang.Override
      public boolean getUsr2() {
        return usr2_;
      }

      public static final int MOTION_FIELD_NUMBER = 3;
      private boolean motion_ = false;
      /**
       * <code>optional bool motion = 3;</code>
       * @return Whether the motion field is set.
       */
      @java.lang.Override
      public boolean hasMotion() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool motion = 3;</code>
       * @return The motion.
       */
      @java.lang.Override
      public boolean getMotion() {
        return motion_;
      }

      public static final int TAMPER_FIELD_NUMBER = 4;
      private boolean tamper_ = false;
      /**
       * <code>optional bool tamper = 4;</code>
       * @return Whether the tamper field is set.
       */
      @java.lang.Override
      public boolean hasTamper() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bool tamper = 4;</code>
       * @return The tamper.
       */
      @java.lang.Override
      public boolean getTamper() {
        return tamper_;
      }

      public static final int MAIN_POWER_FIELD_NUMBER = 5;
      private boolean mainPower_ = false;
      /**
       * <code>optional bool main_power = 5;</code>
       * @return Whether the mainPower field is set.
       */
      @java.lang.Override
      public boolean hasMainPower() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bool main_power = 5;</code>
       * @return The mainPower.
       */
      @java.lang.Override
      public boolean getMainPower() {
        return mainPower_;
      }

      public static final int IGNITION_FIELD_NUMBER = 6;
      private boolean ignition_ = false;
      /**
       * <code>optional bool ignition = 6;</code>
       * @return Whether the ignition field is set.
       */
      @java.lang.Override
      public boolean hasIgnition() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bool ignition = 6;</code>
       * @return The ignition.
       */
      @java.lang.Override
      public boolean getIgnition() {
        return ignition_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeBool(1, usr1_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeBool(2, usr2_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeBool(3, motion_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          output.writeBool(4, tamper_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          output.writeBool(5, mainPower_);
        }
        if (((bitField0_ & 0x00000020) != 0)) {
          output.writeBool(6, ignition_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(1, usr1_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(2, usr2_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(3, motion_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(4, tamper_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(5, mainPower_);
        }
        if (((bitField0_ & 0x00000020) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(6, ignition_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput)) {
          return super.equals(obj);
        }
        com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput other = (com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput) obj;

        if (hasUsr1() != other.hasUsr1()) return false;
        if (hasUsr1()) {
          if (getUsr1()
              != other.getUsr1()) return false;
        }
        if (hasUsr2() != other.hasUsr2()) return false;
        if (hasUsr2()) {
          if (getUsr2()
              != other.getUsr2()) return false;
        }
        if (hasMotion() != other.hasMotion()) return false;
        if (hasMotion()) {
          if (getMotion()
              != other.getMotion()) return false;
        }
        if (hasTamper() != other.hasTamper()) return false;
        if (hasTamper()) {
          if (getTamper()
              != other.getTamper()) return false;
        }
        if (hasMainPower() != other.hasMainPower()) return false;
        if (hasMainPower()) {
          if (getMainPower()
              != other.getMainPower()) return false;
        }
        if (hasIgnition() != other.hasIgnition()) return false;
        if (hasIgnition()) {
          if (getIgnition()
              != other.getIgnition()) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasUsr1()) {
          hash = (37 * hash) + USR1_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getUsr1());
        }
        if (hasUsr2()) {
          hash = (37 * hash) + USR2_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getUsr2());
        }
        if (hasMotion()) {
          hash = (37 * hash) + MOTION_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getMotion());
        }
        if (hasTamper()) {
          hash = (37 * hash) + TAMPER_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getTamper());
        }
        if (hasMainPower()) {
          hash = (37 * hash) + MAIN_POWER_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getMainPower());
        }
        if (hasIgnition()) {
          hash = (37 * hash) + IGNITION_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getIgnition());
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * Digital Input Data
       * </pre>
       *
       * Protobuf type {@code Telemetry.DigitalInput}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Telemetry.DigitalInput)
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInputOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_DigitalInput_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_DigitalInput_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.Builder.class);
        }

        // Construct using com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          usr1_ = false;
          usr2_ = false;
          motion_ = false;
          tamper_ = false;
          mainPower_ = false;
          ignition_ = false;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_DigitalInput_descriptor;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput getDefaultInstanceForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.getDefaultInstance();
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput build() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput buildPartial() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput result = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.usr1_ = usr1_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.usr2_ = usr2_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.motion_ = motion_;
            to_bitField0_ |= 0x00000004;
          }
          if (((from_bitField0_ & 0x00000008) != 0)) {
            result.tamper_ = tamper_;
            to_bitField0_ |= 0x00000008;
          }
          if (((from_bitField0_ & 0x00000010) != 0)) {
            result.mainPower_ = mainPower_;
            to_bitField0_ |= 0x00000010;
          }
          if (((from_bitField0_ & 0x00000020) != 0)) {
            result.ignition_ = ignition_;
            to_bitField0_ |= 0x00000020;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput) {
            return mergeFrom((com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput other) {
          if (other == com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.getDefaultInstance()) return this;
          if (other.hasUsr1()) {
            setUsr1(other.getUsr1());
          }
          if (other.hasUsr2()) {
            setUsr2(other.getUsr2());
          }
          if (other.hasMotion()) {
            setMotion(other.getMotion());
          }
          if (other.hasTamper()) {
            setTamper(other.getTamper());
          }
          if (other.hasMainPower()) {
            setMainPower(other.getMainPower());
          }
          if (other.hasIgnition()) {
            setIgnition(other.getIgnition());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  usr1_ = input.readBool();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 16: {
                  usr2_ = input.readBool();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                case 24: {
                  motion_ = input.readBool();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                case 32: {
                  tamper_ = input.readBool();
                  bitField0_ |= 0x00000008;
                  break;
                } // case 32
                case 40: {
                  mainPower_ = input.readBool();
                  bitField0_ |= 0x00000010;
                  break;
                } // case 40
                case 48: {
                  ignition_ = input.readBool();
                  bitField0_ |= 0x00000020;
                  break;
                } // case 48
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private boolean usr1_ ;
        /**
         * <code>optional bool usr1 = 1;</code>
         * @return Whether the usr1 field is set.
         */
        @java.lang.Override
        public boolean hasUsr1() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional bool usr1 = 1;</code>
         * @return The usr1.
         */
        @java.lang.Override
        public boolean getUsr1() {
          return usr1_;
        }
        /**
         * <code>optional bool usr1 = 1;</code>
         * @param value The usr1 to set.
         * @return This builder for chaining.
         */
        public Builder setUsr1(boolean value) {

          usr1_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>optional bool usr1 = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearUsr1() {
          bitField0_ = (bitField0_ & ~0x00000001);
          usr1_ = false;
          onChanged();
          return this;
        }

        private boolean usr2_ ;
        /**
         * <code>optional bool usr2 = 2;</code>
         * @return Whether the usr2 field is set.
         */
        @java.lang.Override
        public boolean hasUsr2() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional bool usr2 = 2;</code>
         * @return The usr2.
         */
        @java.lang.Override
        public boolean getUsr2() {
          return usr2_;
        }
        /**
         * <code>optional bool usr2 = 2;</code>
         * @param value The usr2 to set.
         * @return This builder for chaining.
         */
        public Builder setUsr2(boolean value) {

          usr2_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>optional bool usr2 = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearUsr2() {
          bitField0_ = (bitField0_ & ~0x00000002);
          usr2_ = false;
          onChanged();
          return this;
        }

        private boolean motion_ ;
        /**
         * <code>optional bool motion = 3;</code>
         * @return Whether the motion field is set.
         */
        @java.lang.Override
        public boolean hasMotion() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <code>optional bool motion = 3;</code>
         * @return The motion.
         */
        @java.lang.Override
        public boolean getMotion() {
          return motion_;
        }
        /**
         * <code>optional bool motion = 3;</code>
         * @param value The motion to set.
         * @return This builder for chaining.
         */
        public Builder setMotion(boolean value) {

          motion_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>optional bool motion = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearMotion() {
          bitField0_ = (bitField0_ & ~0x00000004);
          motion_ = false;
          onChanged();
          return this;
        }

        private boolean tamper_ ;
        /**
         * <code>optional bool tamper = 4;</code>
         * @return Whether the tamper field is set.
         */
        @java.lang.Override
        public boolean hasTamper() {
          return ((bitField0_ & 0x00000008) != 0);
        }
        /**
         * <code>optional bool tamper = 4;</code>
         * @return The tamper.
         */
        @java.lang.Override
        public boolean getTamper() {
          return tamper_;
        }
        /**
         * <code>optional bool tamper = 4;</code>
         * @param value The tamper to set.
         * @return This builder for chaining.
         */
        public Builder setTamper(boolean value) {

          tamper_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <code>optional bool tamper = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearTamper() {
          bitField0_ = (bitField0_ & ~0x00000008);
          tamper_ = false;
          onChanged();
          return this;
        }

        private boolean mainPower_ ;
        /**
         * <code>optional bool main_power = 5;</code>
         * @return Whether the mainPower field is set.
         */
        @java.lang.Override
        public boolean hasMainPower() {
          return ((bitField0_ & 0x00000010) != 0);
        }
        /**
         * <code>optional bool main_power = 5;</code>
         * @return The mainPower.
         */
        @java.lang.Override
        public boolean getMainPower() {
          return mainPower_;
        }
        /**
         * <code>optional bool main_power = 5;</code>
         * @param value The mainPower to set.
         * @return This builder for chaining.
         */
        public Builder setMainPower(boolean value) {

          mainPower_ = value;
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <code>optional bool main_power = 5;</code>
         * @return This builder for chaining.
         */
        public Builder clearMainPower() {
          bitField0_ = (bitField0_ & ~0x00000010);
          mainPower_ = false;
          onChanged();
          return this;
        }

        private boolean ignition_ ;
        /**
         * <code>optional bool ignition = 6;</code>
         * @return Whether the ignition field is set.
         */
        @java.lang.Override
        public boolean hasIgnition() {
          return ((bitField0_ & 0x00000020) != 0);
        }
        /**
         * <code>optional bool ignition = 6;</code>
         * @return The ignition.
         */
        @java.lang.Override
        public boolean getIgnition() {
          return ignition_;
        }
        /**
         * <code>optional bool ignition = 6;</code>
         * @param value The ignition to set.
         * @return This builder for chaining.
         */
        public Builder setIgnition(boolean value) {

          ignition_ = value;
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>optional bool ignition = 6;</code>
         * @return This builder for chaining.
         */
        public Builder clearIgnition() {
          bitField0_ = (bitField0_ & ~0x00000020);
          ignition_ = false;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:Telemetry.DigitalInput)
      }

      // @@protoc_insertion_point(class_scope:Telemetry.DigitalInput)
      private static final com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput();
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<DigitalInput>
          PARSER = new com.google.protobuf.AbstractParser<DigitalInput>() {
        @java.lang.Override
        public DigitalInput parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<DigitalInput> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<DigitalInput> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface DigitalOutputOrBuilder extends
        // @@protoc_insertion_point(interface_extends:Telemetry.DigitalOutput)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional bool usr1 = 1;</code>
       * @return Whether the usr1 field is set.
       */
      boolean hasUsr1();
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return The usr1.
       */
      boolean getUsr1();

      /**
       * <code>optional bool usr2 = 2;</code>
       * @return Whether the usr2 field is set.
       */
      boolean hasUsr2();
      /**
       * <code>optional bool usr2 = 2;</code>
       * @return The usr2.
       */
      boolean getUsr2();
    }
    /**
     * <pre>
     * Digital Output Data
     * </pre>
     *
     * Protobuf type {@code Telemetry.DigitalOutput}
     */
    public static final class DigitalOutput extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:Telemetry.DigitalOutput)
        DigitalOutputOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use DigitalOutput.newBuilder() to construct.
      private DigitalOutput(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private DigitalOutput() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new DigitalOutput();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_DigitalOutput_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_DigitalOutput_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.Builder.class);
      }

      private int bitField0_;
      public static final int USR1_FIELD_NUMBER = 1;
      private boolean usr1_ = false;
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return Whether the usr1 field is set.
       */
      @java.lang.Override
      public boolean hasUsr1() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool usr1 = 1;</code>
       * @return The usr1.
       */
      @java.lang.Override
      public boolean getUsr1() {
        return usr1_;
      }

      public static final int USR2_FIELD_NUMBER = 2;
      private boolean usr2_ = false;
      /**
       * <code>optional bool usr2 = 2;</code>
       * @return Whether the usr2 field is set.
       */
      @java.lang.Override
      public boolean hasUsr2() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bool usr2 = 2;</code>
       * @return The usr2.
       */
      @java.lang.Override
      public boolean getUsr2() {
        return usr2_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeBool(1, usr1_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeBool(2, usr2_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(1, usr1_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(2, usr2_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput)) {
          return super.equals(obj);
        }
        com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput other = (com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput) obj;

        if (hasUsr1() != other.hasUsr1()) return false;
        if (hasUsr1()) {
          if (getUsr1()
              != other.getUsr1()) return false;
        }
        if (hasUsr2() != other.hasUsr2()) return false;
        if (hasUsr2()) {
          if (getUsr2()
              != other.getUsr2()) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasUsr1()) {
          hash = (37 * hash) + USR1_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getUsr1());
        }
        if (hasUsr2()) {
          hash = (37 * hash) + USR2_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getUsr2());
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * Digital Output Data
       * </pre>
       *
       * Protobuf type {@code Telemetry.DigitalOutput}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:Telemetry.DigitalOutput)
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutputOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_DigitalOutput_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_DigitalOutput_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.Builder.class);
        }

        // Construct using com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          usr1_ = false;
          usr2_ = false;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_DigitalOutput_descriptor;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput getDefaultInstanceForType() {
          return com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.getDefaultInstance();
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput build() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput buildPartial() {
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput result = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.usr1_ = usr1_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.usr2_ = usr2_;
            to_bitField0_ |= 0x00000002;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput) {
            return mergeFrom((com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput other) {
          if (other == com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.getDefaultInstance()) return this;
          if (other.hasUsr1()) {
            setUsr1(other.getUsr1());
          }
          if (other.hasUsr2()) {
            setUsr2(other.getUsr2());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  usr1_ = input.readBool();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 16: {
                  usr2_ = input.readBool();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private boolean usr1_ ;
        /**
         * <code>optional bool usr1 = 1;</code>
         * @return Whether the usr1 field is set.
         */
        @java.lang.Override
        public boolean hasUsr1() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional bool usr1 = 1;</code>
         * @return The usr1.
         */
        @java.lang.Override
        public boolean getUsr1() {
          return usr1_;
        }
        /**
         * <code>optional bool usr1 = 1;</code>
         * @param value The usr1 to set.
         * @return This builder for chaining.
         */
        public Builder setUsr1(boolean value) {

          usr1_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>optional bool usr1 = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearUsr1() {
          bitField0_ = (bitField0_ & ~0x00000001);
          usr1_ = false;
          onChanged();
          return this;
        }

        private boolean usr2_ ;
        /**
         * <code>optional bool usr2 = 2;</code>
         * @return Whether the usr2 field is set.
         */
        @java.lang.Override
        public boolean hasUsr2() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional bool usr2 = 2;</code>
         * @return The usr2.
         */
        @java.lang.Override
        public boolean getUsr2() {
          return usr2_;
        }
        /**
         * <code>optional bool usr2 = 2;</code>
         * @param value The usr2 to set.
         * @return This builder for chaining.
         */
        public Builder setUsr2(boolean value) {

          usr2_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>optional bool usr2 = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearUsr2() {
          bitField0_ = (bitField0_ & ~0x00000002);
          usr2_ = false;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:Telemetry.DigitalOutput)
      }

      // @@protoc_insertion_point(class_scope:Telemetry.DigitalOutput)
      private static final com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput();
      }

      public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<DigitalOutput>
          PARSER = new com.google.protobuf.AbstractParser<DigitalOutput>() {
        @java.lang.Override
        public DigitalOutput parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<DigitalOutput> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<DigitalOutput> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    private int bitField0_;
    public static final int METADATA_FIELD_NUMBER = 1;
    private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     * @return Whether the metadata field is set.
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     * @return The metadata.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <pre>
     * Metadata
     * </pre>
     *
     * <code>.Metadata metadata = 1;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
      return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 2;
    private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }
    /**
     * <pre>
     * Timestamp
     * </pre>
     *
     * <code>.Timestamp timestamp = 2;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
      return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
    }

    public static final int ACCELEROMETER_FIELD_NUMBER = 3;
    private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer accelerometer_;
    /**
     * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
     * @return Whether the accelerometer field is set.
     */
    @java.lang.Override
    public boolean hasAccelerometer() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
     * @return The accelerometer.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer getAccelerometer() {
      return accelerometer_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.getDefaultInstance() : accelerometer_;
    }
    /**
     * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AccelerometerOrBuilder getAccelerometerOrBuilder() {
      return accelerometer_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.getDefaultInstance() : accelerometer_;
    }

    public static final int GYROSCOPE_FIELD_NUMBER = 4;
    private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope gyroscope_;
    /**
     * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
     * @return Whether the gyroscope field is set.
     */
    @java.lang.Override
    public boolean hasGyroscope() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
     * @return The gyroscope.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope getGyroscope() {
      return gyroscope_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.getDefaultInstance() : gyroscope_;
    }
    /**
     * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GyroscopeOrBuilder getGyroscopeOrBuilder() {
      return gyroscope_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.getDefaultInstance() : gyroscope_;
    }

    public static final int GRAVITATIONAL_VECTOR_FIELD_NUMBER = 5;
    private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector gravitationalVector_;
    /**
     * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
     * @return Whether the gravitationalVector field is set.
     */
    @java.lang.Override
    public boolean hasGravitationalVector() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
     * @return The gravitationalVector.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector getGravitationalVector() {
      return gravitationalVector_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.getDefaultInstance() : gravitationalVector_;
    }
    /**
     * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVectorOrBuilder getGravitationalVectorOrBuilder() {
      return gravitationalVector_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.getDefaultInstance() : gravitationalVector_;
    }

    public static final int ANALOG_INPUT_FIELD_NUMBER = 6;
    private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput analogInput_;
    /**
     * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
     * @return Whether the analogInput field is set.
     */
    @java.lang.Override
    public boolean hasAnalogInput() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
     * @return The analogInput.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput getAnalogInput() {
      return analogInput_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.getDefaultInstance() : analogInput_;
    }
    /**
     * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInputOrBuilder getAnalogInputOrBuilder() {
      return analogInput_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.getDefaultInstance() : analogInput_;
    }

    public static final int DIGITAL_INPUT_FIELD_NUMBER = 7;
    private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput digitalInput_;
    /**
     * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
     * @return Whether the digitalInput field is set.
     */
    @java.lang.Override
    public boolean hasDigitalInput() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
     * @return The digitalInput.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput getDigitalInput() {
      return digitalInput_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.getDefaultInstance() : digitalInput_;
    }
    /**
     * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInputOrBuilder getDigitalInputOrBuilder() {
      return digitalInput_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.getDefaultInstance() : digitalInput_;
    }

    public static final int DIGITAL_OUTPUT_FIELD_NUMBER = 8;
    private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput digitalOutput_;
    /**
     * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
     * @return Whether the digitalOutput field is set.
     */
    @java.lang.Override
    public boolean hasDigitalOutput() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
     * @return The digitalOutput.
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput getDigitalOutput() {
      return digitalOutput_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.getDefaultInstance() : digitalOutput_;
    }
    /**
     * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
     */
    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutputOrBuilder getDigitalOutputOrBuilder() {
      return digitalOutput_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.getDefaultInstance() : digitalOutput_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getMetadata());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getTimestamp());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getAccelerometer());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getGyroscope());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(5, getGravitationalVector());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeMessage(6, getAnalogInput());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(7, getDigitalInput());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeMessage(8, getDigitalOutput());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getMetadata());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getTimestamp());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getAccelerometer());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getGyroscope());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getGravitationalVector());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getAnalogInput());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getDigitalInput());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getDigitalOutput());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry other = (com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry) obj;

      if (hasMetadata() != other.hasMetadata()) return false;
      if (hasMetadata()) {
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (!getTimestamp()
            .equals(other.getTimestamp())) return false;
      }
      if (hasAccelerometer() != other.hasAccelerometer()) return false;
      if (hasAccelerometer()) {
        if (!getAccelerometer()
            .equals(other.getAccelerometer())) return false;
      }
      if (hasGyroscope() != other.hasGyroscope()) return false;
      if (hasGyroscope()) {
        if (!getGyroscope()
            .equals(other.getGyroscope())) return false;
      }
      if (hasGravitationalVector() != other.hasGravitationalVector()) return false;
      if (hasGravitationalVector()) {
        if (!getGravitationalVector()
            .equals(other.getGravitationalVector())) return false;
      }
      if (hasAnalogInput() != other.hasAnalogInput()) return false;
      if (hasAnalogInput()) {
        if (!getAnalogInput()
            .equals(other.getAnalogInput())) return false;
      }
      if (hasDigitalInput() != other.hasDigitalInput()) return false;
      if (hasDigitalInput()) {
        if (!getDigitalInput()
            .equals(other.getDigitalInput())) return false;
      }
      if (hasDigitalOutput() != other.hasDigitalOutput()) return false;
      if (hasDigitalOutput()) {
        if (!getDigitalOutput()
            .equals(other.getDigitalOutput())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMetadata()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getTimestamp().hashCode();
      }
      if (hasAccelerometer()) {
        hash = (37 * hash) + ACCELEROMETER_FIELD_NUMBER;
        hash = (53 * hash) + getAccelerometer().hashCode();
      }
      if (hasGyroscope()) {
        hash = (37 * hash) + GYROSCOPE_FIELD_NUMBER;
        hash = (53 * hash) + getGyroscope().hashCode();
      }
      if (hasGravitationalVector()) {
        hash = (37 * hash) + GRAVITATIONAL_VECTOR_FIELD_NUMBER;
        hash = (53 * hash) + getGravitationalVector().hashCode();
      }
      if (hasAnalogInput()) {
        hash = (37 * hash) + ANALOG_INPUT_FIELD_NUMBER;
        hash = (53 * hash) + getAnalogInput().hashCode();
      }
      if (hasDigitalInput()) {
        hash = (37 * hash) + DIGITAL_INPUT_FIELD_NUMBER;
        hash = (53 * hash) + getDigitalInput().hashCode();
      }
      if (hasDigitalOutput()) {
        hash = (37 * hash) + DIGITAL_OUTPUT_FIELD_NUMBER;
        hash = (53 * hash) + getDigitalOutput().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Telemetry}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Telemetry)
        com.nichesolv.nds.model.proto.model.TelemetryProto.TelemetryOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.class, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMetadataFieldBuilder();
          getTimestampFieldBuilder();
          getAccelerometerFieldBuilder();
          getGyroscopeFieldBuilder();
          getGravitationalVectorFieldBuilder();
          getAnalogInputFieldBuilder();
          getDigitalInputFieldBuilder();
          getDigitalOutputFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        accelerometer_ = null;
        if (accelerometerBuilder_ != null) {
          accelerometerBuilder_.dispose();
          accelerometerBuilder_ = null;
        }
        gyroscope_ = null;
        if (gyroscopeBuilder_ != null) {
          gyroscopeBuilder_.dispose();
          gyroscopeBuilder_ = null;
        }
        gravitationalVector_ = null;
        if (gravitationalVectorBuilder_ != null) {
          gravitationalVectorBuilder_.dispose();
          gravitationalVectorBuilder_ = null;
        }
        analogInput_ = null;
        if (analogInputBuilder_ != null) {
          analogInputBuilder_.dispose();
          analogInputBuilder_ = null;
        }
        digitalInput_ = null;
        if (digitalInputBuilder_ != null) {
          digitalInputBuilder_.dispose();
          digitalInputBuilder_ = null;
        }
        digitalOutput_ = null;
        if (digitalOutputBuilder_ != null) {
          digitalOutputBuilder_.dispose();
          digitalOutputBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.internal_static_Telemetry_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry build() {
        com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry buildPartial() {
        com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry result = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.metadata_ = metadataBuilder_ == null
              ? metadata_
              : metadataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.timestamp_ = timestampBuilder_ == null
              ? timestamp_
              : timestampBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.accelerometer_ = accelerometerBuilder_ == null
              ? accelerometer_
              : accelerometerBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.gyroscope_ = gyroscopeBuilder_ == null
              ? gyroscope_
              : gyroscopeBuilder_.build();
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.gravitationalVector_ = gravitationalVectorBuilder_ == null
              ? gravitationalVector_
              : gravitationalVectorBuilder_.build();
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.analogInput_ = analogInputBuilder_ == null
              ? analogInput_
              : analogInputBuilder_.build();
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.digitalInput_ = digitalInputBuilder_ == null
              ? digitalInput_
              : digitalInputBuilder_.build();
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.digitalOutput_ = digitalOutputBuilder_ == null
              ? digitalOutput_
              : digitalOutputBuilder_.build();
          to_bitField0_ |= 0x00000080;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry other) {
        if (other == com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.getDefaultInstance()) return this;
        if (other.hasMetadata()) {
          mergeMetadata(other.getMetadata());
        }
        if (other.hasTimestamp()) {
          mergeTimestamp(other.getTimestamp());
        }
        if (other.hasAccelerometer()) {
          mergeAccelerometer(other.getAccelerometer());
        }
        if (other.hasGyroscope()) {
          mergeGyroscope(other.getGyroscope());
        }
        if (other.hasGravitationalVector()) {
          mergeGravitationalVector(other.getGravitationalVector());
        }
        if (other.hasAnalogInput()) {
          mergeAnalogInput(other.getAnalogInput());
        }
        if (other.hasDigitalInput()) {
          mergeDigitalInput(other.getDigitalInput());
        }
        if (other.hasDigitalOutput()) {
          mergeDigitalOutput(other.getDigitalOutput());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getMetadataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getTimestampFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getAccelerometerFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                input.readMessage(
                    getGyroscopeFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                input.readMessage(
                    getGravitationalVectorFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                input.readMessage(
                    getAnalogInputFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 58: {
                input.readMessage(
                    getDigitalInputFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                input.readMessage(
                    getDigitalOutputFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.nichesolv.nds.model.proto.model.MetadataProto.Metadata metadata_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> metadataBuilder_;
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       * @return Whether the metadata field is set.
       */
      public boolean hasMetadata() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       * @return The metadata.
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata getMetadata() {
        if (metadataBuilder_ == null) {
          return metadata_ == null ? com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        } else {
          return metadataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public Builder setMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metadata_ = value;
        } else {
          metadataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public Builder setMetadata(
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder builderForValue) {
        if (metadataBuilder_ == null) {
          metadata_ = builderForValue.build();
        } else {
          metadataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public Builder mergeMetadata(com.nichesolv.nds.model.proto.model.MetadataProto.Metadata value) {
        if (metadataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            metadata_ != null &&
            metadata_ != com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance()) {
            getMetadataBuilder().mergeFrom(value);
          } else {
            metadata_ = value;
          }
        } else {
          metadataBuilder_.mergeFrom(value);
        }
        if (metadata_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public Builder clearMetadata() {
        bitField0_ = (bitField0_ & ~0x00000001);
        metadata_ = null;
        if (metadataBuilder_ != null) {
          metadataBuilder_.dispose();
          metadataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder getMetadataBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getMetadataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      public com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder getMetadataOrBuilder() {
        if (metadataBuilder_ != null) {
          return metadataBuilder_.getMessageOrBuilder();
        } else {
          return metadata_ == null ?
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.getDefaultInstance() : metadata_;
        }
      }
      /**
       * <pre>
       * Metadata
       * </pre>
       *
       * <code>.Metadata metadata = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder> 
          getMetadataFieldBuilder() {
        if (metadataBuilder_ == null) {
          metadataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.MetadataProto.Metadata, com.nichesolv.nds.model.proto.model.MetadataProto.Metadata.Builder, com.nichesolv.nds.model.proto.model.MetadataProto.MetadataOrBuilder>(
                  getMetadata(),
                  getParentForChildren(),
                  isClean());
          metadata_ = null;
        }
        return metadataBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp timestamp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> timestampBuilder_;
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       * @return Whether the timestamp field is set.
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       * @return The timestamp.
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getTimestamp() {
        if (timestampBuilder_ == null) {
          return timestamp_ == null ? com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        } else {
          return timestampBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder setTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          timestamp_ = value;
        } else {
          timestampBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder setTimestamp(
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder builderForValue) {
        if (timestampBuilder_ == null) {
          timestamp_ = builderForValue.build();
        } else {
          timestampBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder mergeTimestamp(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp value) {
        if (timestampBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            timestamp_ != null &&
            timestamp_ != com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance()) {
            getTimestampBuilder().mergeFrom(value);
          } else {
            timestamp_ = value;
          }
        } else {
          timestampBuilder_.mergeFrom(value);
        }
        if (timestamp_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        timestamp_ = null;
        if (timestampBuilder_ != null) {
          timestampBuilder_.dispose();
          timestampBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder getTimestampBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getTimestampFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      public com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder getTimestampOrBuilder() {
        if (timestampBuilder_ != null) {
          return timestampBuilder_.getMessageOrBuilder();
        } else {
          return timestamp_ == null ?
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance() : timestamp_;
        }
      }
      /**
       * <pre>
       * Timestamp
       * </pre>
       *
       * <code>.Timestamp timestamp = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder> 
          getTimestampFieldBuilder() {
        if (timestampBuilder_ == null) {
          timestampBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder, com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder>(
                  getTimestamp(),
                  getParentForChildren(),
                  isClean());
          timestamp_ = null;
        }
        return timestampBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer accelerometer_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AccelerometerOrBuilder> accelerometerBuilder_;
      /**
       * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
       * @return Whether the accelerometer field is set.
       */
      public boolean hasAccelerometer() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
       * @return The accelerometer.
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer getAccelerometer() {
        if (accelerometerBuilder_ == null) {
          return accelerometer_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.getDefaultInstance() : accelerometer_;
        } else {
          return accelerometerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
       */
      public Builder setAccelerometer(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer value) {
        if (accelerometerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          accelerometer_ = value;
        } else {
          accelerometerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
       */
      public Builder setAccelerometer(
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.Builder builderForValue) {
        if (accelerometerBuilder_ == null) {
          accelerometer_ = builderForValue.build();
        } else {
          accelerometerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
       */
      public Builder mergeAccelerometer(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer value) {
        if (accelerometerBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            accelerometer_ != null &&
            accelerometer_ != com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.getDefaultInstance()) {
            getAccelerometerBuilder().mergeFrom(value);
          } else {
            accelerometer_ = value;
          }
        } else {
          accelerometerBuilder_.mergeFrom(value);
        }
        if (accelerometer_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
       */
      public Builder clearAccelerometer() {
        bitField0_ = (bitField0_ & ~0x00000004);
        accelerometer_ = null;
        if (accelerometerBuilder_ != null) {
          accelerometerBuilder_.dispose();
          accelerometerBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.Builder getAccelerometerBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getAccelerometerFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AccelerometerOrBuilder getAccelerometerOrBuilder() {
        if (accelerometerBuilder_ != null) {
          return accelerometerBuilder_.getMessageOrBuilder();
        } else {
          return accelerometer_ == null ?
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.getDefaultInstance() : accelerometer_;
        }
      }
      /**
       * <code>optional .Telemetry.Accelerometer accelerometer = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AccelerometerOrBuilder> 
          getAccelerometerFieldBuilder() {
        if (accelerometerBuilder_ == null) {
          accelerometerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Accelerometer.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AccelerometerOrBuilder>(
                  getAccelerometer(),
                  getParentForChildren(),
                  isClean());
          accelerometer_ = null;
        }
        return accelerometerBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope gyroscope_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GyroscopeOrBuilder> gyroscopeBuilder_;
      /**
       * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
       * @return Whether the gyroscope field is set.
       */
      public boolean hasGyroscope() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
       * @return The gyroscope.
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope getGyroscope() {
        if (gyroscopeBuilder_ == null) {
          return gyroscope_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.getDefaultInstance() : gyroscope_;
        } else {
          return gyroscopeBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
       */
      public Builder setGyroscope(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope value) {
        if (gyroscopeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          gyroscope_ = value;
        } else {
          gyroscopeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
       */
      public Builder setGyroscope(
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.Builder builderForValue) {
        if (gyroscopeBuilder_ == null) {
          gyroscope_ = builderForValue.build();
        } else {
          gyroscopeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
       */
      public Builder mergeGyroscope(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope value) {
        if (gyroscopeBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            gyroscope_ != null &&
            gyroscope_ != com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.getDefaultInstance()) {
            getGyroscopeBuilder().mergeFrom(value);
          } else {
            gyroscope_ = value;
          }
        } else {
          gyroscopeBuilder_.mergeFrom(value);
        }
        if (gyroscope_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
       */
      public Builder clearGyroscope() {
        bitField0_ = (bitField0_ & ~0x00000008);
        gyroscope_ = null;
        if (gyroscopeBuilder_ != null) {
          gyroscopeBuilder_.dispose();
          gyroscopeBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.Builder getGyroscopeBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getGyroscopeFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GyroscopeOrBuilder getGyroscopeOrBuilder() {
        if (gyroscopeBuilder_ != null) {
          return gyroscopeBuilder_.getMessageOrBuilder();
        } else {
          return gyroscope_ == null ?
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.getDefaultInstance() : gyroscope_;
        }
      }
      /**
       * <code>optional .Telemetry.Gyroscope gyroscope = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GyroscopeOrBuilder> 
          getGyroscopeFieldBuilder() {
        if (gyroscopeBuilder_ == null) {
          gyroscopeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.Gyroscope.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GyroscopeOrBuilder>(
                  getGyroscope(),
                  getParentForChildren(),
                  isClean());
          gyroscope_ = null;
        }
        return gyroscopeBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector gravitationalVector_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVectorOrBuilder> gravitationalVectorBuilder_;
      /**
       * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
       * @return Whether the gravitationalVector field is set.
       */
      public boolean hasGravitationalVector() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
       * @return The gravitationalVector.
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector getGravitationalVector() {
        if (gravitationalVectorBuilder_ == null) {
          return gravitationalVector_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.getDefaultInstance() : gravitationalVector_;
        } else {
          return gravitationalVectorBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
       */
      public Builder setGravitationalVector(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector value) {
        if (gravitationalVectorBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          gravitationalVector_ = value;
        } else {
          gravitationalVectorBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
       */
      public Builder setGravitationalVector(
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.Builder builderForValue) {
        if (gravitationalVectorBuilder_ == null) {
          gravitationalVector_ = builderForValue.build();
        } else {
          gravitationalVectorBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
       */
      public Builder mergeGravitationalVector(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector value) {
        if (gravitationalVectorBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
            gravitationalVector_ != null &&
            gravitationalVector_ != com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.getDefaultInstance()) {
            getGravitationalVectorBuilder().mergeFrom(value);
          } else {
            gravitationalVector_ = value;
          }
        } else {
          gravitationalVectorBuilder_.mergeFrom(value);
        }
        if (gravitationalVector_ != null) {
          bitField0_ |= 0x00000010;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
       */
      public Builder clearGravitationalVector() {
        bitField0_ = (bitField0_ & ~0x00000010);
        gravitationalVector_ = null;
        if (gravitationalVectorBuilder_ != null) {
          gravitationalVectorBuilder_.dispose();
          gravitationalVectorBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.Builder getGravitationalVectorBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getGravitationalVectorFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVectorOrBuilder getGravitationalVectorOrBuilder() {
        if (gravitationalVectorBuilder_ != null) {
          return gravitationalVectorBuilder_.getMessageOrBuilder();
        } else {
          return gravitationalVector_ == null ?
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.getDefaultInstance() : gravitationalVector_;
        }
      }
      /**
       * <code>optional .Telemetry.GravitationalVector gravitational_vector = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVectorOrBuilder> 
          getGravitationalVectorFieldBuilder() {
        if (gravitationalVectorBuilder_ == null) {
          gravitationalVectorBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVector.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.GravitationalVectorOrBuilder>(
                  getGravitationalVector(),
                  getParentForChildren(),
                  isClean());
          gravitationalVector_ = null;
        }
        return gravitationalVectorBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput analogInput_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInputOrBuilder> analogInputBuilder_;
      /**
       * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
       * @return Whether the analogInput field is set.
       */
      public boolean hasAnalogInput() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
       * @return The analogInput.
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput getAnalogInput() {
        if (analogInputBuilder_ == null) {
          return analogInput_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.getDefaultInstance() : analogInput_;
        } else {
          return analogInputBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
       */
      public Builder setAnalogInput(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput value) {
        if (analogInputBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          analogInput_ = value;
        } else {
          analogInputBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
       */
      public Builder setAnalogInput(
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.Builder builderForValue) {
        if (analogInputBuilder_ == null) {
          analogInput_ = builderForValue.build();
        } else {
          analogInputBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
       */
      public Builder mergeAnalogInput(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput value) {
        if (analogInputBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
            analogInput_ != null &&
            analogInput_ != com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.getDefaultInstance()) {
            getAnalogInputBuilder().mergeFrom(value);
          } else {
            analogInput_ = value;
          }
        } else {
          analogInputBuilder_.mergeFrom(value);
        }
        if (analogInput_ != null) {
          bitField0_ |= 0x00000020;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
       */
      public Builder clearAnalogInput() {
        bitField0_ = (bitField0_ & ~0x00000020);
        analogInput_ = null;
        if (analogInputBuilder_ != null) {
          analogInputBuilder_.dispose();
          analogInputBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.Builder getAnalogInputBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getAnalogInputFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInputOrBuilder getAnalogInputOrBuilder() {
        if (analogInputBuilder_ != null) {
          return analogInputBuilder_.getMessageOrBuilder();
        } else {
          return analogInput_ == null ?
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.getDefaultInstance() : analogInput_;
        }
      }
      /**
       * <code>optional .Telemetry.AnalogInput analog_input = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInputOrBuilder> 
          getAnalogInputFieldBuilder() {
        if (analogInputBuilder_ == null) {
          analogInputBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInput.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.AnalogInputOrBuilder>(
                  getAnalogInput(),
                  getParentForChildren(),
                  isClean());
          analogInput_ = null;
        }
        return analogInputBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput digitalInput_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInputOrBuilder> digitalInputBuilder_;
      /**
       * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
       * @return Whether the digitalInput field is set.
       */
      public boolean hasDigitalInput() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
       * @return The digitalInput.
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput getDigitalInput() {
        if (digitalInputBuilder_ == null) {
          return digitalInput_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.getDefaultInstance() : digitalInput_;
        } else {
          return digitalInputBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
       */
      public Builder setDigitalInput(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput value) {
        if (digitalInputBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          digitalInput_ = value;
        } else {
          digitalInputBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
       */
      public Builder setDigitalInput(
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.Builder builderForValue) {
        if (digitalInputBuilder_ == null) {
          digitalInput_ = builderForValue.build();
        } else {
          digitalInputBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
       */
      public Builder mergeDigitalInput(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput value) {
        if (digitalInputBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
            digitalInput_ != null &&
            digitalInput_ != com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.getDefaultInstance()) {
            getDigitalInputBuilder().mergeFrom(value);
          } else {
            digitalInput_ = value;
          }
        } else {
          digitalInputBuilder_.mergeFrom(value);
        }
        if (digitalInput_ != null) {
          bitField0_ |= 0x00000040;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
       */
      public Builder clearDigitalInput() {
        bitField0_ = (bitField0_ & ~0x00000040);
        digitalInput_ = null;
        if (digitalInputBuilder_ != null) {
          digitalInputBuilder_.dispose();
          digitalInputBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.Builder getDigitalInputBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getDigitalInputFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInputOrBuilder getDigitalInputOrBuilder() {
        if (digitalInputBuilder_ != null) {
          return digitalInputBuilder_.getMessageOrBuilder();
        } else {
          return digitalInput_ == null ?
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.getDefaultInstance() : digitalInput_;
        }
      }
      /**
       * <code>optional .Telemetry.DigitalInput digital_input = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInputOrBuilder> 
          getDigitalInputFieldBuilder() {
        if (digitalInputBuilder_ == null) {
          digitalInputBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInput.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalInputOrBuilder>(
                  getDigitalInput(),
                  getParentForChildren(),
                  isClean());
          digitalInput_ = null;
        }
        return digitalInputBuilder_;
      }

      private com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput digitalOutput_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutputOrBuilder> digitalOutputBuilder_;
      /**
       * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
       * @return Whether the digitalOutput field is set.
       */
      public boolean hasDigitalOutput() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
       * @return The digitalOutput.
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput getDigitalOutput() {
        if (digitalOutputBuilder_ == null) {
          return digitalOutput_ == null ? com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.getDefaultInstance() : digitalOutput_;
        } else {
          return digitalOutputBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
       */
      public Builder setDigitalOutput(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput value) {
        if (digitalOutputBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          digitalOutput_ = value;
        } else {
          digitalOutputBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
       */
      public Builder setDigitalOutput(
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.Builder builderForValue) {
        if (digitalOutputBuilder_ == null) {
          digitalOutput_ = builderForValue.build();
        } else {
          digitalOutputBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
       */
      public Builder mergeDigitalOutput(com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput value) {
        if (digitalOutputBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0) &&
            digitalOutput_ != null &&
            digitalOutput_ != com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.getDefaultInstance()) {
            getDigitalOutputBuilder().mergeFrom(value);
          } else {
            digitalOutput_ = value;
          }
        } else {
          digitalOutputBuilder_.mergeFrom(value);
        }
        if (digitalOutput_ != null) {
          bitField0_ |= 0x00000080;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
       */
      public Builder clearDigitalOutput() {
        bitField0_ = (bitField0_ & ~0x00000080);
        digitalOutput_ = null;
        if (digitalOutputBuilder_ != null) {
          digitalOutputBuilder_.dispose();
          digitalOutputBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.Builder getDigitalOutputBuilder() {
        bitField0_ |= 0x00000080;
        onChanged();
        return getDigitalOutputFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
       */
      public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutputOrBuilder getDigitalOutputOrBuilder() {
        if (digitalOutputBuilder_ != null) {
          return digitalOutputBuilder_.getMessageOrBuilder();
        } else {
          return digitalOutput_ == null ?
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.getDefaultInstance() : digitalOutput_;
        }
      }
      /**
       * <code>optional .Telemetry.DigitalOutput digital_output = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutputOrBuilder> 
          getDigitalOutputFieldBuilder() {
        if (digitalOutputBuilder_ == null) {
          digitalOutputBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutput.Builder, com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry.DigitalOutputOrBuilder>(
                  getDigitalOutput(),
                  getParentForChildren(),
                  isClean());
          digitalOutput_ = null;
        }
        return digitalOutputBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Telemetry)
    }

    // @@protoc_insertion_point(class_scope:Telemetry)
    private static final com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry();
    }

    public static com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Telemetry>
        PARSER = new com.google.protobuf.AbstractParser<Telemetry>() {
      @java.lang.Override
      public Telemetry parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Telemetry> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Telemetry> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TelemetryProto.Telemetry getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Telemetry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Telemetry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Telemetry_Accelerometer_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Telemetry_Accelerometer_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Telemetry_Gyroscope_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Telemetry_Gyroscope_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Telemetry_GravitationalVector_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Telemetry_GravitationalVector_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Telemetry_AnalogInput_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Telemetry_AnalogInput_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Telemetry_DigitalInput_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Telemetry_DigitalInput_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Telemetry_DigitalOutput_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Telemetry_DigitalOutput_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n-com/nichesolv/nds/model/proto/telemetr" +
      "y.proto\032-com/nichesolv/nds/model/proto/t" +
      "imestamp.proto\032,com/nichesolv/nds/model/" +
      "proto/metadata.proto\"\375\t\n\tTelemetry\022\033\n\010me" +
      "tadata\030\001 \001(\0132\t.Metadata\022\035\n\ttimestamp\030\002 \001" +
      "(\0132\n.Timestamp\0224\n\raccelerometer\030\003 \001(\0132\030." +
      "Telemetry.AccelerometerH\000\210\001\001\022,\n\tgyroscop" +
      "e\030\004 \001(\0132\024.Telemetry.GyroscopeH\001\210\001\001\022A\n\024gr" +
      "avitational_vector\030\005 \001(\0132\036.Telemetry.Gra" +
      "vitationalVectorH\002\210\001\001\0221\n\014analog_input\030\006 " +
      "\001(\0132\026.Telemetry.AnalogInputH\003\210\001\001\0223\n\rdigi" +
      "tal_input\030\007 \001(\0132\027.Telemetry.DigitalInput" +
      "H\004\210\001\001\0225\n\016digital_output\030\010 \001(\0132\030.Telemetr" +
      "y.DigitalOutputH\005\210\001\001\032Q\n\rAccelerometer\022\016\n" +
      "\001x\030\001 \001(\021H\000\210\001\001\022\016\n\001y\030\002 \001(\021H\001\210\001\001\022\016\n\001z\030\003 \001(\021" +
      "H\002\210\001\001B\004\n\002_xB\004\n\002_yB\004\n\002_z\032M\n\tGyroscope\022\016\n\001" +
      "x\030\001 \001(\021H\000\210\001\001\022\016\n\001y\030\002 \001(\021H\001\210\001\001\022\016\n\001z\030\003 \001(\021H" +
      "\002\210\001\001B\004\n\002_xB\004\n\002_yB\004\n\002_z\032W\n\023GravitationalV" +
      "ector\022\016\n\001x\030\001 \001(\002H\000\210\001\001\022\016\n\001y\030\002 \001(\002H\001\210\001\001\022\016\n" +
      "\001z\030\003 \001(\002H\002\210\001\001B\004\n\002_xB\004\n\002_yB\004\n\002_z\032\345\001\n\013Anal" +
      "ogInput\022\021\n\004temp\030\001 \001(\021H\000\210\001\001\022\020\n\003vin\030\002 \001(\rH" +
      "\001\210\001\001\022\021\n\004vsys\030\003 \001(\rH\002\210\001\001\022\022\n\005vbuck\030\004 \001(\rH\003" +
      "\210\001\001\022\023\n\006vusr_1\030\005 \001(\rH\004\210\001\001\022\023\n\006vusr_2\030\006 \001(\r" +
      "H\005\210\001\001\022\027\n\nlean_angle\030\007 \001(\rH\006\210\001\001B\007\n\005_tempB" +
      "\006\n\004_vinB\007\n\005_vsysB\010\n\006_vbuckB\t\n\007_vusr_1B\t\n" +
      "\007_vusr_2B\r\n\013_lean_angle\032\322\001\n\014DigitalInput" +
      "\022\021\n\004usr1\030\001 \001(\010H\000\210\001\001\022\021\n\004usr2\030\002 \001(\010H\001\210\001\001\022\023" +
      "\n\006motion\030\003 \001(\010H\002\210\001\001\022\023\n\006tamper\030\004 \001(\010H\003\210\001\001" +
      "\022\027\n\nmain_power\030\005 \001(\010H\004\210\001\001\022\025\n\010ignition\030\006 " +
      "\001(\010H\005\210\001\001B\007\n\005_usr1B\007\n\005_usr2B\t\n\007_motionB\t\n" +
      "\007_tamperB\r\n\013_main_powerB\013\n\t_ignition\032G\n\r" +
      "DigitalOutput\022\021\n\004usr1\030\001 \001(\010H\000\210\001\001\022\021\n\004usr2" +
      "\030\002 \001(\010H\001\210\001\001B\007\n\005_usr1B\007\n\005_usr2B\020\n\016_accele" +
      "rometerB\014\n\n_gyroscopeB\027\n\025_gravitational_" +
      "vectorB\017\n\r_analog_inputB\020\n\016_digital_inpu" +
      "tB\021\n\017_digital_outputB5\n#com.nichesolv.nd" +
      "s.model.proto.modelB\016TelemetryProtob\006pro" +
      "to3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor(),
          com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor(),
        });
    internal_static_Telemetry_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Telemetry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Telemetry_descriptor,
        new java.lang.String[] { "Metadata", "Timestamp", "Accelerometer", "Gyroscope", "GravitationalVector", "AnalogInput", "DigitalInput", "DigitalOutput", });
    internal_static_Telemetry_Accelerometer_descriptor =
      internal_static_Telemetry_descriptor.getNestedTypes().get(0);
    internal_static_Telemetry_Accelerometer_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Telemetry_Accelerometer_descriptor,
        new java.lang.String[] { "X", "Y", "Z", });
    internal_static_Telemetry_Gyroscope_descriptor =
      internal_static_Telemetry_descriptor.getNestedTypes().get(1);
    internal_static_Telemetry_Gyroscope_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Telemetry_Gyroscope_descriptor,
        new java.lang.String[] { "X", "Y", "Z", });
    internal_static_Telemetry_GravitationalVector_descriptor =
      internal_static_Telemetry_descriptor.getNestedTypes().get(2);
    internal_static_Telemetry_GravitationalVector_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Telemetry_GravitationalVector_descriptor,
        new java.lang.String[] { "X", "Y", "Z", });
    internal_static_Telemetry_AnalogInput_descriptor =
      internal_static_Telemetry_descriptor.getNestedTypes().get(3);
    internal_static_Telemetry_AnalogInput_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Telemetry_AnalogInput_descriptor,
        new java.lang.String[] { "Temp", "Vin", "Vsys", "Vbuck", "Vusr1", "Vusr2", "LeanAngle", });
    internal_static_Telemetry_DigitalInput_descriptor =
      internal_static_Telemetry_descriptor.getNestedTypes().get(4);
    internal_static_Telemetry_DigitalInput_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Telemetry_DigitalInput_descriptor,
        new java.lang.String[] { "Usr1", "Usr2", "Motion", "Tamper", "MainPower", "Ignition", });
    internal_static_Telemetry_DigitalOutput_descriptor =
      internal_static_Telemetry_descriptor.getNestedTypes().get(5);
    internal_static_Telemetry_DigitalOutput_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Telemetry_DigitalOutput_descriptor,
        new java.lang.String[] { "Usr1", "Usr2", });
    com.nichesolv.nds.model.proto.model.TimestampProto.getDescriptor();
    com.nichesolv.nds.model.proto.model.MetadataProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
