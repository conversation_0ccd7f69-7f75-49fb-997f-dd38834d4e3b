// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/timestamp.proto

package com.nichesolv.nds.model.proto.model;

public final class TimestampProto {
  private TimestampProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TimestampOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Timestamp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * Event timestamp. This timestamp is derived from the event
     * and it represents the actual timestamp at which the event happened.
     * </pre>
     *
     * <code>uint32 observed_timestamp = 1;</code>
     * @return The observedTimestamp.
     */
    int getObservedTimestamp();

    /**
     * <pre>
     * Ingestion timestamp is something that is
     * populated when the packet is received at the server side.l
     * </pre>
     *
     * <code>uint64 ingestion_timestamp = 2;</code>
     * @return The ingestionTimestamp.
     */
    long getIngestionTimestamp();
  }
  /**
   * <pre>
   * This class represents timestamp, contains event and ingestion timestamps.
   * Notice that none of the fields are optional in Timestamp message format, what this means is both observed_timestamp and
   * ingestion_timestamp are required fields and the pipeline will report an error if these fields were to be empty or null or without a values.
   * </pre>
   *
   * Protobuf type {@code Timestamp}
   */
  public static final class Timestamp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Timestamp)
      TimestampOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Timestamp.newBuilder() to construct.
    private Timestamp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Timestamp() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Timestamp();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.nichesolv.nds.model.proto.model.TimestampProto.internal_static_Timestamp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.nichesolv.nds.model.proto.model.TimestampProto.internal_static_Timestamp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.class, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder.class);
    }

    public static final int OBSERVED_TIMESTAMP_FIELD_NUMBER = 1;
    private int observedTimestamp_ = 0;
    /**
     * <pre>
     * Event timestamp. This timestamp is derived from the event
     * and it represents the actual timestamp at which the event happened.
     * </pre>
     *
     * <code>uint32 observed_timestamp = 1;</code>
     * @return The observedTimestamp.
     */
    @java.lang.Override
    public int getObservedTimestamp() {
      return observedTimestamp_;
    }

    public static final int INGESTION_TIMESTAMP_FIELD_NUMBER = 2;
    private long ingestionTimestamp_ = 0L;
    /**
     * <pre>
     * Ingestion timestamp is something that is
     * populated when the packet is received at the server side.l
     * </pre>
     *
     * <code>uint64 ingestion_timestamp = 2;</code>
     * @return The ingestionTimestamp.
     */
    @java.lang.Override
    public long getIngestionTimestamp() {
      return ingestionTimestamp_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (observedTimestamp_ != 0) {
        output.writeUInt32(1, observedTimestamp_);
      }
      if (ingestionTimestamp_ != 0L) {
        output.writeUInt64(2, ingestionTimestamp_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (observedTimestamp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, observedTimestamp_);
      }
      if (ingestionTimestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, ingestionTimestamp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp)) {
        return super.equals(obj);
      }
      com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp other = (com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp) obj;

      if (getObservedTimestamp()
          != other.getObservedTimestamp()) return false;
      if (getIngestionTimestamp()
          != other.getIngestionTimestamp()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + OBSERVED_TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + getObservedTimestamp();
      hash = (37 * hash) + INGESTION_TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getIngestionTimestamp());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * This class represents timestamp, contains event and ingestion timestamps.
     * Notice that none of the fields are optional in Timestamp message format, what this means is both observed_timestamp and
     * ingestion_timestamp are required fields and the pipeline will report an error if these fields were to be empty or null or without a values.
     * </pre>
     *
     * Protobuf type {@code Timestamp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Timestamp)
        com.nichesolv.nds.model.proto.model.TimestampProto.TimestampOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.nichesolv.nds.model.proto.model.TimestampProto.internal_static_Timestamp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.nichesolv.nds.model.proto.model.TimestampProto.internal_static_Timestamp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.class, com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        observedTimestamp_ = 0;
        ingestionTimestamp_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.nichesolv.nds.model.proto.model.TimestampProto.internal_static_Timestamp_descriptor;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getDefaultInstanceForType() {
        return com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance();
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp build() {
        com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp buildPartial() {
        com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp result = new com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.observedTimestamp_ = observedTimestamp_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.ingestionTimestamp_ = ingestionTimestamp_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp) {
          return mergeFrom((com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp other) {
        if (other == com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp.getDefaultInstance()) return this;
        if (other.getObservedTimestamp() != 0) {
          setObservedTimestamp(other.getObservedTimestamp());
        }
        if (other.getIngestionTimestamp() != 0L) {
          setIngestionTimestamp(other.getIngestionTimestamp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                observedTimestamp_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                ingestionTimestamp_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int observedTimestamp_ ;
      /**
       * <pre>
       * Event timestamp. This timestamp is derived from the event
       * and it represents the actual timestamp at which the event happened.
       * </pre>
       *
       * <code>uint32 observed_timestamp = 1;</code>
       * @return The observedTimestamp.
       */
      @java.lang.Override
      public int getObservedTimestamp() {
        return observedTimestamp_;
      }
      /**
       * <pre>
       * Event timestamp. This timestamp is derived from the event
       * and it represents the actual timestamp at which the event happened.
       * </pre>
       *
       * <code>uint32 observed_timestamp = 1;</code>
       * @param value The observedTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setObservedTimestamp(int value) {

        observedTimestamp_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Event timestamp. This timestamp is derived from the event
       * and it represents the actual timestamp at which the event happened.
       * </pre>
       *
       * <code>uint32 observed_timestamp = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearObservedTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        observedTimestamp_ = 0;
        onChanged();
        return this;
      }

      private long ingestionTimestamp_ ;
      /**
       * <pre>
       * Ingestion timestamp is something that is
       * populated when the packet is received at the server side.l
       * </pre>
       *
       * <code>uint64 ingestion_timestamp = 2;</code>
       * @return The ingestionTimestamp.
       */
      @java.lang.Override
      public long getIngestionTimestamp() {
        return ingestionTimestamp_;
      }
      /**
       * <pre>
       * Ingestion timestamp is something that is
       * populated when the packet is received at the server side.l
       * </pre>
       *
       * <code>uint64 ingestion_timestamp = 2;</code>
       * @param value The ingestionTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setIngestionTimestamp(long value) {

        ingestionTimestamp_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Ingestion timestamp is something that is
       * populated when the packet is received at the server side.l
       * </pre>
       *
       * <code>uint64 ingestion_timestamp = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIngestionTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ingestionTimestamp_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Timestamp)
    }

    // @@protoc_insertion_point(class_scope:Timestamp)
    private static final com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp();
    }

    public static com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Timestamp>
        PARSER = new com.google.protobuf.AbstractParser<Timestamp>() {
      @java.lang.Override
      public Timestamp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Timestamp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Timestamp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.nichesolv.nds.model.proto.model.TimestampProto.Timestamp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Timestamp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Timestamp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n-com/nichesolv/nds/model/proto/timestam" +
      "p.proto\"D\n\tTimestamp\022\032\n\022observed_timesta" +
      "mp\030\001 \001(\r\022\033\n\023ingestion_timestamp\030\002 \001(\004B5\n" +
      "#com.nichesolv.nds.model.proto.modelB\016Ti" +
      "mestampProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_Timestamp_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Timestamp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Timestamp_descriptor,
        new java.lang.String[] { "ObservedTimestamp", "IngestionTimestamp", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
