// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: com/nichesolv/nds/model/proto/timestamp_with_metadata.proto

package com.nichesolv.nds.model.proto.model;

public final class TimestampWithMetadataProto {
  private TimestampWithMetadataProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TimestampWithMetadataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:TimestampWithMetadata)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 timestamp = 100;</code>
     * @return The timestamp.
     */
    int getTimestamp();

    /**
     * <code>uint64 ingestionTimestamp = 101;</code>
     * @return The ingestionTimestamp.
     */
    long getIngestionTimestamp();

    /**
     * <code>string correlationId = 102;</code>
     * @return The correlationId.
     */
    String getCorrelationId();
    /**
     * <code>string correlationId = 102;</code>
     * @return The bytes for correlationId.
     */
    com.google.protobuf.ByteString
        getCorrelationIdBytes();

    /**
     * <code>uint64 imei = 103;</code>
     * @return The imei.
     */
    long getImei();
  }
  /**
   * Protobuf type {@code TimestampWithMetadata}
   */
  public static final class TimestampWithMetadata extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:TimestampWithMetadata)
      TimestampWithMetadataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TimestampWithMetadata.newBuilder() to construct.
    private TimestampWithMetadata(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TimestampWithMetadata() {
      correlationId_ = "";
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new TimestampWithMetadata();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return TimestampWithMetadataProto.internal_static_TimestampWithMetadata_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return TimestampWithMetadataProto.internal_static_TimestampWithMetadata_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              TimestampWithMetadata.class, Builder.class);
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 100;
    private int timestamp_ = 0;
    /**
     * <code>uint32 timestamp = 100;</code>
     * @return The timestamp.
     */
    @Override
    public int getTimestamp() {
      return timestamp_;
    }

    public static final int INGESTIONTIMESTAMP_FIELD_NUMBER = 101;
    private long ingestionTimestamp_ = 0L;
    /**
     * <code>uint64 ingestionTimestamp = 101;</code>
     * @return The ingestionTimestamp.
     */
    @Override
    public long getIngestionTimestamp() {
      return ingestionTimestamp_;
    }

    public static final int CORRELATIONID_FIELD_NUMBER = 102;
    @SuppressWarnings("serial")
    private volatile Object correlationId_ = "";
    /**
     * <code>string correlationId = 102;</code>
     * @return The correlationId.
     */
    @Override
    public String getCorrelationId() {
      Object ref = correlationId_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        correlationId_ = s;
        return s;
      }
    }
    /**
     * <code>string correlationId = 102;</code>
     * @return The bytes for correlationId.
     */
    @Override
    public com.google.protobuf.ByteString
        getCorrelationIdBytes() {
      Object ref = correlationId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        correlationId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IMEI_FIELD_NUMBER = 103;
    private long imei_ = 0L;
    /**
     * <code>uint64 imei = 103;</code>
     * @return The imei.
     */
    @Override
    public long getImei() {
      return imei_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (timestamp_ != 0) {
        output.writeUInt32(100, timestamp_);
      }
      if (ingestionTimestamp_ != 0L) {
        output.writeUInt64(101, ingestionTimestamp_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(correlationId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 102, correlationId_);
      }
      if (imei_ != 0L) {
        output.writeUInt64(103, imei_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (timestamp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(100, timestamp_);
      }
      if (ingestionTimestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(101, ingestionTimestamp_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(correlationId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(102, correlationId_);
      }
      if (imei_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(103, imei_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof TimestampWithMetadata)) {
        return super.equals(obj);
      }
      TimestampWithMetadata other = (TimestampWithMetadata) obj;

      if (getTimestamp()
          != other.getTimestamp()) return false;
      if (getIngestionTimestamp()
          != other.getIngestionTimestamp()) return false;
      if (!getCorrelationId()
          .equals(other.getCorrelationId())) return false;
      if (getImei()
          != other.getImei()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + getTimestamp();
      hash = (37 * hash) + INGESTIONTIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getIngestionTimestamp());
      hash = (37 * hash) + CORRELATIONID_FIELD_NUMBER;
      hash = (53 * hash) + getCorrelationId().hashCode();
      hash = (37 * hash) + IMEI_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getImei());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static TimestampWithMetadata parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static TimestampWithMetadata parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static TimestampWithMetadata parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static TimestampWithMetadata parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static TimestampWithMetadata parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static TimestampWithMetadata parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static TimestampWithMetadata parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static TimestampWithMetadata parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static TimestampWithMetadata parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static TimestampWithMetadata parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static TimestampWithMetadata parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static TimestampWithMetadata parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(TimestampWithMetadata prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code TimestampWithMetadata}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:TimestampWithMetadata)
        TimestampWithMetadataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return TimestampWithMetadataProto.internal_static_TimestampWithMetadata_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return TimestampWithMetadataProto.internal_static_TimestampWithMetadata_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                TimestampWithMetadata.class, Builder.class);
      }

      // Construct using com.nichesolv.nds.model.proto.model.TimestampWithMetadataProto.TimestampWithMetadata.newBuilder()
      private Builder() {

      }

      private Builder(
          BuilderParent parent) {
        super(parent);

      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        timestamp_ = 0;
        ingestionTimestamp_ = 0L;
        correlationId_ = "";
        imei_ = 0L;
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return TimestampWithMetadataProto.internal_static_TimestampWithMetadata_descriptor;
      }

      @Override
      public TimestampWithMetadata getDefaultInstanceForType() {
        return TimestampWithMetadata.getDefaultInstance();
      }

      @Override
      public TimestampWithMetadata build() {
        TimestampWithMetadata result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public TimestampWithMetadata buildPartial() {
        TimestampWithMetadata result = new TimestampWithMetadata(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(TimestampWithMetadata result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.timestamp_ = timestamp_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.ingestionTimestamp_ = ingestionTimestamp_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.correlationId_ = correlationId_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.imei_ = imei_;
        }
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof TimestampWithMetadata) {
          return mergeFrom((TimestampWithMetadata)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(TimestampWithMetadata other) {
        if (other == TimestampWithMetadata.getDefaultInstance()) return this;
        if (other.getTimestamp() != 0) {
          setTimestamp(other.getTimestamp());
        }
        if (other.getIngestionTimestamp() != 0L) {
          setIngestionTimestamp(other.getIngestionTimestamp());
        }
        if (!other.getCorrelationId().isEmpty()) {
          correlationId_ = other.correlationId_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.getImei() != 0L) {
          setImei(other.getImei());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 800: {
                timestamp_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 800
              case 808: {
                ingestionTimestamp_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 808
              case 818: {
                correlationId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 818
              case 824: {
                imei_ = input.readUInt64();
                bitField0_ |= 0x00000008;
                break;
              } // case 824
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int timestamp_ ;
      /**
       * <code>uint32 timestamp = 100;</code>
       * @return The timestamp.
       */
      @Override
      public int getTimestamp() {
        return timestamp_;
      }
      /**
       * <code>uint32 timestamp = 100;</code>
       * @param value The timestamp to set.
       * @return This builder for chaining.
       */
      public Builder setTimestamp(int value) {

        timestamp_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 timestamp = 100;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        timestamp_ = 0;
        onChanged();
        return this;
      }

      private long ingestionTimestamp_ ;
      /**
       * <code>uint64 ingestionTimestamp = 101;</code>
       * @return The ingestionTimestamp.
       */
      @Override
      public long getIngestionTimestamp() {
        return ingestionTimestamp_;
      }
      /**
       * <code>uint64 ingestionTimestamp = 101;</code>
       * @param value The ingestionTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setIngestionTimestamp(long value) {

        ingestionTimestamp_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 ingestionTimestamp = 101;</code>
       * @return This builder for chaining.
       */
      public Builder clearIngestionTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ingestionTimestamp_ = 0L;
        onChanged();
        return this;
      }

      private Object correlationId_ = "";
      /**
       * <code>string correlationId = 102;</code>
       * @return The correlationId.
       */
      public String getCorrelationId() {
        Object ref = correlationId_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          correlationId_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string correlationId = 102;</code>
       * @return The bytes for correlationId.
       */
      public com.google.protobuf.ByteString
          getCorrelationIdBytes() {
        Object ref = correlationId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          correlationId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string correlationId = 102;</code>
       * @param value The correlationId to set.
       * @return This builder for chaining.
       */
      public Builder setCorrelationId(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        correlationId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string correlationId = 102;</code>
       * @return This builder for chaining.
       */
      public Builder clearCorrelationId() {
        correlationId_ = getDefaultInstance().getCorrelationId();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string correlationId = 102;</code>
       * @param value The bytes for correlationId to set.
       * @return This builder for chaining.
       */
      public Builder setCorrelationIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        correlationId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private long imei_ ;
      /**
       * <code>uint64 imei = 103;</code>
       * @return The imei.
       */
      @Override
      public long getImei() {
        return imei_;
      }
      /**
       * <code>uint64 imei = 103;</code>
       * @param value The imei to set.
       * @return This builder for chaining.
       */
      public Builder setImei(long value) {

        imei_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 imei = 103;</code>
       * @return This builder for chaining.
       */
      public Builder clearImei() {
        bitField0_ = (bitField0_ & ~0x00000008);
        imei_ = 0L;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:TimestampWithMetadata)
    }

    // @@protoc_insertion_point(class_scope:TimestampWithMetadata)
    private static final TimestampWithMetadata DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new TimestampWithMetadata();
    }

    public static TimestampWithMetadata getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TimestampWithMetadata>
        PARSER = new com.google.protobuf.AbstractParser<TimestampWithMetadata>() {
      @Override
      public TimestampWithMetadata parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TimestampWithMetadata> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<TimestampWithMetadata> getParserForType() {
      return PARSER;
    }

    @Override
    public TimestampWithMetadata getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TimestampWithMetadata_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_TimestampWithMetadata_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n;com/nichesolv/nds/model/proto/timestam" +
      "p_with_metadata.proto\"k\n\025TimestampWithMe" +
      "tadata\022\021\n\ttimestamp\030d \001(\r\022\032\n\022ingestionTi" +
      "mestamp\030e \001(\004\022\025\n\rcorrelationId\030f \001(\t\022\014\n\004" +
      "imei\030g \001(\004BA\n#com.nichesolv.nds.model.pr" +
      "oto.modelB\032TimestampWithMetadataProtob\006p" +
      "roto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_TimestampWithMetadata_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_TimestampWithMetadata_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_TimestampWithMetadata_descriptor,
        new String[] { "Timestamp", "IngestionTimestamp", "CorrelationId", "Imei", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
