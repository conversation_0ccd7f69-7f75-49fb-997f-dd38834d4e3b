syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "MotorControllerProto";

import "com/nichesolv/nds/model/proto/timestamp.proto";
import  "com/nichesolv/nds/model/proto/metadata.proto";


message MotorController{
  MotorControllerData data = 1;
  MotorControllerStatus status = 2;
  Timestamp timestamp = 3;
  Metadata metadata = 4;
  optional bool di_motion = 5;
  optional bool di_ignition = 6;
}

// Motor controller data.
message MotorControllerData {

  optional float dcVoltage = 1;

  optional sint32 motorSpeed = 3;

  optional float dcCurrent = 5;

  optional float motorTemperature = 7;

  optional float mcsTemperature = 9;

}

// Motor controller status.
message MotorControllerStatus {

  optional string driveSelection = 1;

  optional bool  regeneration = 2;

  optional bool readySign = 3;

  optional bool pLight = 4;

  optional bool reverse = 5;

  optional bool cruise = 6;

  optional bool vehicleBrake = 7;

  optional bool sideStand = 8;

  optional sint32 throttlePercentage = 9;

  repeated MotorStatus motor_status = 10;

}

enum MotorStatus {

  WORKING_FINE = 0;

  MOSFET_FAULT = 1;

  DRIVE_FAULT = 2;

  OVERLOAD_FAULT = 4;

  OVER_VOLTAGE_FAULT = 8;

  UNDER_VOLTAGE_FAULT = 12;

  OVERHEAT_FAULT = 16;

  OVER_CURRENT_FAULT = 10;

  LOW_VOLTAGE_FAULT = 32;

  MOTOR_LOST_HALL_FAULT = 64;

  HALL_SENSOR_FAULT = 128;

  MOTOR_OVERHEAT_FAULT = 256;

  MOTOR_STUCK_FAULT = 512;

  THROTTLE_FAULT = 1024;

  SPEED_OVERSHOOT_FAULT = 14;

  NO_STATUS = 15;
}