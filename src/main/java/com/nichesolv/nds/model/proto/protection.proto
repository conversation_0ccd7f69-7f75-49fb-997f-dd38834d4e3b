syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "ProtectionProto";


enum Protection {

  CELL_UNDER_VOLTAGE_PROTECTION = 0;

  CELL_OVER_VOLTAGE_PROTECTION = 1;

  PACK_UNDER_VOLTAGE_PROTECTION = 2;

  PACK_OVER_VOLTAGE_PROTECTION = 3;

  CELL_UNDER_TEMPERATURE_PROTECTION = 4;

  CELL_OVER_TEMPERATURE_PROTECTION = 5;

  AMBIENT_UNDER_TEMPERATURE_PROTECTION = 6;

  AMBIENT_OVER_TEMPERATURE_PROTECTION = 7;

  MOSFET_UNDER_TEMPERATURE_PROTECTION = 8;

  MOSFET_OVER_TEMPERATURE_PROTECTION = 9;

  THERMAL_RUNWAY_PROTECTION = 10;

  BUZZER_OR_LED_PROTECTION = 11;

  CGH_OVER_CURRENT_PROTECTION = 12;

  DSG_OVER_CURRENT_PROTECTION = 13;

  NO_PROTECTION = -1;

  SINGLE_OVERVOLTAGE_PROTECTION = 15;

  SINGLE_UNDERVOLTAGE_PROTECTION = 16;

  PACK_OVERVOLTAGE_PROTECTION = 17;

  PACK_UNDERVOLTAGE_PROTECTION = 18;

  CHARGE_OVER_TEMPERATURE_PROTECTION = 19;

  CHARGE_LOW_TEMPERATURE_PROTECTION = 20;

  DISCHARGE_OVER_TEMPERATURE_PROTECTION = 21;

  DISCHARGE_LOW_TEMPERATURE_PROTECTION = 22;

  CHARGE_OVERCURRENT_PROTECTION = 23;

  DISCHARGE_OVERCURRENT_PROTECTION = 24;

  SHORT_CIRCUIT_PROTECTION = 25;

  FRONT_DETECTION_IC_ERROR = 26;

  SOFTWARE_LOCK_MOS = 27;

  CHARGE_MOS_OFF = 28;

  DISCHARGE_MOS_OFF = 29;
}
