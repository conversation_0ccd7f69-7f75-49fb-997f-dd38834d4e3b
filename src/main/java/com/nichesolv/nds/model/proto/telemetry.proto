syntax = "proto3";

option java_package = "com.nichesolv.nds.model.proto.model";
option java_outer_classname = "TelemetryProto";

import "com/nichesolv/nds/model/proto/timestamp.proto";
import "com/nichesolv/nds/model/proto/metadata.proto";

message Telemetry {

  // Metadata
  Metadata metadata = 1;

  // Timestamp
  Timestamp timestamp = 2;

  // Accelerometer Data
  message Accelerometer {
    optional sint32 x = 1;
    optional sint32 y = 2;
    optional sint32 z = 3;
  }
  optional Accelerometer accelerometer = 3;

  // Gyroscope Data
  message Gyroscope {
    optional sint32 x = 1;
    optional sint32 y = 2;
    optional sint32 z = 3;
  }
  optional Gyroscope gyroscope = 4;

  // Gravitational Vector Data
  message GravitationalVector {
    optional float x = 1;
    optional float y = 2;
    optional float z = 3;
  }
  optional GravitationalVector gravitational_vector = 5;

  // Analog Input Data
  message AnalogInput {
    optional sint32 temp = 1;
    optional uint32 vin = 2;
    optional uint32 vsys = 3;
    optional uint32 vbuck = 4;
    optional uint32 vusr_1 = 5;
    optional uint32 vusr_2 = 6;
    optional uint32 lean_angle = 7;
  }
  optional AnalogInput analog_input = 6;

  // Digital Input Data
  message DigitalInput {
    optional bool usr1 = 1;
    optional bool usr2 = 2;
    optional bool motion = 3;
    optional bool tamper = 4;
    optional bool main_power = 5;
    optional bool ignition = 6;
  }
  optional DigitalInput digital_input = 7;

  // Digital Output Data
  message DigitalOutput {
    optional bool usr1 = 1;
    optional bool usr2 = 2;
  }
  optional DigitalOutput digital_output = 8;
}