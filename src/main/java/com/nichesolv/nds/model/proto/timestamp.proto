syntax = "proto3";

// The package where the generated code will live.
option java_package = "com.nichesolv.nds.model.proto.model";
// Name of the outer class.
// the use of this option is debatable but we are keeping it since we have model class with the same name.
option java_outer_classname = "TimestampProto";

// This class represents timestamp, contains event and ingestion timestamps.
// Notice that none of the fields are optional in Timestamp message format, what this means is both observed_timestamp and
// ingestion_timestamp are required fields and the pipeline will report an error if these fields were to be empty or null or without a values.
message Timestamp {

  // Event timestamp. This timestamp is derived from the event
  // and it represents the actual timestamp at which the event happened.
  uint32 observed_timestamp = 1;

  // Ingestion timestamp is something that is
  // populated when the packet is received at the server side.l
  uint64 ingestion_timestamp = 2;

}