appConfig.queueingEngineConfig.host=${QUEUEING_ENGINE_CONFIG_HOST}
appConfig.queueingEngineConfig.port=${QUEUEING_ENGINE_CONFIG_PORT}
appConfig.queueingEngineConfig.virtualHost=${QUEUEING_ENGINE_CONFIG_VIRTUAL_HOST}
appConfig.queueingEngineConfig.username=${QUEUEING_ENGINE_CONFIG_USERNAME}
appConfig.queueingEngineConfig.password=${QUEUEING_ENGINE_CONFIG_PASSWORD}
//=====================
// STREAMING
//=====================
appConfig.streaming.checkpointing.interval=${STREAMING_CHECKPOINTING_INTERVAL}
appConfig.streaming.parallelism=${STREAMING_PARALLELISM}
appConfig.streaming.jobName="EV Data Cleaning Pipeline"
//====================
// UDF CONFIG
//====================
appConfig.udf.dataCleaning.python.v1.basePath=
appConfig.udf.dataCleaning.python.v1.entryPoint=
appConfig.udf.dataCleaning.python.v1.pythonExecutablePath=
appConfig.udf.dataCleaning.python.v1.files.zip=/home/<USER>/data-cleaning/udf/
//====================
// RMQ SOURCE CONFIG
//===================
appConfig.source.rmq.accelerometer.uid:AccelerometerSource=
appConfig.source.rmq.accelerometer.parallelism=
appConfig.source.rmq.io.uid:IoSource=
appConfig.source.rmq.io.parallelism=
appConfig.source.rmq.location.uid:LocationSource=
appConfig.source.rmq.location.parallelism=
appConfig.source.rmq.motorController.uid:MotorController=
appConfig.source.rmq.motorController.parallelism=
//================
// JDBC
//===============
appConfig.persistence.jdbcConnectionUrl=*****************************************
appConfig.persistence.jdbcUsername=postgres
appConfig.persistence.jdbcPassword=password
//================
// CATALOG
//================
appConfig.persistence.catalog.enable=
appConfig.persistence.catalog.name=
appConfig.persistence.catalog.defaultDatabase=
appConfig.persistence.catalog.username=
appConfig.persistence.catalog.password=
appConfig.persistence.catalog.baseUrl=
//================
// REST API CONFIG
//================
appConfig.persistence.dev.locationData.endpoint=
appConfig.persistence.demo.locationData.endpoint=
appConfig.persistence.dev.motorControllerData.endpoint=
appConfig.persistence.demo.motorControllerData.endpoint=
appConfig.streaming.checkpointDir=
# LOCATION ENDPOINT
appConfig.persistence.dev.endpoint=https://evahanam.dev.nichesolv.com/ev
appConfig.persistence.demo.endpoint=https://evahana.poc.nichesolv.com/ev
appConfig.persistence.cluster.endpoint=https://evahana.poc.nichesolv.com/ev
appConfig.persistence.io.path=/v1/analog-input-data
appConfig.persistence.location.path=/v1/location-data
appConfig.persistence.mc.data.path=/v1/motor-data
appConfig.persistence.mc.status.path=/v1/mcs-status-data
# PYTHON ARCHIVE
appConfig.python.files=s3://com.nichesolv.flink.dev.python.files
appConfig.python.archive=s3://com.nichesolv.flink.dev.python.archive
appConfig.python.executable=python
appConfig.python.client.executable=python
appConfig.deployWithZipArchiveEnabled=false
# Source Properties
appConfig.source.analogInput.queueName=event.parsed.analogInput
appConfig.source.analogInput.usesCorrelationId=false
appConfig.version=232bbfcaef6526ad32944d273bba5ee4bc4dc930
appConfig.autoWatermarkInterval=100
appConfig.enableRestPersistence=true
appConfig.enableDbPersistence=true
appConfig.windowWidth=15
appConfig.defaultIdleness=30
appConfig.debug=true
# Lookup cache
appConfig.lookup.cache=PARTIAL
appConfig.lookup.partial-cache.max-rows=1000
appConfig.lookup.partial-cache.expire-after-write=3600s
appConfig.lookup.partial-cache.expire-after-access=3600s
appConfig.lookup.partial-cache.cache-missing-key=false
appConfig.lookup.max-retries=5
# Watermarking
appConfig.useBoundedWatermarkGenerator=true
appConfig.streaming.maxParallelism=10


