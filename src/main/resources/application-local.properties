appConfig.queueingEngineConfig.host=${QUEUEING_ENGINE_CONFIG_HOST}
appConfig.queueingEngineConfig.port=${QUEUEING_ENGINE_CONFIG_PORT}
appConfig.queueingEngineConfig.virtualHost=${QUEUEING_ENGINE_CONFIG_VIRTUAL_HOST}
appConfig.queueingEngineConfig.username=${QUEUEING_ENGINE_CONFIG_USERNAME}
appConfig.queueingEngineConfig.password=${QUEUEING_ENGINE_CONFIG_PASSWORD}
appConfig.streaming.checkpointing.interval=600000
appConfig.streaming.parallelism=1
appConfig.streaming.jobName=EV Data Cleaning Pipeline
appConfig.persistence.jdbcConnectionUrl=jdbc:postgresql://${JDBC_HOST}:${JDBC_PORT}/${JDBC_DEFAULT_DATABASE}
appConfig.persistence.jdbcUsername=${JDBC_USERNAME}
appConfig.persistence.jdbcPassword=${JDBC_PASSWORD}
appConfig.persistence.catalog.name=evcatalog
appConfig.persistence.catalog.defaultDatabase=${JDBC_DEFAULT_DATABASE}
appConfig.persistence.catalog.username=${JDBC_USERNAME}
appConfig.persistence.catalog.password=${JDBC_PASSWORD}
appConfig.persistence.catalog.baseUrl=jdbc:postgresql://${JDBC_HOST}:${JDBC_PORT}
appConfig.persistence.catalog.enable=false
appConfig.streaming.checkpointDir=file:///tmp/flink/data-cleaning-job-checkpoint
# REST API
appConfig.persistence.dev.endpoint=http://localhost:5000
appConfig.persistence.demo.endpoint=http://localhost:5000
appConfig.persistence.cluster.endpoint=http://localhost:5000
appConfig.persistence.io.path=/v1/analog-input-data
appConfig.persistence.location.path=/v1/location-data
appConfig.persistence.mc.data.path=/v1/motor-data
appConfig.persistence.mc.status.path=/v1/mcs-status-data
# PYTHON ARCHIVE
appConfig.python.files=s3://com.nichesolv.flink.local.python.files/v1
appConfig.python.archive=s3://com.nichesolv.flink.local.python.archive/venv.zip
appConfig.python.executable=venv.zip/venv/bin/python3
appConfig.python.client.executable=venv.zip/venv/bin/python3
# This setting has flink use executables and files from s3 bucket.
appConfig.deployWithZipArchiveEnabled=false
appConfig.udf.dataCleaning.python.v1.basePath=${PYTHON_FILES}
appConfig.udf.dataCleaning.python.v1.entryPoint=udf.py
appConfig.udf.dataCleaning.python.v1.pythonExecutablePath=python3
# Source Properties
appConfig.source.analogInput.queueName=event.parsed.analogInput
appConfig.source.analogInput.usesCorrelationId=false
appConfig.version=232bbfcaef6526ad32944d273bba5ee4bc4dc930
appConfig.autoWatermarkInterval=10
appConfig.enableRestPersistence=false
appConfig.enableDbPersistence=true
appConfig.windowWidth=5
appConfig.defaultIdleness=120
appConfig.debug=false
# Lookup cache
appConfig.lookup.cache=PARTIAL
appConfig.lookup.partial-cache.max-rows=1000
appConfig.lookup.partial-cache.expire-after-write=3600s
appConfig.lookup.partial-cache.expire-after-access=3600s
appConfig.lookup.partial-cache.cache-missing-key=false
appConfig.lookup.max-retries=5
# Watermarking
appConfig.useBoundedWatermarkGenerator=true
appConfig.streaming.maxParallelism=10
appConfig.enableRawDataPersistence=false
appConfig.cache.host=localhost
appConfig.cache.port=6379
appConfig.cache.username=
appConfig.cache.password=

redis.host=localhost
redis.port=6379
redis.username=
redis.password=