appConfig.queueingEngineConfig.host=${QUEUEING_ENGINE_CONFIG_HOST}
appConfig.queueingEngineConfig.port=${QUEUEING_ENGINE_CONFIG_PORT}
appConfig.queueingEngineConfig.virtualHost=${QUEUEING_ENGINE_CONFIG_VIRTUAL_HOST}
appConfig.queueingEngineConfig.username=${QUEUEING_ENGINE_CONFIG_USERNAME}
appConfig.queueingEngineConfig.password=${QUEUEING_ENGINE_CONFIG_PASSWORD}
appConfig.streaming.checkpointing.interval=600000
appConfig.streaming.parallelism=1
appConfig.streaming.jobName=EV Data Cleaning Pipeline
appConfig.udf.dataCleaning.python.v1.basePath=/home/<USER>/data-cleaning/udf/
appConfig.udf.dataCleaning.python.v1.entryPoint=udf.py
appConfig.udf.dataCleaning.python.v1.pythonExecutablePath=/usr/bin/python3
appConfig.persistence.jdbcConnectionUrl=********************************/postgres
appConfig.persistence.jdbcUsername=postgres
appConfig.persistence.jdbcPassword=password
appConfig.persistence.catalog.name=evcatalog
appConfig.persistence.catalog.defaultDatabase=postgres
appConfig.persistence.catalog.username=postgres
appConfig.persistence.catalog.password=password
appConfig.persistence.catalog.baseUrl=********************************
appConfig.persistence.catalog.enable=false
appConfig.streaming.checkpointDir=file:///tmp/flink/data-cleaning-job-checkpoint
# REST API
appConfig.persistence.dev.endpoint=https://evahanam.dev.nichesolv.com/ev
appConfig.persistence.demo.endpoint=https://evahana.poc.nichesolv.com/ev
appConfig.persistence.cluster.endpoint=https://nichesolv.ev-be-dev.nichesolv.com/ev
appConfig.persistence.io.path=/v1/analog-input-data
appConfig.persistence.location.path=/v1/location-data
appConfig.persistence.mc.data.path=/v1/motor-data
appConfig.persistence.mc.status.path=/v1/mcs-status-data
# PYTHON ARCHIVE
appConfig.python.files=s3://com.nichesolv.flink.dev.python.files
appConfig.python.archive=s3://com.nichesolv.flink.dev.python.archive
appConfig.python.executable=python
appConfig.python.client.executable=python
appConfig.deployWithZipArchiveEnabled=false
# Source Properties
appConfig.source.analogInput.queueName=event.parsed.analogInput
appConfig.source.analogInput.usesCorrelationId=false
appConfig.version=232bbfcaef6526ad32944d273bba5ee4bc4dc930
appConfig.autoWatermarkInterval=100
appConfig.enableRestPersistence=true
appConfig.enableDbPersistence=true
appConfig.windowWidth=15
appConfig.defaultIdleness=300
appConfig.debug=true
# Lookup cache
appConfig.lookup.cache=PARTIAL
appConfig.lookup.partial-cache.max-rows=1000
appConfig.lookup.partial-cache.expire-after-write=3600s
appConfig.lookup.partial-cache.expire-after-access=3600s
appConfig.lookup.partial-cache.cache-missing-key=false
appConfig.lookup.max-retries=5
# Watermarking
appConfig.useBoundedWatermarkGenerator=true
appConfig.streaming.maxParallelism=10