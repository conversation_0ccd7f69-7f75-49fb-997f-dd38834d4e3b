SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CASE WHEN TRY_CAST(D.temp AS FLOAT) NOT BETWEEN D.ai_temp_lower_limit
  AND D.ai_temp_upper_limit THEN TRY_CAST(D.temp AS FLOAT) ELSE NULL END AS ai_temp,
  CASE WHEN TRY_CAST(D.vin AS FLOAT) / 1000 NOT BETWEEN D.ai_vin_lower_limit
  AND D.ai_vin_upper_limit THEN TRY_CAST(D.vin AS FLOAT) / 1000 ELSE NULL END AS ai_vin,
  CAST(NULL AS FLOAT) AS ai_vsys,
  CAST(NULL AS FLOAT) AS ai_vbuck,
  CAST(NULL AS INTEGER) AS ai_vusr1,
  CAST(NULL AS INTEGER) AS ai_vusr2,
  CAST(NULL AS INTEGER) AS ai_lean_angle
FROM
  analog_inputs AS D
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE'
  AND (
    TRY_CAST(D.temp AS FLOAT) NOT BETWEEN D.ai_temp_lower_limit
    AND D.ai_temp_upper_limit
    OR TRY_CAST(D.vin AS FLOAT) / 1000 NOT BETWEEN D.ai_vin_lower_limit
    AND D.ai_vin_upper_limit
  )
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));