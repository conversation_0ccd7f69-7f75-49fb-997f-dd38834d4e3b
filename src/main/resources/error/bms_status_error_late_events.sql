CREATE TEMPORARY VIEW `bms_status_error_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CASE WHEN TRY_CAST(D.battery_volt AS FLOAT) not between 0
  AND 100 THEN TRY_CAST(D.battery_volt AS FLOAT)
  ELSE NULL END AS battery_volt,
  CAST(NULL AS FLOAT) AS cell_volt_max,
  CAST(NULL AS FLOAT) AS cell_volt_min,
  CAST(NULL AS INTEGER) AS chg_cycle_count,
  CASE WHEN TRY_CAST(D.`current` AS FLOAT) NOT BETWEEN -100
  AND 100 THEN TRY_CAST(D.`current` AS FLOAT) ELSE NULL END  AS `current`,
  CAST(NULL AS INTEGER) AS dsg_cycle_count,
  CASE WHEN TRY_CAST(D.soc AS FLOAT) NOT BETWEEN 0
  AND 100 THEN TRY_CAST(D.soc AS FLOAT) ELSE NULL END AS soc,
  CAST(NULL AS FLOAT) AS soh,
  CAST(NULL AS FLOAT) AS temperature_max,
  CAST(NULL AS FLOAT) AS temperature_min,
  CAST(NULL AS INTEGER) AS remaining_capacity,
  CASE WHEN TRY_CAST(D.mosfet_temperature AS FLOAT) NOT BETWEEN -25
  AND 80 THEN TRY_CAST(D.mosfet_temperature AS FLOAT) ELSE NULL END AS mosfet_temperature,
  CAST(NULL AS FLOAT) AS discharge
FROM
  bms_status AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE'
  AND (
        TRY_CAST(D.battery_volt AS FLOAT) not between 0
        AND 100
        OR TRY_CAST(D.`current` AS FLOAT) NOT BETWEEN -100
        AND 100
        OR TRY_CAST(D.soc AS FLOAT) NOT BETWEEN 0
        AND 100
        OR TRY_CAST(D.mosfet_temperature AS FLOAT) NOT BETWEEN -25
        AND 80
      )
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));
