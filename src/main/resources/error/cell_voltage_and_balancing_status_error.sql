SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  D.cell_id AS cell_id,
  CASE WHEN TRY_CAST(D.volts AS FLOAT) NOT BETWEEN D.battery_cell_voltage_lower_limit + 0.001
  AND D.battery_cell_voltage_upper_limit
  THEN TRY_CAST(D.volts AS FLOAT) ELSE NULL END AS volts,
  CAST(NULL AS BOOLEAN) AS balancing_status
FROM
  bms_cell_voltages AS D
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE'
  AND ((
        TRY_CAST(D.volts AS FLOAT) NOT BETWEEN D.battery_cell_voltage_lower_limit + 0.001
        AND D.battery_cell_voltage_upper_limit
      )
      OR D.cell_id > D.voltage_sensor_count)
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));