SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CASE WHEN TRY_CAST(D.latitude AS DOUBLE) NOT BETWEEN D.latitude_lower_limit
  AND D.latitude_upper_limit THEN TRY_CAST(D.latitude AS DOUBLE) ELSE NULL END AS latitude,
  CASE WHEN TRY_CAST(D.longitude AS DOUBLE) NOT BETWEEN D.longitude_lower_limit
  AND D.longitude_upper_limit THEN TRY_CAST(D.longitude AS DOUBLE) ELSE NULL END AS longitude,
  CASE WHEN TRY_CAST(D.altitude AS FLOAT) NOT BETWEEN D.altitude_lower_limit
  AND D.altitude_upper_limit THEN TRY_CAST(D.altitude AS FLOAT) ELSE NULL END AS altitude,
  CASE WHEN TRY_CAST(D.speed AS FLOAT) NOT BETWEEN D.speed_lower_limit
  AND D.speed_upper_limit THEN TRY_CAST(D.speed AS FLOAT) ELSE NULL END AS speed,
  CAST(NULL AS FLOAT) AS bearing,
  CAST(NULL AS FLOAT) AS pdop,
  CAST(NULL AS FLOAT) AS hdop,
  CAST(NULL AS FLOAT) AS vdop,
  CAST(NULL AS FLOAT) AS view_sats,
  CAST(NULL AS FLOAT) AS track_sats,
  CAST(NULL AS DOUBLE) AS gps_distance
FROM
  location AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE'
  AND (
        TRY_CAST(D.latitude AS DOUBLE) NOT BETWEEN D.latitude_lower_limit
        AND D.latitude_upper_limit
        OR TRY_CAST(D.longitude AS DOUBLE) NOT BETWEEN D.longitude_lower_limit
        AND D.longitude_upper_limit
        OR TRY_CAST(D.altitude AS FLOAT) NOT BETWEEN D.altitude_lower_limit
        AND D.altitude_upper_limit
        OR TRY_CAST(D.speed AS FLOAT) NOT BETWEEN D.speed_lower_limit
        AND D.speed_upper_limit
      )
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));
