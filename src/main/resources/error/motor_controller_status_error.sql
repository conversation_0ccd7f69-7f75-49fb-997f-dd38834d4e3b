SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CAST(NULL AS STRING) AS drive_selection,
  CAST(NULL AS BOOLEAN) AS regeneration,
  CAST(NULL AS BOOLEAN) AS ready_sign,
  CAST(NULL AS BOOLEAN) AS p_light,
  CAST(NULL AS BOOLEAN) AS reverse,
  CAST(NULL AS BOOLEAN) AS cruise,
  CAST(NULL AS BOOLEAN) AS vehicle_brake,
  CAST(NULL AS BOOLEAN) AS side_stand,
  CASE WHEN TRY_CAST(D.throttle_percentage AS FLOAT) NOT BETWEEN D.motor_throttle_percentage_lower_limit
  AND D.motor_throttle_percentage_upper_limit THEN TRY_CAST(D.throttle_percentage AS FLOAT) ELSE NULL END AS throttle_percentage_corrected
FROM
  motor_controller_status AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE'
  AND (
            TRY_CAST(D.throttle_percentage AS FLOAT) NOT BETWEEN D.motor_throttle_percentage_lower_limit
            AND D.motor_throttle_percentage_upper_limit
          )
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));
