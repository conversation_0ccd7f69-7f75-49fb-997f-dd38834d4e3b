CREATE TEMPORARY VIEW `bms_alarm_protection_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  D.alarm AS alarm,
  D.protection AS protection,
  D.alarm_name AS alarm_name,
  D.protection_name AS protection_name,
  A.id AS alarm_id
FROM
  bms_alarms_protections AS D
  INNER JOIN alarm_type FOR SYSTEM_TIME AS OF D.proc_time AS A ON A.name = D.alarm_name
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE';
