SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS observed_timestamp,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  C.temp_corrected AS temp_corrected,
  (C.vin_corrected / 1000) AS vin_corrected,
  TRY_CAST(D.vsys AS FLOAT) AS vsys,
  TRY_CAST(D.vbuck AS FLOAT) AS vbuck,
  D.vusr_1 AS vusr_1,
  D.vusr_2 AS vusr_2,
  D.lean_angle AS lean_angle
FROM
  (
    SELECT
      imei,
      corr_id,
      temp_corrected,
      vin_corrected
    FROM
      (
        SELECT
          A.imei AS imei,
          A.corr_id AS corr_id,
          A.temp_corrected AS temp_corrected,
          B.vin_corrected AS vin_corrected
        FROM
          (
            SELECT
              r.imei AS imei,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 0) AS INT
              ) AS ts,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 1) AS STRING
              ) AS corr_id,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 2) AS FLOAT
              ) AS temp_corrected,
              1000 * unix_timestamp(
                cast(r.window_start AS string)
              ) AS window_start,
              1000 * unix_timestamp(
                cast(r.window_end AS string)
              ) AS window_end
            FROM
              (
                SELECT
                  imei,
                  tumble_start(rowtime, interval '10' seconds) AS window_start,
                  tumble_end(rowtime, interval '10' seconds) AS window_end,
                  impute_udf(
                    observed_timestamp, correlation_id,
                    temp, 20, 80, 'temp', 'backward_forward_filled'
                  ) AS temperature_corrected
                FROM
                  analog_inputs
                GROUP BY
                  imei,
                  tumble(rowtime, interval '10' seconds)
              ) AS r CROSS
              JOIN UNNEST(r.temperature_corrected) AS t (`a`)
          ) AS A
          INNER JOIN (
            SELECT
              r.imei AS imei,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 0) AS INT
              ) AS ts,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 1) AS STRING
              ) AS corr_id,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 2) AS FLOAT
              ) AS vin_corrected,
              1000 * unix_timestamp(
                cast(r.window_start AS string)
              ) AS window_start,
              1000 * unix_timestamp(
                cast(r.window_end AS string)
              ) AS window_end
            FROM
              (
                SELECT
                  imei,
                  tumble_start(rowtime, interval '10' seconds) AS window_start,
                  tumble_end(rowtime, interval '10' seconds) AS window_end,
                  impute_udf(
                    observed_timestamp, correlation_id,
                    vin, 2000, 100000, 'vin', 'backward_forward_filled'
                  ) AS vin_corrected
                FROM
                  analog_inputs
                GROUP BY
                  imei,
                  tumble(rowtime, interval '10' seconds)
              ) AS r CROSS
              JOIN UNNEST(r.vin_corrected) AS t (`a`)
          ) AS B ON A.imei = B.imei
          AND A.corr_id = B.corr_id
      )
  ) AS C
  INNER JOIN analog_inputs AS D ON C.imei = D.imei
  AND C.corr_id = D.correlation_id
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE';
