CREATE TEMPORARY VIEW `analog_input_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  TRY_CAST(D.temp AS FLOAT) AS ai_temp,
  (D.vin / 1000.0) AS ai_vin,
  TRY_CAST(D.vsys AS FLOAT) AS ai_vsys,
  TRY_CAST(D.vbuck AS FLOAT) AS ai_vbuck,
  D.vusr_1 AS ai_vusr1,
  D.vusr_2 AS ai_vusr2,
  D.lean_angle AS ai_lean_angle
FROM
  analog_inputs AS D
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE';
