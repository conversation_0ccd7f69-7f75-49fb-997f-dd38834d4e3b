SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS observed_timestamp,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  TRY_CAST(D.battery_volt AS FLOAT) AS battery_volt,
  TRY_CAST(D.cell_volt_max AS FLOAT) AS cell_volt_max,
  TRY_CAST(D.cell_volt_min AS FLOAT) AS cell_volt_min,
  D.chg_cycle_count AS chg_cycle_count,
  D.`current` AS `current`,
  D.dsg_cycle_count AS dsg_cycle_count,
  C.soc_corrected AS soc,
  TRY_CAST(D.soh AS FLOAT) AS soh,
  TRY_CAST(D.temperature_max AS FLOAT) AS temperature_max,
  TRY_CAST(D.temperature_min AS FLOAT) AS temperature_min,
  D.remaining_capacity AS remaining_capacity,
  CASE WHEN TRY_CAST(D.volts AS FLOAT) = 0
  --  When cell voltage is not received and balance status is received we get very small values. The below line handles that issue. Find the root cause and address it
      AND TRY_CAST(D.volts AS FLOAT) > 0.001
      AND TRY_CAST(D.volts AS FLOAT) <= 5 THEN TRY_CAST(D.volts AS FLOAT) ELSE NULL END AS volts,
  D.mosfet_temperature AS mosfet_temperature
FROM
   (
     SELECT
       A.imei AS imei,
       A.corr_id AS corr_id,
       A.soc_corrected AS soc_corrected
     FROM
       (
         SELECT
           r.imei AS imei,
           TRY_CAST(
             SPLIT_INDEX(`a`, ':', 0) AS INT
           ) AS ts,
           TRY_CAST(
             SPLIT_INDEX(`a`, ':', 1) AS STRING
           ) AS corr_id,
           TRY_CAST(
             SPLIT_INDEX(`a`, ':', 2) AS FLOAT
           ) AS soc_corrected,
           1000 * unix_timestamp(
             cast(r.window_start AS string)
           ) AS window_start,
           1000 * unix_timestamp(
             cast(r.window_end AS string)
           ) AS window_end
         FROM
           (
             SELECT
               imei,
               tumble_start(rowtime, interval '10' seconds) AS window_start,
               tumble_end(rowtime, interval '10' seconds) AS window_end,
               impute_udf(
                 observed_timestamp, correlation_id,
                 soc, 0, 100, 'soc',
                 'backward_forward_filled'
               ) AS soc_corrected
             FROM
               bms_status
             GROUP BY
               imei,
               tumble(rowtime, interval '10' seconds)
           ) AS r CROSS
           JOIN UNNEST(
             r.soc_corrected
           ) AS t (`a`)
       ) AS A
   ) AS C
   INNER JOIN bms_status AS D ON C.imei = D.imei
   AND C.corr_id = D.correlation_id
   LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE';
