CREATE TEMPORARY VIEW `bms_status_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  TRY_CAST(D.battery_volt AS FLOAT) AS battery_volt,
  TRY_CAST(D.cell_volt_max AS FLOAT) AS cell_volt_max,
  TRY_CAST(D.cell_volt_min AS FLOAT) AS cell_volt_min,
  D.chg_cycle_count AS chg_cycle_count,
  D.`current` AS `current`,
  D.dsg_cycle_count AS dsg_cycle_count,
  TRY_CAST(D.soc AS FLOAT) AS soc,
  TRY_CAST(D.soh AS FLOAT) AS soh,
  TRY_CAST(D.temperature_max AS FLOAT) AS temperature_max,
  TRY_CAST(D.temperature_min AS FLOAT) AS temperature_min,
  D.remaining_capacity AS remaining_capacity,
  D.mosfet_temperature AS mosfet_temperature
FROM
  bms_status AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE';
