CREATE TEMPORARY VIEW `bms_cell_voltage_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  D.cell_id AS cell_id,
  TRY_CAST(D.volts AS FLOAT) AS volts,
  D.balancing_status AS balancing_status
FROM
  bms_cell_voltages AS D
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime) AND D.cell_id <> -1
  AND V.operation_status = 'ACTIVE';
