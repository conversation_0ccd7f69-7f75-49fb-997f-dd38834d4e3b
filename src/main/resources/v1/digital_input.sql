SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS observed_timestamp,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  D.usr1 AS usr1,
  D.usr2 AS usr2,
  D.motion AS motion,
  D.main_power AS main_power,
  D.ignition AS ignition,
  D.tamper AS tamper
FROM
  digital_inputs AS D
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE';
