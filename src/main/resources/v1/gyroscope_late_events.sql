CREATE TEMPORARY VIEW `gyroscope_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  TRY_CAST(D.x AS FLOAT) AS gyro_x_axis,
  TRY_CAST(D.y AS FLOAT) AS gyro_y_axis,
  TRY_CAST(D.z AS FLOAT) AS gyro_z_axis
FROM
  gyroscope AS D
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE';
