SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS observed_timestamp,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  TRY_CAST(D.latitude AS DOUBLE) AS latitude,
  TRY_CAST(D.longitude AS DOUBLE)AS longitude,
  TRY_CAST(D.altitude AS FLOAT) AS altitude,
  TRY_CAST(C.speed_corrected AS FLOAT)AS speed,
  TRY_CAST(D.bearing AS FLOAT)AS bearing,
  TRY_CAST(D.pdop AS FLOAT)AS pdop,
  TRY_CAST(D.hdop AS FLOAT)AS hdop,
  TRY_CAST(D.vdop AS FLOAT)AS vdop,
  TRY_CAST(D.view_sats AS FLOAT) AS view_sats,
  TRY_CAST(D.track_sats AS FLOAT) AS track_sats
FROM
  (
    SELECT
      A.imei AS imei,
      A.corr_id AS corr_id,
      <PERSON>.speed_corrected AS speed_corrected
    FROM
      (
        SELECT
          r.imei AS imei,
          TRY_CAST(
            SPLIT_INDEX(`a`, ':', 0) AS INT
          ) AS ts,
          TRY_CAST(
            SPLIT_INDEX(`a`, ':', 1) AS STRING
          ) AS corr_id,
          TRY_CAST(
            SPLIT_INDEX(`a`, ':', 2) AS FLOAT
          ) AS speed_corrected,
          1000 * unix_timestamp(
            cast(r.window_start AS string)
          ) AS window_start,
          1000 * unix_timestamp(
            cast(r.window_end AS string)
          ) AS window_end
        FROM
          (
            SELECT
              imei,
              tumble_start(rowtime, interval '10' seconds) AS window_start,
              tumble_end(rowtime, interval '10' seconds) AS window_end,
              impute_udf(
                observed_timestamp, correlation_id,
                speed, 0, 100, 'location_speed',
                'linear_interpolation'
              ) AS speed_corrected
            FROM
              location
            GROUP BY
              imei,
              tumble(rowtime, interval '10' seconds)
          ) AS r CROSS
          JOIN UNNEST(
            r.speed_corrected
          ) AS t (`a`)
      ) AS A
  ) AS C
  INNER JOIN location AS D ON C.imei = D.imei
  AND C.corr_id = D.correlation_id
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE';
