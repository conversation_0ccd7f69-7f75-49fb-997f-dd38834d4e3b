CREATE TEMPORARY VIEW `location_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  TRY_CAST(D.latitude AS DOUBLE) AS latitude,
  TRY_CAST(D.longitude AS DOUBLE) AS longitude,
  TRY_CAST(D.altitude AS FLOAT) AS altitude,
  TRY_CAST(D.speed AS FLOAT) AS speed,
  TRY_CAST(D.bearing AS FLOAT) AS bearing,
  TRY_CAST(D.pdop AS FLOAT) AS pdop,
  TRY_CAST(D.hdop AS FLOAT) AS hdop,
  TRY_CAST(D.vdop AS FLOAT) AS vdop,
  TRY_CAST(D.view_sats AS FLOAT) AS view_sats,
  TRY_CAST(D.track_sats AS FLOAT) AS track_sats
FROM
  location AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE';
