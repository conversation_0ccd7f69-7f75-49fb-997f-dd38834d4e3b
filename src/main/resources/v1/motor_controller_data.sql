SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS observed_timestamp,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  D.dc_voltage AS dc_voltage,
  C.motor_speed_corrected AS motor_speed_corrected,
  C.dc_current_corrected AS dc_current_corrected,
  D.motor_temperature AS motor_temperature,
  D.mcs_temperature AS mcs_temperature
FROM
  (
    SELECT
      imei,
      corr_id,
      motor_speed_corrected,
      dc_current_corrected
    FROM
      (
        SELECT
          A.imei AS imei,
          A.corr_id AS corr_id,
          A.motor_speed_corrected AS motor_speed_corrected,
          B.dc_current_corrected AS dc_current_corrected
        FROM
          (
            SELECT
              r.imei AS imei,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 0) AS INT
              ) AS ts,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 1) AS STRING
              ) AS corr_id,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 2) AS FLOAT
              ) AS motor_speed_corrected,
              1000 * unix_timestamp(
                cast(r.window_start AS string)
              ) AS window_start,
              1000 * unix_timestamp(
                cast(r.window_end AS string)
              ) AS window_end
            FROM
              (
                SELECT
                  imei,
                  tumble_start(rowtime, interval '10' seconds) AS window_start,
                  tumble_end(rowtime, interval '10' seconds) AS window_end,
                  impute_udf(
                    observed_timestamp, correlation_id,
                    motor_speed, 0, 1000, 'motor_speed',
                    'linear_interpolation'
                  ) AS motor_speed_corrected
                FROM
                  motor_controller_data
                GROUP BY
                  imei,
                  tumble(rowtime, interval '10' seconds)
              ) AS r CROSS
              JOIN UNNEST(r.motor_speed_corrected) AS t (`a`)
          ) AS A
          INNER JOIN (
            SELECT
              r.imei AS imei,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 0) AS INT
              ) AS ts,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 1) AS STRING
              ) AS corr_id,
              TRY_CAST(
                SPLIT_INDEX(`a`, ':', 2) AS FLOAT
              ) AS dc_current_corrected,
              1000 * unix_timestamp(
                cast(r.window_start AS string)
              ) AS window_start,
              1000 * unix_timestamp(
                cast(r.window_end AS string)
              ) AS window_end
            FROM
              (
                SELECT
                  imei,
                  tumble_start(rowtime, interval '10' seconds) AS window_start,
                  tumble_end(rowtime, interval '10' seconds) AS window_end,
                  impute_udf(
                    observed_timestamp, correlation_id,
                    dc_current, -25, 100, 'dc_current',
                    'kalman_filter'
                  ) AS dc_current_corrected
                FROM
                  motor_controller_data
                GROUP BY
                  imei,
                  tumble(rowtime, interval '10' seconds)
              ) AS r CROSS
              JOIN UNNEST(r.dc_current_corrected) AS t (`a`)
          ) AS B ON A.imei = B.imei
          AND A.corr_id = B.corr_id
      )
  ) AS C
  INNER JOIN motor_controller_data AS D ON C.imei = D.imei
  AND C.corr_id = D.correlation_id
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE';