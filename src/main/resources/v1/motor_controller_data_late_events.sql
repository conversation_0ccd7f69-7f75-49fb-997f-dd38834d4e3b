CREATE TEMPORARY VIEW `motor_controller_data_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  D.dc_voltage AS dc_voltage,
  TRY_CAST(D.motor_speed AS FLOAT) AS motor_speed,
  TRY_CAST(D.dc_current AS FLOAT) AS dc_current,
  D.motor_temperature AS motor_temperature,
  D.mcs_temperature AS mcs_temperature
FROM
  motor_controller_data AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE';
