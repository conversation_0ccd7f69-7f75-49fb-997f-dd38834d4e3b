SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS observed_timestamp,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.manufacturer_id AS manufacturer_id,
  V.owner_id AS owner_id,
  D.drive_selection AS drive_selection,
  D.regeneration AS regeneration,
  D.ready_sign AS ready_sign,
  D.p_light AS p_light,
  D.reverse AS reverse,
  D.cruise AS cruise,
  D.vehicle_brake AS vehicle_brake,
  D.side_stand AS side_stand,
  C.throttle_percentage_corrected AS throttle_percentage_corrected,
  D.motor_status AS motor_status
FROM
  (
    SELECT
      A.imei AS imei,
      A.corr_id AS corr_id,
      A.throttle_percentage_corrected AS throttle_percentage_corrected
    FROM
      (
        SELECT
          r.imei AS imei,
          TRY_CAST(
            SPLIT_INDEX(`a`, ':', 0) AS INT
          ) AS ts,
          TRY_CAST(
            SPLIT_INDEX(`a`, ':', 1) AS STRING
          ) AS corr_id,
          TRY_CAST(
            SPLIT_INDEX(`a`, ':', 2) AS FLOAT
          ) AS throttle_percentage_corrected,
          1000 * unix_timestamp(
            cast(r.window_start AS string)
          ) AS window_start,
          1000 * unix_timestamp(
            cast(r.window_end AS string)
          ) AS window_end
        FROM
          (
            SELECT
              imei,
              tumble_start(rowtime, interval '10' seconds) AS window_start,
              tumble_end(rowtime, interval '10' seconds) AS window_end,
              impute_udf(
                observed_timestamp, correlation_id,
                throttle_percentage, 0, 100, 'throttle_percentage',
                'linear_interpolation'
              ) AS throttle_percentage_corrected
            FROM
              motor_controller_status
            GROUP BY
              imei,
              tumble(rowtime, interval '10' seconds)
          ) AS r CROSS
          JOIN UNNEST(
            r.throttle_percentage_corrected
          ) AS t (`a`)
      ) AS A
  ) AS C
  INNER JOIN motor_controller_status AS D ON C.imei = D.imei
  AND C.corr_id = D.correlation_id
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE';
