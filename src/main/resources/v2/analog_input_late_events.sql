CREATE TEMPORARY VIEW `analog_input_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CASE WHEN TRY_CAST(D.temp AS FLOAT) >= D.ai_temp_lower_limit
    AND TRY_CAST(D.temp AS FLOAT) <= D.ai_temp_upper_limit THEN TRY_CAST(D.temp AS FLOAT) ELSE NULL END AS ai_temp,
  CASE WHEN TRY_CAST(D.vin AS FLOAT) / 1000 >= D.ai_vin_lower_limit
    AND TRY_CAST(D.vin AS FLOAT) / 1000 <= D.ai_vin_upper_limit THEN TRY_CAST(D.vin AS FLOAT) / 1000 ELSE NULL END AS ai_vin,
  TRY_CAST(D.vsys AS FLOAT) AS ai_vsys,
  TRY_CAST(D.vbuck AS FLOAT) AS ai_vbuck,
  D.vusr_1 AS ai_vusr1,
  D.vusr_2 AS ai_vusr2,
  CAST(NULL AS INTEGER) AS ai_lean_angle
FROM
  analog_inputs AS D
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE'
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));