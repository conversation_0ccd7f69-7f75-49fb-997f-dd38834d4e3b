CREATE TEMPORARY VIEW `bms_cell_temp_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  D.stack_id AS stack_id,
  CASE WHEN TRY_CAST(D.temperature AS FLOAT) >= D.battery_cell_temperature_lower_limit
      AND TRY_CAST(D.temperature AS FLOAT) <= D.battery_cell_temperature_upper_limit THEN TRY_CAST(D.temperature AS FLOAT) ELSE NULL END AS temperature
FROM
  bms_cell_temp AS D
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE'
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));