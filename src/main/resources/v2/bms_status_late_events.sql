CREATE TEMPORARY VIEW `bms_status_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CASE WHEN TRY_CAST(D.battery_volt AS FLOAT) >= D.battery_volt_lower_limit
      AND TRY_CAST(D.battery_volt AS FLOAT) <= D.battery_volt_upper_limit THEN TRY_CAST(D.battery_volt AS FLOAT)
       ELSE NULL END AS battery_volt,
  TRY_CAST(D.cell_volt_max AS FLOAT) AS cell_volt_max,
  TRY_CAST(D.cell_volt_min AS FLOAT) AS cell_volt_min,
  D.chg_cycle_count AS chg_cycle_count,
  CASE WHEN TRY_CAST(D.`current` AS FLOAT) >= D.battery_current_lower_limit AND TRY_CAST(D.`current` AS FLOAT) <= D.battery_current_upper_limit THEN TRY_CAST(D.`current` AS FLOAT) ELSE NULL END  AS `current`,
  D.dsg_cycle_count AS dsg_cycle_count,
  CASE WHEN TRY_CAST(D.soc AS FLOAT) >= D.battery_soc_lower_limit
      AND TRY_CAST(D.soc AS FLOAT) <= D.battery_soc_upper_limit THEN TRY_CAST(D.soc AS FLOAT) ELSE NULL END AS soc,
  TRY_CAST(D.soh AS FLOAT) AS soh,
  TRY_CAST(D.temperature_max AS FLOAT) AS temperature_max,
  TRY_CAST(D.temperature_min AS FLOAT) AS temperature_min,
  D.remaining_capacity AS remaining_capacity,
  CASE WHEN TRY_CAST(D.mosfet_temperature AS FLOAT) >= D.battery_mosfet_temperature_lower_limit
      AND TRY_CAST(D.mosfet_temperature AS FLOAT) <= D.battery_mosfet_temperature_upper_limit THEN TRY_CAST(D.mosfet_temperature AS FLOAT) ELSE NULL END AS mosfet_temperature,
  CAST(NULL AS FLOAT) AS discharge
FROM
  bms_status AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE'
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));