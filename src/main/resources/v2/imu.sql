SELECT
  D.event_time AS event_time,
  D.`timestamp` AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  <PERSON><PERSON>imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CASE WHEN TRY_CAST(D.ai_temp AS FLOAT) >= ai_temp_lower_limit
    AND TRY_CAST(D.ai_temp AS FLOAT) <= ai_temp_upper_limit THEN TRY_CAST(D.ai_temp AS FLOAT) ELSE NULL END AS ai_temp,
  CASE WHEN TRY_CAST(D.ai_vin AS FLOAT) / 1000 >= ai_vin_lower_limit
    AND TRY_CAST(D.ai_vin AS FLOAT) / 1000 <= ai_vin_upper_limit THEN TRY_CAST(D.ai_vin AS FLOAT) / 1000 ELSE NULL END AS ai_vin,
  TRY_CAST(D.ai_vsys AS FLOAT) AS ai_vsys,
  TRY_CAST(D.ai_vbuck AS FLOAT) AS ai_vbuck,
  D.ai_vusr1 AS ai_vusr1,
  D.ai_vusr2 AS ai_vusr2,
  D.ai_lean_angle AS ai_lean_angle,
  D.di_usr1 AS di_usr1,
  D.di_usr2 AS di_usr2,
  D.di_motion AS di_motion,
  D.di_main_power AS di_main_power,
  D.di_ignition AS di_ignition,
  D.di_tamper AS di_tamper,
  D.do_usr1 AS do_usr1,
  D.do_usr2 AS do_usr2,
  TRY_CAST(D.accel_x AS FLOAT) AS accel_x,
  TRY_CAST(D.accel_y AS FLOAT) AS accel_y,
  TRY_CAST(D.accel_z AS FLOAT) AS accel_z,
  TRY_CAST(D.gyro_x AS FLOAT) AS gyro_x,
  TRY_CAST(D.gyro_y AS FLOAT) AS gyro_y,
  TRY_CAST(D.gyro_z AS FLOAT) AS gyro_z,
  TRY_CAST(D.grv_x AS FLOAT) AS grv_x,
  TRY_CAST(D.grv_y AS FLOAT) AS grv_y,
  TRY_CAST(D.grv_z AS FLOAT) AS grv_z
FROM
  imu AS D
  INNER JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE'
  AND D.`timestamp` BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));