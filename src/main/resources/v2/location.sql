SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS observed_timestamp,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CASE WHEN TRY_CAST(D.latitude AS DOUBLE) >= D.latitude_lower_limit
      AND TRY_CAST(D.latitude AS DOUBLE) <= D.latitude_upper_limit THEN TRY_CAST(D.latitude AS DOUBLE) ELSE NULL END AS latitude,
  CASE WHEN TRY_CAST(D.longitude AS DOUBLE) >= D.longitude_lower_limit
      AND TRY_CAST(D.longitude AS DOUBLE) <= D.longitude_upper_limit THEN TRY_CAST(D.longitude AS DOUBLE) ELSE NULL END AS longitude,
  CASE WHEN TRY_CAST(D.altitude AS FLOAT) >= D.altitude_lower_limit
      AND TRY_CAST(D.altitude AS FLOAT) < D.altitude_upper_limit THEN TRY_CAST(D.altitude AS FLOAT) ELSE NULL END AS altitude,
  CASE WHEN TRY_CAST(D.speed AS FLOAT) >= D.speed_lower_limit
      AND TRY_CAST(D.speed AS FLOAT) <= D.speed_upper_limit THEN TRY_CAST(D.speed AS FLOAT) ELSE NULL END AS speed,
  TRY_CAST(D.bearing AS FLOAT)AS bearing,
  TRY_CAST(D.pdop AS FLOAT)AS pdop,
  TRY_CAST(D.hdop AS FLOAT)AS hdop,
  TRY_CAST(D.vdop AS FLOAT)AS vdop,
  TRY_CAST(D.view_sats AS FLOAT) AS view_sats,
  TRY_CAST(D.track_sats AS FLOAT) AS track_sats,
  TRY_CAST(D.gps_distance AS DOUBLE) AS gps_distance
FROM
  location AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE'
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));