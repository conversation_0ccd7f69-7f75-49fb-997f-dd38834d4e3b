CREATE TEMPORARY VIEW `motor_controller_data_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CASE WHEN D.dc_voltage >= D.motor_dc_voltage_lower_limit AND D.dc_voltage <= D.motor_dc_voltage_upper_limit THEN D.dc_voltage ELSE NULL END AS dc_voltage,
  CASE WHEN TRY_CAST(D.motor_speed AS FLOAT) >= D.motor_speed_lower_limit AND TRY_CAST(D.motor_speed AS FLOAT) <= D.motor_speed_upper_limit THEN TRY_CAST(D.motor_speed AS FLOAT) ELSE NULL END AS motor_speed,
  CASE WHEN D.dc_current >= D.motor_dc_current_lower_limit AND D.dc_current <= D.motor_dc_current_upper_limit THEN D.dc_current ELSE NULL END AS dc_current,
  CASE WHEN D.motor_temperature >= D.motor_temperature_lower_limit
    AND D.motor_temperature <= D.motor_temperature_upper_limit THEN D.motor_temperature ELSE NULL END AS motor_temperature,
  CASE WHEN D.mcs_temperature >= D.motor_mcs_temperature_lower_limit
      AND D.mcs_temperature <= D.motor_mcs_temperature_upper_limit THEN D.mcs_temperature ELSE NULL END AS mcs_temperature
FROM
  motor_controller_data AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE'
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));