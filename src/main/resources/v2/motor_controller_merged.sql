SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS observed_timestamp,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  CASE WHEN D.dc_voltage >= D.motor_dc_voltage_lower_limit AND D.dc_voltage <= D.motor_dc_voltage_upper_limit THEN D.dc_voltage ELSE NULL END AS dc_voltage,
  CASE WHEN TRY_CAST(D.motor_speed AS FLOAT) >= D.motor_speed_lower_limit AND TRY_CAST(D.motor_speed AS FLOAT) <= D.motor_speed_upper_limit THEN TRY_CAST(D.motor_speed AS FLOAT) ELSE NULL END AS motor_speed_corrected,
  CASE WHEN D.dc_current >= D.motor_dc_current_lower_limit AND D.dc_current <= D.motor_dc_current_upper_limit THEN D.dc_current ELSE NULL END AS dc_current_corrected,
  CASE WHEN D.motor_temperature >= D.motor_temperature_lower_limit
    AND D.motor_temperature <= D.motor_temperature_upper_limit THEN D.motor_temperature ELSE NULL END AS motor_temperature,
  CASE WHEN D.mcs_temperature >= D.motor_mcs_temperature_lower_limit
        AND D.mcs_temperature <= D.motor_mcs_temperature_upper_limit THEN D.mcs_temperature ELSE NULL END AS mcs_temperature,
  D.drive_selection AS drive_selection,
  D.regeneration AS regeneration,
  D.ready_sign AS ready_sign,
  D.p_light AS p_light,
  D.reverse AS reverse,
  D.cruise AS cruise,
  D.vehicle_brake AS vehicle_brake,
  D.side_stand AS side_stand,
  CASE WHEN TRY_CAST(D.throttle_percentage AS FLOAT) >= D.motor_throttle_percentage_lower_limit
    AND TRY_CAST(D.throttle_percentage AS FLOAT) <= D.motor_throttle_percentage_upper_limit THEN TRY_CAST(D.throttle_percentage AS FLOAT) ELSE NULL END AS throttle_percentage_corrected,
  D.motor_id AS motor_id,
  D.di_motion AS di_motion,
  D.di_ignition AS di_ignition,
  D.motor_speed_kmph AS motor_speed_kmph,
  D.motor_distance AS motor_distance
FROM
  motor_controller_merged AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  V.operation_status = 'ACTIVE'
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND))
  AND D.motor_id is not null;