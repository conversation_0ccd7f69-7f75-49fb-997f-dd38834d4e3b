CREATE TEMPORARY VIEW `motor_controller_status_late` AS
SELECT
  D.event_time AS event_time,
  D.observed_timestamp AS `timestamp`,
  D.ingestion_timestamp AS ingestion_timestamp,
  D.correlation_id AS correlation_id,
  D.imei AS imei,
  V.id AS vehicle_id,
  V.mfr_org_id AS mfr_org_id,
  V.owner_org_id AS owner_org_id,
  D.drive_selection AS drive_selection,
  D.regeneration AS regeneration,
  D.ready_sign AS ready_sign,
  D.p_light AS p_light,
  D.reverse AS reverse,
  D.cruise AS cruise,
  D.vehicle_brake AS vehicle_brake,
  D.side_stand AS side_stand,
  CASE WHEN TRY_CAST(D.throttle_percentage AS FLOAT) >= D.motor_throttle_percentage_lower_limit
      AND TRY_CAST(D.throttle_percentage AS FLOAT) <= D.motor_throttle_percentage_upper_limit THEN TRY_CAST(D.throttle_percentage AS FLOAT) ELSE NULL END AS throttle_percentage
FROM
  motor_controller_status AS D
  LEFT JOIN vehicle FOR SYSTEM_TIME AS OF D.proc_time AS V ON TRY_CAST(D.imei AS STRING) = V.imei
WHERE
  CURRENT_WATERMARK(rowtime) IS NOT NULL
  AND rowtime <= CURRENT_WATERMARK(rowtime)
  AND V.operation_status = 'ACTIVE'
  AND D.observed_timestamp BETWEEN EXTRACT(EPOCH FROM (NOW() - INTERVAL '5' DAY)) AND EXTRACT(EPOCH FROM (NOW() + INTERVAL '5' SECOND));