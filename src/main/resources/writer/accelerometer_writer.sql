INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on,
  packet_received_on, mfr_org_id,
  owner_org_id, vehicle_id,
  accel_x_axis, accel_y_axis, accel_z_axis
)
VALUES
  (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
  ) ON CONFLICT (imei, timestamp) DO
UPDATE
SET
  accel_x_axis = COALESCE(
    excluded.accel_x_axis, %s.accel_x_axis
  ),
  accel_y_axis = COALESCE(
    excluded.accel_y_axis, %s.accel_y_axis
  ),
  accel_z_axis = COALESCE(
    excluded.accel_z_axis, %s.accel_z_axis
  );
