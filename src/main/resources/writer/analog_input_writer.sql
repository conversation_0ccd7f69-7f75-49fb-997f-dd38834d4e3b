INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on,
  packet_received_on, mfr_org_id,
  owner_org_id, vehicle_id,
  ai_lean_angle, ai_system_voltage,
  ai_temperature, ai_vbuck, ai_voltage_input,
  ai_vusr1, ai_vusr2
)
VALUES
  (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
  ) ON CONFLICT (imei, timestamp) DO
UPDATE
SET
  ai_lean_angle = COALESCE(
    excluded.ai_lean_angle, %s.ai_lean_angle
  ),
  ai_system_voltage = COALESCE(
    excluded.ai_system_voltage, %s.ai_system_voltage
  ),
  ai_temperature = COALESCE(
    excluded.ai_temperature, %s.ai_temperature
  ),
  ai_vbuck = COALESCE(
    excluded.ai_vbuck, %s.ai_vbuck
  ),
  ai_voltage_input = COALESCE(
    excluded.ai_voltage_input, %s.ai_voltage_input
  ),
  ai_vusr1 = COALESCE(
    excluded.ai_vusr1, %s.ai_vusr1
  ),
  ai_vusr2 = COALESCE(
    excluded.ai_vusr2, %s.ai_vusr2
  );
