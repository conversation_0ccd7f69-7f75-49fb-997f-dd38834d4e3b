INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on,
  packet_received_on, mfr_org_id,
  owner_org_id, vehicle_id, temperature_max,
  temperature_min
)
VALUES
  (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT (imei, timestamp) DO
UPDATE
SET
  temperature_max = COALESCE(
    excluded.temperature_max, %s.temperature_max
  ),
  temperature_min = COALESCE(
    excluded.temperature_min, %s.temperature_min
  );
