INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on, 
  packet_received_on, mfr_org_id, 
  owner_org_id, vehicle_id, cell_volt_max, 
  cell_volt_min
) 
VALUES 
  (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT (imei, timestamp) DO 
UPDATE 
SET 
  cell_volt_max = COALESCE(
    excluded.cell_volt_max, %s.cell_volt_max
  ), 
  cell_volt_min = COALESCE(
    excluded.cell_volt_min, %s.cell_volt_min
  );
