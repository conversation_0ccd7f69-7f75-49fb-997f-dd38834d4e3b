INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on, 
  packet_received_on, mfr_org_id, 
  owner_org_id, vehicle_id, battery_volt, 
  chg_cycle_count,
  current, dsg_cycle_count, soc, soh,
  remaining_capacity,
  mosfet_temperature, discharge
) 
VALUES 
  (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
  ) ON CONFLICT (imei, timestamp) DO 
UPDATE 
SET
  battery_volt = COALESCE(
    excluded.battery_volt, %s.battery_volt
  ),
  current = COALESCE(
    excluded.current, %s.current
  ), 
  chg_cycle_count = COALESCE(
    excluded.chg_cycle_count, %s.chg_cycle_count
  ), 
  dsg_cycle_count = COALESCE(
    excluded.dsg_cycle_count, %s.dsg_cycle_count
  ), 
  soc = COALESCE(
    excluded.soc, %s.soc
  ), 
  soh = COALESCE(
    excluded.soh, %s.soh
  ),
  remaining_capacity = COALESCE(
    excluded.remaining_capacity, %s.remaining_capacity
  ),
  mosfet_temperature = COALESCE(
    excluded.mosfet_temperature, %s.mosfet_temperature
  );
