INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on, 
  packet_received_on, mfr_org_id, 
  owner_org_id, vehicle_id,
  di_ignition,
  di_main_power, di_motion, di_tamper, 
  di_usr1, di_usr2
) 
VALUES 
  (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
  ) ON CONFLICT (imei, timestamp) DO 
UPDATE 
SET
  di_ignition = COALESCE(
    excluded.di_ignition, %s.di_ignition
  ), 
  di_main_power = COALESCE(
    excluded.di_main_power, %s.di_main_power
  ), 
  di_motion = COALESCE(
    excluded.di_motion, %s.di_motion
  ), 
  di_tamper = COALESCE(
    excluded.di_tamper, %s.di_tamper
  ), 
  di_usr1 = COALESCE(
    excluded.di_usr1, %s.di_usr1
  ), 
  di_usr2 = COALESCE(
    excluded.di_usr2, %s.di_usr2
  );
