INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on, 
  packet_received_on, mfr_org_id, 
  owner_org_id, vehicle_id,
  grv_x_axis, grv_y_axis, grv_z_axis,
  ai_lean_angle
) 
VALUES 
  (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
  ) ON CONFLICT (imei, timestamp) DO 
UPDATE 
SET
  grv_x_axis = COALESCE(
    excluded.grv_x_axis, %s.grv_x_axis
  ),
  grv_y_axis = COALESCE(
    excluded.grv_y_axis, %s.grv_y_axis
  ),
  grv_z_axis = COALESCE(
    excluded.grv_z_axis, %s.grv_z_axis
  ),
  ai_lean_angle = COALESCE(
    excluded.ai_lean_angle, %s.ai_lean_angle
  );
