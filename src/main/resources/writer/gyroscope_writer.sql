INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on, 
  packet_received_on, mfr_org_id, 
  owner_org_id, vehicle_id,
  gyro_x_axis, gyro_y_axis, gyro_z_axis
) 
VALUES 
  (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
  ) ON CONFLICT (imei, timestamp) DO 
UPDATE 
SET
  gyro_x_axis = COALESCE(
    excluded.gyro_x_axis, %s.gyro_x_axis
  ), 
  gyro_y_axis = COALESCE(
    excluded.gyro_y_axis, %s.gyro_y_axis
  ), 
  gyro_z_axis = COALESCE(
    excluded.gyro_z_axis, %s.gyro_z_axis
  );
