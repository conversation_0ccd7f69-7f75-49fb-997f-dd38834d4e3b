INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on,
  packet_received_on, mfr_org_id,
  owner_org_id, vehicle_id,
  accel_x_axis, accel_y_axis, accel_z_axis,
  di_ignition, di_motion,
  gyro_x_axis, gyro_y_axis, gyro_z_axis,
  grv_x_axis, grv_y_axis, grv_z_axis,
  ai_lean_angle
)
VALUES
  (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
    ?, ?, ?, ?
  ) ON CONFLICT (imei, timestamp) DO
UPDATE
SET
  ai_lean_angle = COALESCE(
    excluded.ai_lean_angle, %s.ai_lean_angle
  ),
  accel_x_axis = COALESCE(
    excluded.accel_x_axis, %s.accel_x_axis
  ),
  accel_y_axis = COALESCE(
    excluded.accel_y_axis, %s.accel_y_axis
  ),
  accel_z_axis = COALESCE(
    excluded.accel_z_axis, %s.accel_z_axis
  ),
  di_ignition = COALESCE(
    excluded.di_ignition, %s.di_ignition
  ),
  di_motion = COALESCE(
    excluded.di_motion, %s.di_motion
  ),
  gyro_x_axis = COALESCE(
    excluded.gyro_x_axis, %s.gyro_x_axis
  ),
  gyro_y_axis = COALESCE(
    excluded.gyro_y_axis, %s.gyro_y_axis
  ),
  gyro_z_axis = COALESCE(
    excluded.gyro_z_axis, %s.gyro_z_axis
  ),
  grv_x_axis = COALESCE(
    excluded.grv_x_axis, %s.grv_x_axis
  ),
  grv_y_axis = COALESCE(
    excluded.grv_y_axis, %s.grv_y_axis
  ),
  grv_z_axis = COALESCE(
    excluded.grv_z_axis, %s.grv_z_axis
  );