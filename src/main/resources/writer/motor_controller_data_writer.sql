INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on, 
  packet_received_on, mfr_org_id, 
  owner_org_id, vehicle_id,
  motor_dc_current, motor_dc_voltage, 
  motor_mcs_temperature, motor_speed, 
  motor_temperature
) 
VALUES 
  (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
  ) ON CONFLICT (imei, timestamp) DO 
UPDATE 
SET
  motor_dc_current = COALESCE(
    excluded.motor_dc_current, %s.motor_dc_current
  ), 
  motor_dc_voltage = COALESCE(
    excluded.motor_dc_voltage, %s.motor_dc_voltage
  ), 
  motor_mcs_temperature = COALESCE(
    excluded.motor_mcs_temperature, 
    %s.motor_mcs_temperature
  ), 
  motor_speed = COALESCE(
    excluded.motor_speed, %s.motor_speed
  ), 
  motor_temperature = COALESCE(
    excluded.motor_temperature, %s.motor_temperature
  );
