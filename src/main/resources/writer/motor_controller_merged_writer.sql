INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on, 
  packet_received_on, mfr_org_id, 
  owner_org_id, vehicle_id,
  motor_dc_current, motor_dc_voltage, 
  motor_mcs_temperature, motor_speed, 
  motor_temperature,motor_brake,
  motor_cruise, motor_driving_mode,
  motor_parking_sign,
  motor_ready_sign, motor_regeneration,
  motor_reverse, motor_side_stand,
  motor_throttle, motor_id,
  di_motion, di_ignition,
  motor_speed_kmph, motor_distance
) 
VALUES 
  (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
    ?
  ) ON CONFLICT (imei, timestamp, motor_id) DO
UPDATE 
SET
  motor_dc_current = COALESCE(
    excluded.motor_dc_current, %s.motor_dc_current
  ), 
  motor_dc_voltage = COALESCE(
    excluded.motor_dc_voltage, %s.motor_dc_voltage
  ), 
  motor_mcs_temperature = COALESCE(
    excluded.motor_mcs_temperature, 
    %s.motor_mcs_temperature
  ), 
  motor_speed = COALESCE(
    excluded.motor_speed, %s.motor_speed
  ), 
  motor_temperature = COALESCE(
    excluded.motor_temperature, %s.motor_temperature
  ),
  motor_brake = COALESCE(
      excluded.motor_brake, %s.motor_brake
    ),
    motor_cruise = COALESCE(
      excluded.motor_cruise, %s.motor_cruise
    ),
    motor_driving_mode = COALESCE(
      excluded.motor_driving_mode, %s.motor_driving_mode
    ),
    motor_parking_sign = COALESCE(
      excluded.motor_parking_sign, %s.motor_parking_sign
    ),
    motor_ready_sign = COALESCE(
      excluded.motor_ready_sign, %s.motor_ready_sign
    ),
    motor_regeneration = COALESCE(
      excluded.motor_regeneration, %s.motor_regeneration
    ),
    motor_reverse = COALESCE(
      excluded.motor_reverse, %s.motor_reverse
    ),
    motor_side_stand = COALESCE(
      excluded.motor_side_stand, %s.motor_side_stand
    ),
    motor_throttle = COALESCE(
      excluded.motor_throttle, %s.motor_throttle
    ),
    motor_id = COALESCE(
      excluded.motor_id, %s.motor_id
    ),
    di_motion = COALESCE(
      excluded.di_motion, %s.di_motion
    ),
    di_ignition = COALESCE(
      excluded.di_ignition, %s.di_ignition
    ),
    motor_speed_kmph = COALESCE(
        excluded.motor_speed_kmph, %s.motor_speed_kmph
    ),
    motor_distance = COALESCE(
        excluded.motor_distance, %s.motor_distance
    );
