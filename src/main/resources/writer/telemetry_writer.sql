INSERT INTO %s (
  imei, timestamp, co_relation_id, created_on,
  packet_received_on, mfr_org_id,
  owner_org_id, vehicle_id,
  ai_system_voltage,
  ai_temperature, ai_vbuck, ai_voltage_input,
  ai_vusr1, ai_vusr2, di_ignition,
  di_main_power, di_motion, di_tamper,
  di_usr1, di_usr2, do_usr1, do_usr2
)
VALUES
  (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?, ?
  ) ON CONFLICT (imei, timestamp) DO
UPDATE
SET
  ai_system_voltage = COALESCE(
    excluded.ai_system_voltage, %s.ai_system_voltage
  ),
  ai_temperature = COALESCE(
    excluded.ai_temperature, %s.ai_temperature
  ),
  ai_vbuck = COALESCE(
    excluded.ai_vbuck, %s.ai_vbuck
  ),
  ai_voltage_input = COALESCE(
    excluded.ai_voltage_input, %s.ai_voltage_input
  ),
  ai_vusr1 = COALESCE(
    excluded.ai_vusr1, %s.ai_vusr1
  ),
  ai_vusr2 = COALESCE(
    excluded.ai_vusr2, %s.ai_vusr2
  ),
  di_ignition = COALESCE(
    excluded.di_ignition, %s.di_ignition
  ),
  di_main_power = COALESCE(
    excluded.di_main_power, %s.di_main_power
  ),
  di_motion = COALESCE(
    excluded.di_motion, %s.di_motion
  ),
  di_tamper = COALESCE(
    excluded.di_tamper, %s.di_tamper
  ),
  di_usr1 = COALESCE(
    excluded.di_usr1, %s.di_usr1
  ),
  di_usr2 = COALESCE(
    excluded.di_usr2, %s.di_usr2
  ),
  do_usr1 = COALESCE(
    excluded.do_usr1, %s.do_usr1
  ),
  do_usr2 = COALESCE(
    excluded.do_usr2, %s.do_usr2
  );