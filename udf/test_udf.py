import pandas as pd
import numpy as np

from v1.udf import _impute

# measurements = pd.Series([0,1,2,3,4,5,6,7,8,9])
# measurements = pd.Series([np.nan,1,2,3,np.nan,5,6,7,np.nan,np.nan])
# measurements = pd.Series([0,1,np.nan,3,np.nan,5,6,7,np.nan,9])
# measurements = pd.Series([50,np.nan,90])
# measurements = pd.Series([1,2,np.nan,4,5,6])
measurements = pd.Series([1,2,np.nan,4,5,np.nan])

min_val = 0
max_val = 10
min_samples = 5

output_li = _impute(measurements, min_val, max_val, min_samples, method='linear_interpolation')
print(output_li)
output_kf = _impute(measurements, min_val, max_val, min_samples, method='kalman_filter')
print(output_kf)
output_bf = _impute(measurements, min_val, max_val, min_samples, method='backward_forward_filled')
print(output_bf)