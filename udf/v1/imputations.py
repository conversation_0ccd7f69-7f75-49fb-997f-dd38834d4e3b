# imputations.py

import logging
import typing

import pandas as pd
import numpy as np

from hamilton.function_modifiers import config

from kalman_filter import get_kalman_filtered_values

logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)


def between_range(measurements: pd.Series, min_val: typing.Any, max_val: typing.Any) -> pd.Series:
    """
    Replace out of range values with null.
    """
    log.info("Replacing extreme values with nulls")
    measurements.loc[(measurements < min_val) | (measurements > max_val)] = np.nan
    return measurements


def limits(between_range: pd.Series, min_samples: typing.Any) -> tuple:
    """
    Calculates the upper and lower limits of the valid values.
    """
    if between_range.shape[0] <= min_samples:
        log.info("Samples less than min_samples")
        return np.nan, np.nan
    iqr_multiplier = 1.5
    q1: float = between_range.quantile(0.25)
    q3: float = between_range.quantile(0.75)
    iqr: float = q3 - q1
    log.info(f"q1: {q1}, q3: {q3}")
    upper_limit: float = q3 + iqr * iqr_multiplier
    lower_limit: float = q1 - iqr * iqr_multiplier
    return lower_limit, upper_limit


def outliers_replaced_with_nan(measurements: pd.Series, limits: tuple) -> pd.Series:
    """
    Replaces outliers with nulls.
    """
    if np.isnan(limits).all():
        log.info("Skipping outlier detection")
        return measurements
    log.info("Replacing outliers with nulls")
    lower_limit, upper_limit = limits
    log.info(f"upper_limit: {upper_limit}, lower_limit: {lower_limit}")
    measurements.loc[(measurements > upper_limit) | (measurements < lower_limit)] = np.nan
    return measurements


def backward_forward_filled(outliers_replaced_with_nan: pd.Series, min_samples: typing.Any) -> pd.Series:
    """
    Function to perform intermediate imputation.
    """

    if not outliers_replaced_with_nan.isna().sum() > 0 or outliers_replaced_with_nan.count() < min_samples:
        log.info(f"Outliers replacement skipped due to no missing data")
        return outliers_replaced_with_nan

    log.info("Intermediate imputation with forward_backward fill.")

    first_valid_index: typing.Hashable = outliers_replaced_with_nan.first_valid_index()
    last_valid_index: typing.Hashable = outliers_replaced_with_nan.last_valid_index()
    log.info(f"first_valid_index: {first_valid_index}, last_valid_index: {last_valid_index}")

    log.info("backward filling initial nulls")
    outliers_replaced_with_nan_copy = outliers_replaced_with_nan.copy()

    outliers_replaced_with_nan_copy[:first_valid_index + 1].fillna(method="bfill", inplace=True)

    log.info("Forward filling final nulls")
    outliers_replaced_with_nan_copy[last_valid_index:].fillna(method="ffill", inplace=True)

    return outliers_replaced_with_nan_copy


def outliers_replaced(backward_forward_filled: pd.Series) -> pd.Series:
    """
    Function replaces outliers.
    """
    log.info("Replacing outliers if any.")
    return backward_forward_filled


@config.when(method='kalman_filter')
def impute_values__kalman_filter(outliers_replaced: pd.Series, min_samples: typing.Any) -> pd.Series:
    """
    Performs a kalman filter based imputation of nan values.
    """
    if outliers_replaced.isna().sum() > 0 and outliers_replaced.count() >= min_samples:
        log.info("Performing kalman filter based imputation.")
        log.info("Nulls observed, imputing.")
        kalman_applied: pd.Series = get_kalman_filtered_values(np.ma.masked_invalid(outliers_replaced))
        outliers_replaced.fillna(kalman_applied, inplace=True)
    return outliers_replaced.round(3)


@config.when(method='linear_interpolation')
def impute_values__linear_interpolation(outliers_replaced: pd.Series, min_samples: typing.Any) -> pd.Series:
    """
    Performs a linear interpolation based imputation.
    """
    if outliers_replaced.isna().sum() > 0 and outliers_replaced.count() >= min_samples:
        log.info("Performing linear Interpolation based imputation.")
        log.info("Nulls observed, imputing.")
        imputed = outliers_replaced.interpolate(method='linear')
        outliers_replaced.fillna(imputed, inplace=True)
    return outliers_replaced.round(3)


@config.when(method='backward_forward_filled')
def impute_values__backward_forward_filled(outliers_replaced: pd.Series, min_samples: typing.Any) -> pd.Series:
    """
    Performs backward forward filling imputation on nan values in the measurement series.
    """
    if outliers_replaced.isna().sum() > 0 and outliers_replaced.count() >= min_samples:
        log.info("Performing backward forward filling based imputation.")
        log.info("Nulls observed, imputing.")
        imputed = outliers_replaced.fillna(method='bfill')
        outliers_replaced.fillna(imputed, inplace=True)
    return outliers_replaced.round(3)
