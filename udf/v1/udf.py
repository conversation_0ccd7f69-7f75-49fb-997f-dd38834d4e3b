import logging
import typing

import pandas as pd
from hamilton import telemetry

# Disable hamilton usage data collection.
telemetry.disable_telemetry()

from hamilton import driver
from pyflink.table.udf import udaf, udf
from pyflink.table import DataTypes

# Hamilton functions
import imputations

logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)


def _impute(measurements: pd.Series, min_val: pd.Series, max_val: pd.Series, method: pd.Series) -> pd.Series:
    columns = {
        "measurements": measurements.copy()
    }
    config = {

        'min_val': min_val[0],
        'max_val': max_val[0],
        'method': method[0],
        'min_samples': 5
    }
    dr = driver.Driver(config, imputations)
    output_columns = [
        'impute_values'
    ]
    df: pd.DataFrame = dr.execute(output_columns, inputs=columns)
    df.fillna(0, inplace=True)

    log.info(f"Result dataframe description: {df.describe().to_dict()}")

    return df['impute_values']


# =======================
# Deprecated, don't use.
# =======================
@udaf(result_type=DataTypes.ARRAY(DataTypes.FLOAT()), func_type="pandas")
def impute_udaf(ts, correlation_id, measurements, min_val, max_val):
    log.info("Executing impute udaf.")

    log.info(f"Timestamps: {ts.tolist()}")
    log.info(f"CorrelationIds: {correlation_id.tolist()}")
    log.info(f"Measurements description: {measurements.describe().to_dict()}")
    log.info(f"Measurement values: {measurements.tolist()}")

    try:
        return _impute(measurements, min_val, max_val, "kalman_filter")
    except Exception as e:
        log.error(f"Exception caught while executing _impute: {e}")

    return measurements.tolist()


@udaf(result_type=DataTypes.ARRAY(DataTypes.STRING()), func_type="pandas")
def impute_udf(ts: pd.Series, correlation_id: pd.Series,
               measurements: pd.Series, min_val: pd.Series,
               max_val: pd.Series, identifier: pd.Series, method: pd.Series):
    log.info("Executing impute udf.")

    log.info(f"Timestamps: {ts.tolist()}")
    log.info(f"CorrelationIds: {correlation_id.tolist()}")
    log.info(f"Measurements description: {measurements.describe().to_dict()}")
    log.info(f"Measurement values: {measurements.tolist()}")
    log.info(f"min_val: {min_val}, max_val: {max_val}, identifier: {identifier}, method: {method}")

    try:
        measurements = _impute(measurements, min_val, max_val, method)
    except Exception as e:
        log.error(f"Exception caught while executing 'impute_udf': {e}")

    # Returns a series.
    result = [":".join(map(str, element)) for element in zip(ts, correlation_id.tolist(), measurements.tolist())]
    return result
